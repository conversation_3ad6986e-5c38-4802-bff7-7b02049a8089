#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من تقرير المقارنة التفصيلية لاختبار المشكلة
"""

from flask import Flask, render_template, request, redirect, url_for, flash
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'test-key'

def get_db_connection():
    if not os.path.exists('instance'):
        os.makedirs('instance')
    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/test_detailed_comparison')
def test_detailed_comparison():
    """نسخة مبسطة من تقرير المقارنة التفصيلية"""
    conn = get_db_connection()
    try:
        print("=== اختبار تقرير المقارنة التفصيلية ===")
        
        # معاملات ثابتة للاختبار
        comparison_type = 'branches'
        selected_locations = ['1', '2']  # أول فرعين
        date_filter = request.args.get('date_filter', 'month')
        start_month = request.args.get('start_month')
        end_month = request.args.get('end_month')
        
        print(f"نوع المقارنة: {comparison_type}")
        print(f"فلتر التاريخ: {date_filter}")
        
        # بناء فلتر التاريخ
        if date_filter == 'custom' and start_month and end_month:
            date_filter_sql = "AND strftime('%Y-%m', d.dispense_month) BETWEEN ? AND ?"
            date_params = [start_month, end_month]
            period_desc = f"من {start_month} إلى {end_month}"
            print(f"فترة مخصصة: {period_desc}")
        else:
            # الشهر الحالي (افتراضي)
            current_month = datetime.now().strftime('%Y-%m')
            date_filter_sql = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            period_desc = f"الشهر الحالي ({current_month})"
            print(f"الشهر الحالي: {period_desc}")
        
        # استعلام بسيط للأدوية
        query = '''
            SELECT
                b.name as location_name,
                dr.name as drug_name,
                'دواء' as item_type,
                COUNT(d.id) as cases_count,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.quantity * dd.price) as total_cost,
                AVG(dd.price) as avg_price
            FROM dispensed d
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            JOIN drugs dr ON d.drug_id = dr.id
            WHERE b.id IN (?, ?)
            ''' + date_filter_sql + '''
            GROUP BY b.id, b.name, dr.id, dr.name
            ORDER BY b.name, total_cost DESC
            LIMIT 10
        '''
        
        params = selected_locations + date_params
        print(f"الاستعلام: {query}")
        print(f"المعاملات: {params}")
        
        # تنفيذ الاستعلام
        results = conn.execute(query, params).fetchall()
        print(f"عدد النتائج: {len(results)}")
        
        # معالجة النتائج
        comparison_data = []
        for row in results:
            print(f"معالجة صف: {dict(row)}")
            item = {
                'location_name': row['location_name'],
                'drug_name': row['drug_name'],
                'item_type': row['item_type'],
                'cases_count': row['cases_count'] or 0,
                'total_quantity': row['total_quantity'] or 0,
                'total_cost': row['total_cost'] or 0,
                'avg_price': row['avg_price'] or 0
            }
            comparison_data.append(item)
        
        # تنظيم البيانات
        locations_data = {}
        for item in comparison_data:
            location = item['location_name']
            if location not in locations_data:
                locations_data[location] = {
                    'name': location,
                    'drugs': [],
                    'total_cases': 0,
                    'total_quantity': 0,
                    'total_cost': 0
                }
            
            locations_data[location]['drugs'].append({
                'name': item['drug_name'],
                'type': item['item_type'],
                'cases_count': item['cases_count'],
                'total_quantity': item['total_quantity'],
                'total_cost': item['total_cost'],
                'avg_price': item['avg_price']
            })
            
            locations_data[location]['total_cases'] += item['cases_count']
            locations_data[location]['total_quantity'] += item['total_quantity']
            locations_data[location]['total_cost'] += item['total_cost']
        
        # الحصول على أسماء الفروع
        branches_query = "SELECT name FROM branches WHERE id IN (?, ?)"
        branches_results = conn.execute(branches_query, selected_locations).fetchall()
        locations_names = [row['name'] for row in branches_results]
        
        print(f"أسماء المواقع: {locations_names}")
        print(f"بيانات المواقع: {list(locations_data.keys())}")
        
        # إحصائيات
        total_cases = sum(item['cases_count'] for item in comparison_data)
        total_quantity = sum(item['total_quantity'] for item in comparison_data)
        total_cost = sum(item['total_cost'] for item in comparison_data)
        avg_cost = total_cost / total_cases if total_cases > 0 else 0
        
        print("=== نجح الاختبار! ===")
        
        return f'''
        <h1>✅ اختبار تقرير المقارنة التفصيلية نجح!</h1>
        <p><strong>الفترة:</strong> {period_desc}</p>
        <p><strong>عدد النتائج:</strong> {len(comparison_data)}</p>
        <p><strong>المواقع:</strong> {', '.join(locations_names)}</p>
        <p><strong>إجمالي التكلفة:</strong> {total_cost}</p>
        <p><strong>إجمالي الحالات:</strong> {total_cases}</p>
        <h2>البيانات:</h2>
        <ul>
        ''' + ''.join([f'<li>{item["location_name"]} - {item["drug_name"]}: {item["total_cost"]}</li>' for item in comparison_data]) + '''
        </ul>
        <p><a href="/">العودة للرئيسية</a></p>
        '''
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ خطأ في الاختبار: {e}")
        print(f"تفاصيل الخطأ: {error_details}")
        return f'''
        <h1>❌ فشل الاختبار</h1>
        <p><strong>الخطأ:</strong> {str(e)}</p>
        <pre>{error_details}</pre>
        <p><a href="/">العودة للرئيسية</a></p>
        '''
    finally:
        conn.close()

@app.route('/')
def home():
    return '''
    <h1>اختبار تقرير المقارنة التفصيلية</h1>
    <ul>
        <li><a href="/test_detailed_comparison">اختبار الشهر الحالي</a></li>
        <li><a href="/test_detailed_comparison?date_filter=custom&start_month=2024-01&end_month=2024-02">اختبار فترة مخصصة</a></li>
    </ul>
    '''

if __name__ == '__main__':
    app.run(debug=True, port=8081)
