{% extends "base.html" %}

{% block title %}إدارة الأنسولين - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="mdi mdi-needle me-2"></i>إدارة الأنسولين
                    </h4>
                    <a href="/insulin/dispense" class="btn btn-light btn-add">
                        <i class="mdi mdi-cart me-1"></i>منصرف الأنسولين
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="POST" action="/manage/insulin_types">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="mdi mdi-plus-circle me-2"></i>إضافة نوع أنسولين جديد</h5>
                                        <span class="badge bg-primary">أنواع الأنسولين</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="type_name" class="form-label">اسم النوع</label>
                                        <input type="text" class="form-control" id="type_name" name="type_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="type_description" class="form-label">وصف النوع (اختياري)</label>
                                        <textarea class="form-control" id="type_description" name="type_description" rows="2"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="mdi mdi-plus-circle me-1"></i>إضافة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <form method="POST" action="/manage/insulin_categories">
                            <div class="card shadow-sm">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="mdi mdi-plus-circle me-2"></i>إضافة فئة أنسولين جديدة</h5>
                                        <span class="badge bg-info">فئات الأنسولين</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="category_name" class="form-label">اسم الفئة</label>
                                        <input type="text" class="form-control" id="category_name" name="category_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="category_description" class="form-label">وصف الفئة (اختياري)</label>
                                        <textarea class="form-control" id="category_description" name="category_description" rows="2"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-info">
                                        <i class="mdi mdi-plus-circle me-1"></i>إضافة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="mdi mdi-plus-circle me-2"></i>إضافة صنف أنسولين</h5>
                                    <span class="badge bg-light text-primary">
                                        <i class="mdi mdi-information-outline me-1"></i>إضافة أصناف جديدة من الأنسولين
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/manage/insulin_codes">
                                    <div class="row g-3">
                                        <!-- حقل الكود مخفي - يتم إنشاؤه تلقائياً -->
                                        <input type="hidden" name="code" value="{{ next_code }}">
                                        <div class="col-md-12">
                                            <div class="form-floating">
                                                <input type="text" class="form-control" id="name" name="name" required>
                                                <label for="name">اسم الصنف</label>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-floating">
                                                <textarea class="form-control" id="description" name="description" style="height: 100px;"></textarea>
                                                <label for="description">وصف الصنف (اختياري)</label>
                                            </div>
                                        </div>
                                        <div class="col-12 text-center">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="mdi mdi-content-save me-1"></i>إضافة الصنف
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة تكويدات الأنسولين -->
                <div class="card shadow-sm mt-4">
                    <div class="card-header bg-primary text-white">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة أصناف الأنسولين</h5>
                            </div>
                            <div class="col-auto">
                                <div class="input-group">
                                    <span class="input-group-text bg-light"><i class="mdi mdi-magnify"></i></span>
                                    <input type="text" id="searchInsulinCode" class="form-control" placeholder="بحث...">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if insulin_codes %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-bordered" id="insulinCodesTable">
                                <thead class="table-primary">
                                    <tr class="text-center">
                                        <th>#</th>
                                        <th>كود الصنف</th>
                                        <th>اسم الصنف</th>
                                        <th>وصف الصنف</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for code in insulin_codes %}
                                    <tr>
                                        <td class="text-center">{{ loop.index }}</td>
                                        <td class="text-center fw-bold">{{ code.code }}</td>
                                        <td>{{ code.name }}</td>
                                        <td>{{ code.description or '-' }}</td>
                                        <td class="text-center">{{ code.created_at }}</td>
                                        <td class="text-center">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-primary rounded-circle me-1 edit-insulin-code"
                                                        data-id="{{ code.id }}"
                                                        data-code="{{ code.code }}"
                                                        data-name="{{ code.name }}"
                                                        data-description="{{ code.description or '' }}">
                                                    <i class="mdi mdi-pencil"></i>
                                                </button>
                                                <form method="POST" action="/manage/insulin_codes/{{ code.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الصنف؟');">
                                                    <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                                        <i class="mdi mdi-delete"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="mdi mdi-information me-2"></i>لا توجد أصناف أنسولين مسجلة حالياً.
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- قائمة أنواع الأنسولين -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>أنواع الأنسولين</h5>
                            </div>
                            <div class="card-body">
                                {% if insulin_types %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-bordered" id="insulinTypesTable">
                                        <thead class="table-primary">
                                            <tr class="text-center">
                                                <th>#</th>
                                                <th>اسم النوع</th>
                                                <th>الوصف</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for type in insulin_types %}
                                            <tr>
                                                <td class="text-center">{{ loop.index }}</td>
                                                <td>{{ type.name }}</td>
                                                <td>{{ type.description or '-' }}</td>
                                                <td class="text-center">
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-primary rounded-circle me-1 edit-insulin-type"
                                                                data-id="{{ type.id }}"
                                                                data-name="{{ type.name }}"
                                                                data-description="{{ type.description or '' }}">
                                                            <i class="mdi mdi-pencil"></i>
                                                        </button>
                                                        <form method="POST" action="/manage/insulin_types/{{ type.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا النوع؟');">
                                                            <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                                                <i class="mdi mdi-delete"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-info">
                                    <i class="mdi mdi-information me-2"></i>لا توجد أنواع أنسولين مسجلة حالياً.
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- قائمة فئات الأنسولين -->
                    <div class="col-md-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="mdi mdi-format-list-bulleted me-2"></i>فئات الأنسولين</h5>
                            </div>
                            <div class="card-body">
                                {% if insulin_categories %}
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-bordered" id="insulinCategoriesTable">
                                        <thead class="table-info">
                                            <tr class="text-center">
                                                <th>#</th>
                                                <th>اسم الفئة</th>
                                                <th>الوصف</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for category in insulin_categories %}
                                            <tr>
                                                <td class="text-center">{{ loop.index }}</td>
                                                <td>{{ category.name }}</td>
                                                <td>{{ category.description or '-' }}</td>
                                                <td class="text-center">
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-info rounded-circle me-1 edit-insulin-category"
                                                                data-id="{{ category.id }}"
                                                                data-name="{{ category.name }}"
                                                                data-description="{{ category.description or '' }}">
                                                            <i class="mdi mdi-pencil"></i>
                                                        </button>
                                                        <form method="POST" action="/manage/insulin_categories/{{ category.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الفئة؟');">
                                                            <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                                                <i class="mdi mdi-delete"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-info">
                                    <i class="mdi mdi-information me-2"></i>لا توجد فئات أنسولين مسجلة حالياً.
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل تكويد الأنسولين -->
<div class="modal fade" id="editInsulinCodeModal" tabindex="-1" aria-labelledby="editInsulinCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editInsulinCodeModalLabel">
                    <i class="mdi mdi-pencil me-1"></i>تعديل صنف الأنسولين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/manage/insulin_codes/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_code_id" name="code_id">
                    <div class="mb-3">
                        <label for="edit_code" class="form-label">الكود</label>
                        <input type="text" class="form-control" id="edit_code" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">اسم الصنف</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">وصف الصنف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل نوع الأنسولين -->
<div class="modal fade" id="editInsulinTypeModal" tabindex="-1" aria-labelledby="editInsulinTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editInsulinTypeModalLabel">
                    <i class="mdi mdi-pencil me-1"></i>تعديل نوع الأنسولين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/manage/insulin_types/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_type_id" name="type_id">
                    <div class="mb-3">
                        <label for="edit_type_name" class="form-label">اسم النوع</label>
                        <input type="text" class="form-control" id="edit_type_name" name="type_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_type_description" class="form-label">وصف النوع</label>
                        <textarea class="form-control" id="edit_type_description" name="type_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل فئة الأنسولين -->
<div class="modal fade" id="editInsulinCategoryModal" tabindex="-1" aria-labelledby="editInsulinCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="editInsulinCategoryModalLabel">
                    <i class="mdi mdi-pencil me-1"></i>تعديل فئة الأنسولين
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="/manage/insulin_categories/update">
                <div class="modal-body">
                    <input type="hidden" id="edit_category_id" name="category_id">
                    <div class="mb-3">
                        <label for="edit_category_name" class="form-label">اسم الفئة</label>
                        <input type="text" class="form-control" id="edit_category_name" name="category_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_category_description" class="form-label">وصف الفئة</label>
                        <textarea class="form-control" id="edit_category_description" name="category_description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-info">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // تفعيل البحث في جدول الأصناف
        $("#searchInsulinCode").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("#insulinCodesTable tbody tr").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });

        // تفعيل نموذج تعديل الصنف
        $('.edit-insulin-code').on('click', function() {
            var id = $(this).data('id');
            var code = $(this).data('code');
            var name = $(this).data('name');
            var description = $(this).data('description');

            $('#edit_code_id').val(id);
            $('#edit_code').val(code);
            $('#edit_name').val(name);
            $('#edit_description').val(description);

            $('#editInsulinCodeModal').modal('show');
        });

        // تفعيل نموذج تعديل النوع
        $('.edit-insulin-type').on('click', function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var description = $(this).data('description');

            $('#edit_type_id').val(id);
            $('#edit_type_name').val(name);
            $('#edit_type_description').val(description);

            $('#editInsulinTypeModal').modal('show');
        });

        // تفعيل نموذج تعديل الفئة
        $('.edit-insulin-category').on('click', function() {
            var id = $(this).data('id');
            var name = $(this).data('name');
            var description = $(this).data('description');

            $('#edit_category_id').val(id);
            $('#edit_category_name').val(name);
            $('#edit_category_description').val(description);

            $('#editInsulinCategoryModal').modal('show');
        });
    });
</script>
{% endblock %}
