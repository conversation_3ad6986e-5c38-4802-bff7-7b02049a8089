#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تقرير المقارنة التفصيلية
"""

import sqlite3
import os
from datetime import datetime

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

def test_detailed_comparison_query():
    """اختبار استعلام تقرير المقارنة التفصيلية"""
    
    conn = get_db_connection()
    
    try:
        # اختبار استعلام بسيط للأدوية
        query = '''
            SELECT
                b.name as location_name,
                dr.name as drug_name,
                'دواء' as item_type,
                COUNT(d.id) as cases_count,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.quantity * dd.price) as total_cost,
                AVG(dd.price) as avg_price
            FROM dispensed d
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            JOIN drugs dr ON d.drug_id = dr.id
            WHERE strftime('%Y-%m', d.dispense_month) = ?
            GROUP BY b.id, b.name, dr.id, dr.name
            ORDER BY b.name, total_cost DESC
            LIMIT 5
        '''
        
        current_month = datetime.now().strftime('%Y-%m')
        print(f"اختبار الاستعلام للشهر: {current_month}")
        
        results = conn.execute(query, [current_month]).fetchall()
        
        print(f"عدد النتائج: {len(results)}")
        
        if results:
            print("✅ الاستعلام نجح!")
            
            # اختبار معالجة البيانات
            comparison_data = []
            for row in results:
                row_dict = dict(row)
                # تحويل None إلى 0 للحقول الرقمية
                for key in ['cases_count', 'total_quantity', 'total_cost', 'avg_price']:
                    if row_dict.get(key) is None:
                        row_dict[key] = 0
                comparison_data.append(row_dict)
            
            print("✅ معالجة البيانات نجحت!")
            
            # اختبار حساب الإجماليات
            total_cost = sum((item['total_cost'] if item['total_cost'] is not None else 0) for item in comparison_data)
            total_cases = sum((item['cases_count'] if item['cases_count'] is not None else 0) for item in comparison_data)
            
            print(f"✅ حساب الإجماليات نجح: التكلفة={total_cost}, الحالات={total_cases}")
            
            # اختبار تنظيم البيانات
            locations_data = {}
            for row in comparison_data:
                location = row['location_name']
                if location not in locations_data:
                    locations_data[location] = {
                        'name': location,
                        'drugs': [],
                        'total_cases': 0,
                        'total_quantity': 0,
                        'total_cost': 0
                    }

                drug_data = {
                    'name': row['drug_name'],
                    'type': row['item_type'] if 'item_type' in row else 'دواء',
                    'cases_count': row['cases_count'],
                    'total_quantity': row['total_quantity'],
                    'total_cost': row['total_cost'],
                    'avg_price': row['avg_price']
                }

                locations_data[location]['drugs'].append(drug_data)
                locations_data[location]['total_cases'] += row['cases_count']
                locations_data[location]['total_quantity'] += row['total_quantity']
                locations_data[location]['total_cost'] += row['total_cost']
            
            print("✅ تنظيم البيانات نجح!")
            print(f"عدد المواقع: {len(locations_data)}")
            
            return True
        else:
            print("⚠️ لا توجد بيانات للاختبار")
            return True  # ليس خطأ، فقط لا توجد بيانات
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("=== اختبار تقرير المقارنة التفصيلية ===")
    
    if test_detailed_comparison_query():
        print("\n✅ جميع الاختبارات نجحت! المشكلة تم إصلاحها.")
    else:
        print("\n❌ بعض الاختبارات فشلت.")
