#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Flask, render_template, request, redirect, url_for, flash
import sqlite3
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

def get_db_connection():
    if not os.path.exists('instance'):
        os.makedirs('instance')
    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/')
def home():
    return '''
    <h1>🚀 التطبيق يعمل!</h1>
    <p>إذا رأيت هذه الرسالة، فالتطبيق يعمل بشكل صحيح.</p>
    <a href="/admin/data-management">إدارة البيانات</a>
    '''

@app.route('/admin/data-management')
def data_management():
    return '''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>إدارة البيانات</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-4">
            <h1>📊 إدارة البيانات</h1>
            
            <div class="alert alert-success">
                ✅ التطبيق يعمل بشكل صحيح!
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5>🧪 اختبار التفريغ المتقدم</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="/admin/clear-data">
                        <input type="hidden" name="clear_type" value="advanced">
                        <input type="hidden" name="advanced_type" value="categories">
                        
                        <h6>اختر التصنيفات:</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="selected_categories" value="1" id="cat1">
                            <label class="form-check-label" for="cat1">تصنيف تجريبي 1</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="selected_categories" value="2" id="cat2">
                            <label class="form-check-label" for="cat2">تصنيف تجريبي 2</label>
                        </div>
                        
                        <br>
                        <button type="submit" class="btn btn-warning">تفريغ التصنيفات المحددة</button>
                    </form>
                </div>
            </div>
            
            <br>
            <a href="/" class="btn btn-secondary">العودة للرئيسية</a>
        </div>
    </body>
    </html>
    '''

@app.route('/admin/clear-data', methods=['POST'])
def clear_data():
    try:
        clear_type = request.form.get('clear_type')
        print(f"🔄 نوع التفريغ: {clear_type}")
        
        if clear_type == 'advanced':
            advanced_type = request.form.get('advanced_type')
            print(f"🔄 نوع التفريغ المتقدم: {advanced_type}")
            
            if advanced_type == 'categories':
                selected_categories = request.form.getlist('selected_categories')
                print(f"🏷️ التصنيفات المحددة: {selected_categories}")
                
                if selected_categories:
                    flash(f'تم اختيار {len(selected_categories)} تصنيف للتفريغ', 'success')
                else:
                    flash('لم يتم اختيار أي تصنيفات', 'warning')
        
        flash('تم اختبار التفريغ المتقدم بنجاح!', 'info')
        
    except Exception as e:
        flash(f'خطأ: {str(e)}', 'danger')
        print(f"❌ خطأ: {e}")
    
    return redirect(url_for('data_management'))

if __name__ == '__main__':
    print("🚀 بدء تشغيل التطبيق التجريبي...")
    print("📡 افتح المتصفح على: http://localhost:8080")
    app.run(debug=True, port=8080, host='0.0.0.0')
