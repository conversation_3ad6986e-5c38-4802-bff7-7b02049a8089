{% extends "base.html" %}

{% block title %}تقرير الأحكام القضائية{% endblock %}

{% block scripts %}
<!-- مكتبات التصدير -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
{% endblock %}

{% block extra_css %}
<style>
/* تحديث CSS - إصدار 2.0 */
/* تنسيق الشعار */
.report-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
}

.report-logo {
    max-width: 60px;
    max-height: 50px;
    height: auto;
    margin-bottom: 8px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    object-fit: contain;
}

.report-header h2 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 1.25rem;
}

.report-header p {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* حقوق الملكية */
.copyright-footer {
    margin-top: 15px;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
    text-align: center;
    color: #6c757d;
    font-size: 0.7rem;
}

.copyright-footer img {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-left: 6px;
    vertical-align: middle;
}

.table th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.85rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header .header-title {
    color: white;
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    color: white !important;
    border: none !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    padding: 12px 8px !important;
}

.table-dark th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    color: white !important;
    border-color: #495057 !important;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(0, 123, 255, 0.05);
}

/* تأكيد أنماط رؤوس الجداول */
#reportTable thead th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    color: white !important;
    border: 1px solid #495057 !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    padding: 12px 8px !important;
    text-align: center !important;
}

/* إزالة أي أنماط متضاربة */
.table-dark > :not(caption) > * > * {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    color: white !important;
}

.filter-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.btn-generate {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 10px 30px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    color: white;
}

.report-summary {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.cost-highlight {
    color: #dc3545;
    font-weight: bold;
}

.quantity-highlight {
    color: #28a745;
    font-weight: bold;
}

/* أنماط الطباعة */
@media print {
    .btn, button, .action-buttons, .no-print {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #343a40 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .table thead th {
        background: #495057 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    @page {
        margin: 0.8cm;
        size: A4 landscape;
    }

    .report-logo {
        display: block !important;
        max-width: 60px;
        height: auto;
        margin: 0 auto 10px;
    }

    .report-header {
        display: block !important;
        text-align: center;
        margin-bottom: 15px;
        page-break-inside: avoid;
    }

    .report-header h2 {
        font-size: 1.2rem !important;
    }

    .report-header p {
        font-size: 0.8rem !important;
    }

    .copyright-footer {
        display: block !important;
        text-align: center;
        margin-top: 15px;
        padding-top: 8px;
        border-top: 1px solid #eee;
        page-break-inside: avoid;
        font-size: 8px;
    }

    .copyright-footer img {
        width: 15px;
        height: 15px;
    }

    body {
        font-size: 12pt;
    }

    .table {
        font-size: 10pt;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
                        <li class="breadcrumb-item active">تقرير الأحكام القضائية</li>
                    </ol>
                </div>
                <h4 class="page-title">تقرير الأحكام القضائية</h4>
            </div>
        </div>
    </div>

    <!-- نموذج إعدادات التقرير -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">إعدادات التقرير</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('judicial_report') }}">
                        <div class="filter-section">
                            <div class="row g-3">
                                <!-- النطاق -->
                                <div class="col-md-6">
                                    <label for="scope" class="form-label">النطاق</label>
                                    <select class="form-select" id="scope" name="scope" required onchange="updateScopeOptions()">
                                        <option value="all" {% if request.form.get('scope') == 'all' %}selected{% endif %}>جميع الفروع</option>
                                        <option value="branch" {% if request.form.get('scope') == 'branch' %}selected{% endif %}>فرع محدد</option>
                                        <option value="area" {% if request.form.get('scope') == 'area' %}selected{% endif %}>منطقة محددة</option>
                                        <option value="clinic" {% if request.form.get('scope') == 'clinic' %}selected{% endif %}>عيادة محددة</option>
                                    </select>
                                </div>

                                <!-- اختيار الفرع/المنطقة/العيادة -->
                                <div class="col-md-6">
                                    <label for="scope_id" class="form-label">التحديد</label>
                                    <select class="form-select" id="scope_id" name="scope_id">
                                        <option value="">اختر النطاق أولاً</option>
                                    </select>
                                </div>

                                <!-- نوع التقرير -->
                                <div class="col-md-6">
                                    <label for="report_type" class="form-label">نوع التقرير</label>
                                    <select class="form-select" id="report_type" name="report_type" required>
                                        <option value="medicine" {% if request.form.get('report_type') == 'medicine' %}selected{% endif %}>تكلفة الأدوية</option>
                                        <option value="patient" {% if request.form.get('report_type') == 'patient' %}selected{% endif %}>بيانات المرضى</option>
                                    </select>
                                </div>

                                <!-- مستوى التفاصيل -->
                                <div class="col-md-6">
                                    <label for="detail_level" class="form-label">مستوى التفاصيل</label>
                                    <select class="form-select" id="detail_level" name="detail_level" required>
                                        <option value="summary" {% if request.form.get('detail_level') == 'summary' %}selected{% endif %}>إجمالي</option>
                                        <option value="detailed" {% if request.form.get('detail_level') == 'detailed' %}selected{% endif %}>تفصيلي</option>
                                    </select>
                                </div>

                                <!-- الفترة الزمنية -->
                                <div class="col-md-6">
                                    <label for="date_range" class="form-label">الفترة الزمنية</label>
                                    <select class="form-select" id="date_range" name="date_range" onchange="toggleCustomDates()">
                                        <optgroup label="فترات قصيرة">
                                            <option value="month" {% if request.form.get('date_range') == 'month' %}selected{% endif %}>الشهر الحالي</option>
                                            <option value="current_and_previous_month" {% if request.form.get('date_range') == 'current_and_previous_month' %}selected{% endif %}>الشهر الحالي والسابق</option>
                                            <option value="last_3_months" {% if request.form.get('date_range') == 'last_3_months' %}selected{% endif %}>آخر 3 أشهر</option>
                                        </optgroup>
                                        <optgroup label="فترات متوسطة">
                                            <option value="last_6_months" {% if request.form.get('date_range') == 'last_6_months' %}selected{% endif %}>آخر 6 أشهر</option>
                                            <option value="quarter" {% if request.form.get('date_range') == 'quarter' %}selected{% endif %}>الربع الحالي</option>
                                            <option value="q1" {% if request.form.get('date_range') == 'q1' %}selected{% endif %}>الربع الأول</option>
                                            <option value="q2" {% if request.form.get('date_range') == 'q2' %}selected{% endif %}>الربع الثاني</option>
                                            <option value="q3" {% if request.form.get('date_range') == 'q3' %}selected{% endif %}>الربع الثالث</option>
                                            <option value="q4" {% if request.form.get('date_range') == 'q4' %}selected{% endif %}>الربع الرابع</option>
                                        </optgroup>
                                        <optgroup label="فترات طويلة">
                                            <option value="year" {% if request.form.get('date_range') == 'year' %}selected{% endif %}>السنة الحالية</option>
                                            <option value="custom" {% if request.form.get('date_range') == 'custom' %}selected{% endif %}>فترة مخصصة</option>
                                        </optgroup>
                                    </select>
                                </div>

                                <!-- الفترة المخصصة -->
                                <div class="col-md-6" id="custom_dates_section" style="{% if request.form.get('date_range') == 'custom' %}display: block;{% else %}display: none;{% endif %}">
                                    <div class="row">
                                        <div class="col-6">
                                            <label for="start_date" class="form-label">من شهر</label>
                                            <input type="month" class="form-control" id="start_date" name="start_date" value="{{ request.form.get('start_date', '') }}">
                                        </div>
                                        <div class="col-6">
                                            <label for="end_date" class="form-label">إلى شهر</label>
                                            <input type="month" class="form-control" id="end_date" name="end_date" value="{{ request.form.get('end_date', '') }}">
                                        </div>
                                    </div>
                                </div>

                                <!-- زر إنشاء التقرير -->
                                <div class="col-12">
                                    <button type="submit" class="btn btn-generate" onclick="return validateForm()">
                                        <i class="mdi mdi-file-chart me-2"></i>إنشاء التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- عرض التقرير -->
    {% if request.method == 'POST' %}
        {% if report_data %}
        <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="header-title">{{ report_title }}</h4>
                    <div class="action-buttons">
                        <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                            <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="exportToPDF()">
                            <i class="mdi mdi-file-pdf me-1"></i>تصدير PDF
                        </button>
                        <button class="btn btn-info btn-sm" onclick="printReport()">
                            <i class="mdi mdi-printer me-1"></i>طباعة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- الشعار -->
                    <div class="report-header">
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo" style="max-width: 60px !important; max-height: 50px !important;">
                        <h2 style="font-size: 1.25rem !important; margin-bottom: 5px !important;"><i class="mdi mdi-file-document-outline me-2"></i>{{ report_title }}</h2>
                        <p style="font-size: 0.85rem !important;"><i class="mdi mdi-calendar me-2"></i>الفترة: {{ period_desc }}</p>
                    </div>

                    <!-- ملخص التقرير -->
                    <div class="report-summary">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h5>{{ report_data|length }}</h5>
                                <small>إجمالي السجلات</small>
                            </div>
                            <div class="col-md-3">
                                <h6>{{ period_desc or 'جميع الفترات' }}</h6>
                                <small>الفترة الزمنية</small>
                            </div>
                            {% if request.form.get('report_type') == 'medicine' %}
                            <div class="col-md-3">
                                {% set total_cost = 0 %}
                                {% if request.form.get('detail_level') == 'summary' %}
                                    {% for row in report_data %}
                                        {% set total_cost = total_cost + row.total_cost %}
                                    {% endfor %}
                                {% else %}
                                    {% for row in report_data %}
                                        {% set total_cost = total_cost + row.cost %}
                                    {% endfor %}
                                {% endif %}
                                <h5>{{ "%.2f"|format(total_cost) }} ج.م</h5>
                                <small>إجمالي التكلفة</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- جدول التقرير -->
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover" id="reportTable">
                            <thead style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;">
                                <tr class="text-center" style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;">
                                    {% if request.form.get('report_type') == 'medicine' %}
                                        {% if request.form.get('detail_level') == 'summary' %}
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">اسم الدواء</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">عدد الحالات</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">الكمية</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">التكلفة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">متوسط التكلفة</th>
                                        {% else %}
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">اسم الدواء</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">الوحدة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">الكمية</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">اسم المريض</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">التكلفة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">متوسط التكلفة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">العيادة</th>
                                        {% endif %}
                                    {% elif request.form.get('report_type') == 'patient' %}
                                        {% if request.form.get('detail_level') == 'summary' %}
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">اسم المريض</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">اسم الدواء</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">الكمية</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">التكلفة</th>
                                        {% else %}
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">اسم المريض</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">التشخيص</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">اسم الدواء</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">الوحدة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">سعر الوحدة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">الجرعة شهرياً</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">التكلفة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">تاريخ حكم المحكمة</th>
                                            <th style="background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important; color: white !important; border: none !important;">تاريخ بداية العلاج</th>
                                        {% endif %}
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in report_data %}
                                <tr class="align-middle">
                                    {% if request.form.get('report_type') == 'medicine' %}
                                        {% if request.form.get('detail_level') == 'summary' %}
                                            <td class="fw-bold text-primary">{{ row.medicine_name }}</td>
                                            <td class="text-center">
                                                <span class="badge bg-info">{{ row.case_count }}</span>
                                            </td>
                                            <td class="text-center quantity-highlight">{{ "%.2f"|format(row.total_quantity) }}</td>
                                            <td class="text-end cost-highlight">{{ "%.2f"|format(row.total_cost) }} ج.م</td>
                                            <td class="text-end">{{ "%.2f"|format(row.avg_cost) }} ج.م</td>
                                        {% else %}
                                            <td class="fw-bold text-primary">{{ row.medicine_name }}</td>
                                            <td class="text-center">
                                                <span class="badge bg-secondary">{{ row.unit }}</span>
                                            </td>
                                            <td class="text-center quantity-highlight">{{ "%.2f"|format(row.quantity) }}</td>
                                            <td>{{ row.patient_name }}</td>
                                            <td class="text-end cost-highlight">{{ "%.2f"|format(row.cost) }} ج.م</td>
                                            <td class="text-end">{{ "%.2f"|format(row.avg_cost) }} ج.م</td>
                                            <td class="text-muted small">{{ row.branch_name }} - {{ row.area_name }} - {{ row.clinic_name }}</td>
                                        {% endif %}
                                    {% elif request.form.get('report_type') == 'patient' %}
                                        {% if request.form.get('detail_level') == 'summary' %}
                                            <td class="fw-bold text-primary">{{ row.patient_name }}</td>
                                            <td>{{ row.medicine_name }}</td>
                                            <td class="text-center quantity-highlight">{{ "%.2f"|format(row.total_quantity) }}</td>
                                            <td class="text-end cost-highlight">{{ "%.2f"|format(row.total_cost) }} ج.م</td>
                                        {% else %}
                                            <td class="fw-bold text-primary">{{ row.patient_name }}</td>
                                            <td>
                                                <span class="badge bg-success">{{ row.diagnosis or 'غير محدد' }}</span>
                                            </td>
                                            <td>{{ row.medicine_name }}</td>
                                            <td class="text-center">
                                                <span class="badge bg-secondary">{{ row.unit }}</span>
                                            </td>
                                            <td class="text-end">{{ "%.2f"|format(row.unit_price) }} ج.م</td>
                                            <td class="text-center quantity-highlight">{{ "%.2f"|format(row.monthly_dose) }}</td>
                                            <td class="text-end cost-highlight">{{ "%.2f"|format(row.monthly_cost) }} ج.م</td>
                                            <td class="text-center">{{ row.court_ruling_date }}</td>
                                            <td class="text-center">{{ row.treatment_start_date }}</td>
                                        {% endif %}
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- حقوق الملكية -->
                <div class="copyright-footer" style="font-size: 0.7rem !important; margin-top: 15px !important; padding-top: 8px !important;">
                    <img src="{{ url_for('static', filename='images/Ahmed.jpeg') }}" alt="ك/أحمد علي أحمد" style="width: 18px !important; height: 18px !important; margin-left: 6px !important;">
                    <span><i class="mdi mdi-copyright me-1"></i>جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}</span>
                </div>
            </div>
        </div>
    </div>
        {% else %}
        <!-- رسالة عدم وجود بيانات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="mdi mdi-file-search-outline" style="font-size: 4rem; color: #6c757d;"></i>
                        <h4 class="mt-3 text-muted">لا توجد بيانات</h4>
                        <p class="text-muted">لم يتم العثور على بيانات تطابق المعايير المحددة. يرجى تعديل خيارات البحث والمحاولة مرة أخرى.</p>
                    </div>

                    <!-- حقوق الملكية -->
                    <div class="copyright-footer" style="font-size: 0.7rem !important; margin-top: 15px !important; padding-top: 8px !important;">
                        <img src="{{ url_for('static', filename='images/Ahmed.jpeg') }}" alt="ك/أحمد علي أحمد" style="width: 18px !important; height: 18px !important; margin-left: 6px !important;">
                        <span><i class="mdi mdi-copyright me-1"></i>جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}</span>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    {% else %}
    <!-- رسالة ترحيبية -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="mdi mdi-chart-line" style="font-size: 4rem; color: #007bff;"></i>
                    <h4 class="mt-3 text-primary">مرحباً بك في نظام تقارير الأحكام القضائية</h4>
                    <p class="text-muted">اختر إعدادات التقرير أعلاه وانقر على "إنشاء التقرير" للحصول على التقرير المطلوب.</p>
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="mdi mdi-pill text-success" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">تقارير الأدوية</h6>
                                <small class="text-muted">تكلفة وإحصائيات الأدوية</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="mdi mdi-account-group text-info" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">تقارير المرضى</h6>
                                <small class="text-muted">بيانات وإحصائيات المرضى</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="mdi mdi-filter text-warning" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">فلترة متقدمة</h6>
                                <small class="text-muted">حسب الفرع والمنطقة والعيادة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <i class="mdi mdi-file-export text-danger" style="font-size: 2rem;"></i>
                                <h6 class="mt-2">تصدير التقارير</h6>
                                <small class="text-muted">Excel و PDF</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// تحديث خيارات النطاق
function updateScopeOptions() {
    const scope = document.getElementById('scope').value;
    const scopeSelect = document.getElementById('scope_id');
    
    // مسح الخيارات الحالية
    scopeSelect.innerHTML = '<option value="">اختر...</option>';
    
    if (scope === 'all') {
        scopeSelect.disabled = true;
        return;
    }
    
    scopeSelect.disabled = false;
    
    // بيانات الفروع والمناطق والعيادات
    const branches = {{ branches|tojson|safe }};
    const areas = {{ areas|tojson|safe }};
    const clinics = {{ clinics|tojson|safe }};

    console.log('Scope:', scope);
    console.log('Branches:', branches);
    console.log('Areas:', areas);
    console.log('Clinics:', clinics);
    
    if (scope === 'branch') {
        branches.forEach(branch => {
            const option = document.createElement('option');
            option.value = branch.id;
            option.textContent = branch.name;
            scopeSelect.appendChild(option);
        });
    } else if (scope === 'area') {
        areas.forEach(area => {
            const option = document.createElement('option');
            option.value = area.id;
            option.textContent = `${area.branch_name} - ${area.name}`;
            scopeSelect.appendChild(option);
        });
    } else if (scope === 'clinic') {
        clinics.forEach(clinic => {
            const option = document.createElement('option');
            option.value = clinic.id;
            option.textContent = `${clinic.branch_name} - ${clinic.area_name} - ${clinic.name}`;
            scopeSelect.appendChild(option);
        });
    }
}



// إظهار/إخفاء قسم التواريخ المخصصة
function toggleCustomDates() {
    const dateRange = document.getElementById('date_range');
    const customSection = document.getElementById('custom_dates_section');

    if (!dateRange || !customSection) {
        return;
    }

    if (dateRange.value === 'custom') {
        customSection.style.display = 'block';
        // جعل الحقول مطلوبة
        document.getElementById('start_date').required = true;
        document.getElementById('end_date').required = true;
    } else {
        customSection.style.display = 'none';
        // إزالة الحقول المطلوبة
        document.getElementById('start_date').required = false;
        document.getElementById('end_date').required = false;
    }
}

// التحقق من صحة النموذج
function validateForm() {
    const dateRange = document.getElementById('date_range').value;

    if (dateRange === 'custom') {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;

        if (!startDate || !endDate) {
            alert('يرجى تحديد تاريخ البداية والنهاية للفترة المخصصة');
            return false;
        }

        if (startDate > endDate) {
            alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
            return false;
        }
    }

    return true;
}

// تصدير إلى Excel
async function exportToExcel() {
    try {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('تقرير الأحكام القضائية');

        let currentRow = 1;

        // إضافة عنوان التقرير
        const titleCell = worksheet.getCell('A' + currentRow);
        titleCell.value = '{{ report_title|default("تقرير الأحكام القضائية") }}';
        titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
        titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF667eea' } };
        titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
        currentRow += 2;

        // إضافة معلومات التقرير
        const reportInfo = [
            'الفترة الزمنية: {{ period_desc|default("جميع الفترات") }}',
            'تاريخ إنشاء التقرير: ' + new Date().toLocaleDateString('ar-EG')
        ];

        reportInfo.forEach(info => {
            worksheet.getCell('A' + currentRow).value = info;
            worksheet.getCell('A' + currentRow).font = { bold: true };
            currentRow++;
        });
        currentRow++;

        // إضافة رؤوس الجدول
        const table = document.getElementById('reportTable');
        if (table) {
            const headers = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(cell => headers.push(cell.textContent.trim()));

            // إضافة الرؤوس
            headers.forEach((header, index) => {
                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                cell.value = header;
                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF495057' } };
                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                cell.border = {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                };
            });
            currentRow++;

            // إضافة البيانات
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach((cell, index) => {
                    const excelCell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    excelCell.value = cell.textContent.trim();
                    excelCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    excelCell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
                currentRow++;
            });

            // تحديد عرض الأعمدة
            worksheet.columns = headers.map(() => ({ width: 20 }));
        }

        // تصدير الملف
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const fileName = "تقرير_الأحكام_القضائية_" + new Date().toISOString().slice(0, 10) + ".xlsx";
        saveAs(blob, fileName);

        console.log('تم تصدير Excel بنجاح');
    } catch (error) {
        console.error("Error exporting to Excel:", error);
        alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
    }
}

// تصدير إلى PDF
async function exportToPDF() {
    try {
        // إخفاء العناصر غير المطلوبة
        var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
        noprint.forEach(function(element) {
            element.style.display = 'none';
        });

        // الحصول على المحتوى المراد تحويله
        var element = document.querySelector('.container-fluid');

        // تحويل إلى صورة
        const canvas = await html2canvas(element, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: element.scrollWidth,
            height: element.scrollHeight
        });

        // إنشاء PDF
        const { jsPDF } = window.jspdf;
        var pdf = new jsPDF('p', 'mm', 'a4');

        var imgData = canvas.toDataURL('image/png');
        var imgWidth = 210;
        var pageHeight = 295;
        var imgHeight = (canvas.height * imgWidth) / canvas.width;
        var heightLeft = imgHeight;
        var position = 0;

        // إضافة الصفحة الأولى
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        var fileName = 'تقرير_الأحكام_القضائية_' + new Date().toISOString().slice(0, 10) + '.pdf';
        pdf.save(fileName);

        // إظهار العناصر مرة أخرى
        noprint.forEach(function(element) {
            element.style.display = '';
        });

        console.log('تم تصدير PDF بنجاح');
    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير PDF. يرجى المحاولة مرة أخرى.');

        // إظهار العناصر مرة أخرى في حالة الخطأ
        var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
        noprint.forEach(function(element) {
            element.style.display = '';
        });
    }
}



// طباعة التقرير
function printReport() {
    try {
        // إخفاء الأزرار والعناصر غير المطلوبة للطباعة
        const noprint = document.querySelectorAll('.btn, button, .action-buttons, .no-print');
        noprint.forEach(function(element) {
            element.style.display = 'none';
        });

        // طباعة الصفحة
        window.print();

        // إظهار العناصر مرة أخرى بعد الطباعة
        setTimeout(() => {
            noprint.forEach(function(element) {
                element.style.display = '';
            });
        }, 1000);

        console.log('تم إرسال التقرير للطباعة');
    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        alert('حدث خطأ أثناء الطباعة: ' + error.message);

        // إظهار العناصر مرة أخرى في حالة الخطأ
        const noprint = document.querySelectorAll('.btn, button, .action-buttons, .no-print');
        noprint.forEach(function(element) {
            element.style.display = '';
        });
    }
}

// تحديث الخيارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateScopeOptions();
    toggleCustomDates();

    // استعادة القيم المحفوظة
    const savedScope = "{{ request.form.get('scope', '') }}";
    const savedScopeId = "{{ request.form.get('scope_id', '') }}";

    if (savedScope) {
        document.getElementById('scope').value = savedScope;
        updateScopeOptions();

        if (savedScopeId) {
            setTimeout(() => {
                document.getElementById('scope_id').value = savedScopeId;
            }, 100);
        }
    }

    // استعادة قيمة الفترة الزمنية
    const savedDateRange = "{{ request.form.get('date_range', '') }}";
    if (savedDateRange) {
        const dateRangeSelect = document.getElementById('date_range');
        if (dateRangeSelect) {
            dateRangeSelect.value = savedDateRange;
            toggleCustomDates();
        }
    }

    // التأكد من إظهار قسم التواريخ المخصصة إذا كانت محددة
    setTimeout(() => {
        toggleCustomDates();
    }, 100);
});
</script>
{% endblock %}
