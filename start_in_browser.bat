@echo off
echo Starting the Medicine Dispenser application and opening it in the browser...

REM Start the application using the run_app.py script
start "MedicineApp" cmd /c "python "%~dp0run_app.py""

REM Wait for a few seconds to ensure the server has started
timeout /t 5 /nobreak >nul

REM Open the application in the default web browser
start http://127.0.0.1:8080

echo The application should be running in your browser.
