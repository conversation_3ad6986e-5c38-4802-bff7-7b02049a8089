from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, make_response, send_file
import sqlite3
import os
import json
import shutil
import zipfile
from datetime import datetime, timedelta

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'مفتاح-سري-افتراضي-للتطوير'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/medicine_dispenser.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# التأكد من وجود مجلد instance
if not os.path.exists('instance'):
    os.makedirs('instance')

# دالة للاتصال بقاعدة البيانات
def get_db_connection():
    # التأكد من وجود مجلد instance
    if not os.path.exists('instance'):
        os.makedirs('instance')

    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

# دالة للتحقق من إمكانية التعديل/الحذف (خلال 4 أشهر)
def can_edit_or_delete(dispense_date_str):
    """
    التحقق من إمكانية التعديل أو الحذف
    يُسمح بالتعديل/الحذف فقط خلال 4 أشهر من تاريخ الإدخال
    """
    try:
        # تحويل تاريخ الصرف إلى datetime
        if isinstance(dispense_date_str, str):
            dispense_date = datetime.strptime(dispense_date_str, '%Y-%m-%d')
        else:
            dispense_date = dispense_date_str

        # التاريخ الحالي
        current_date = datetime.now()

        # حساب الفرق بالأشهر
        months_diff = (current_date.year - dispense_date.year) * 12 + (current_date.month - dispense_date.month)

        # السماح بالتعديل/الحذف خلال 4 أشهر فقط
        can_modify = months_diff < 4

        print(f"تحقق من التاريخ: {dispense_date_str} - الفرق: {months_diff} أشهر - مسموح: {can_modify}")

        return can_modify, months_diff

    except Exception as e:
        print(f"خطأ في التحقق من التاريخ: {e}")
        return False, 0

# دوال إدارة النسخ الاحتياطية والتفريغ
def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # اسم الملف مع التاريخ والوقت
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'medicine_dispenser_backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)

        # نسخ قاعدة البيانات
        shutil.copy2('instance/medicine_dispenser.db', backup_path)

        # ضغط النسخة الاحتياطية
        zip_filename = f'medicine_dispenser_backup_{timestamp}.zip'
        zip_path = os.path.join(backup_dir, zip_filename)

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(backup_path, backup_filename)

        # حذف الملف غير المضغوط
        os.remove(backup_path)

        return zip_path, zip_filename

    except Exception as e:
        print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None, None

def get_table_counts():
    """الحصول على عدد السجلات في كل جدول"""
    try:
        conn = get_db_connection()
        counts = {}

        tables = [
            'branches', 'areas', 'clinics', 'drug_categories', 'drugs',
            'dispensed', 'dispensed_details', 'insulin_dispensed',
            'drug_groups', 'insulin_codes', 'insulin_types', 'insulin_categories'
        ]

        for table in tables:
            try:
                count = conn.execute(f'SELECT COUNT(*) FROM {table}').fetchone()[0]
                counts[table] = count
            except:
                counts[table] = 0

        conn.close()
        return counts

    except Exception as e:
        print(f"خطأ في حساب السجلات: {e}")
        return {}

def clear_dispensing_data():
    """تفريغ بيانات الصرف فقط (الاحتفاظ بالبيانات الأساسية)"""
    try:
        conn = get_db_connection()

        # حذف بيانات الصرف
        conn.execute('DELETE FROM dispensed_details')
        conn.execute('DELETE FROM dispensed')
        conn.execute('DELETE FROM insulin_dispensed')
        conn.execute('DELETE FROM drug_groups')

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"خطأ في تفريغ بيانات الصرف: {e}")
        return False

def clear_all_data():
    """تفريغ جميع البيانات"""
    try:
        conn = get_db_connection()

        # حذف جميع البيانات بالترتيب الصحيح
        tables_order = [
            'dispensed_details', 'dispensed', 'insulin_dispensed', 'drug_groups',
            'drugs', 'drug_categories', 'clinics', 'areas', 'branches',
            'insulin_codes', 'insulin_types', 'insulin_categories'
        ]

        for table in tables_order:
            try:
                conn.execute(f'DELETE FROM {table}')
            except:
                pass

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"خطأ في تفريغ جميع البيانات: {e}")
        return False

def clear_specific_tables(tables_list):
    """تفريغ جداول محددة"""
    try:
        conn = get_db_connection()

        for table in tables_list:
            try:
                conn.execute(f'DELETE FROM {table}')
            except Exception as e:
                print(f"خطأ في حذف جدول {table}: {e}")

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"خطأ في تفريغ الجداول المحددة: {e}")
        return False

def clear_by_categories(category_ids):
    """تفريغ بيانات الصرف لتصنيفات محددة"""
    try:
        conn = get_db_connection()

        # تحويل قائمة المعرفات إلى نص للاستعلام
        category_ids_str = ','.join(map(str, category_ids))

        # حذف تفاصيل الصرف للأدوية في التصنيفات المحددة
        conn.execute(f'''
            DELETE FROM dispensed_details
            WHERE dispensed_id IN (
                SELECT d.id FROM dispensed d
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE dr.category_id IN ({category_ids_str})
            )
        ''')

        # حذف سجلات الصرف للأدوية في التصنيفات المحددة
        conn.execute(f'''
            DELETE FROM dispensed
            WHERE drug_id IN (
                SELECT id FROM drugs WHERE category_id IN ({category_ids_str})
            )
        ''')

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"خطأ في تفريغ التصنيفات المحددة: {e}")
        return False

def clear_by_clinics(clinic_ids):
    """تفريغ بيانات الصرف لعيادات محددة"""
    try:
        conn = get_db_connection()

        clinic_ids_str = ','.join(map(str, clinic_ids))

        # حذف تفاصيل الصرف للعيادات المحددة
        conn.execute(f'''
            DELETE FROM dispensed_details
            WHERE dispensed_id IN (
                SELECT id FROM dispensed WHERE clinic_id IN ({clinic_ids_str})
            )
        ''')

        # حذف سجلات الصرف للعيادات المحددة
        conn.execute(f'DELETE FROM dispensed WHERE clinic_id IN ({clinic_ids_str})')

        # حذف سجلات الأنسولين للعيادات المحددة
        conn.execute(f'DELETE FROM insulin_dispensed WHERE clinic_id IN ({clinic_ids_str})')

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"خطأ في تفريغ العيادات المحددة: {e}")
        return False

def clear_by_date_range(start_date, end_date):
    """تفريغ بيانات الصرف لفترة زمنية محددة"""
    try:
        conn = get_db_connection()

        # حذف تفاصيل الصرف للفترة المحددة
        conn.execute('''
            DELETE FROM dispensed_details
            WHERE dispensed_id IN (
                SELECT id FROM dispensed
                WHERE dispense_month BETWEEN ? AND ?
            )
        ''', (start_date, end_date))

        # حذف سجلات الصرف للفترة المحددة
        conn.execute('DELETE FROM dispensed WHERE dispense_month BETWEEN ? AND ?',
                    (start_date, end_date))

        # حذف سجلات الأنسولين للفترة المحددة
        conn.execute('DELETE FROM insulin_dispensed WHERE dispense_month BETWEEN ? AND ?',
                    (start_date, end_date))

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"خطأ في تفريغ الفترة الزمنية المحددة: {e}")
        return False

def clear_by_insulin_types(type_names):
    """تفريغ بيانات الأنسولين لأنواع محددة"""
    try:
        conn = get_db_connection()

        # تحويل قائمة الأسماء إلى نص للاستعلام
        type_names_str = ','.join([f"'{name}'" for name in type_names])

        # حذف سجلات الأنسولين للأنواع المحددة
        conn.execute(f'''
            DELETE FROM insulin_dispensed
            WHERE type IN ({type_names_str})
        ''')

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"خطأ في تفريغ أنواع الأنسولين المحددة: {e}")
        return False

def get_categories_with_counts():
    """الحصول على قائمة التصنيفات مع عدد الأدوية والسجلات"""
    try:
        conn = get_db_connection()

        # جلب التصنيفات الأساسية
        categories = conn.execute('''
            SELECT id, name FROM drug_categories ORDER BY name
        ''').fetchall()

        result = []
        for cat in categories:
            # حساب عدد الأدوية لكل تصنيف
            drugs_count = conn.execute(
                'SELECT COUNT(*) FROM drugs WHERE category_id = ?',
                (cat['id'],)
            ).fetchone()[0]

            # حساب عدد سجلات الصرف لكل تصنيف
            dispensed_count = conn.execute('''
                SELECT COUNT(DISTINCT d.id)
                FROM dispensed d
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE dr.category_id = ?
            ''', (cat['id'],)).fetchone()[0]

            result.append({
                'id': cat['id'],
                'name': cat['name'],
                'drugs_count': drugs_count,
                'dispensed_count': dispensed_count
            })

        conn.close()
        return result

    except Exception as e:
        print(f"خطأ في الحصول على التصنيفات: {e}")
        return []

def get_insulin_types_with_counts():
    """الحصول على قائمة أنواع الأنسولين مع عدد السجلات"""
    try:
        conn = get_db_connection()

        # جلب أنواع الأنسولين
        types = conn.execute('''
            SELECT id, name FROM insulin_types ORDER BY name
        ''').fetchall()

        result = []
        for type_item in types:
            # حساب عدد سجلات الأنسولين لكل نوع
            insulin_count = conn.execute(
                'SELECT COUNT(*) FROM insulin_dispensed WHERE type = ?',
                (type_item['name'],)
            ).fetchone()[0]

            result.append({
                'id': type_item['id'],
                'name': type_item['name'],
                'insulin_count': insulin_count
            })

        conn.close()
        return result

    except Exception as e:
        print(f"خطأ في الحصول على أنواع الأنسولين: {e}")
        return []

def get_clinics_with_counts():
    """الحصول على قائمة العيادات مع عدد السجلات"""
    try:
        conn = get_db_connection()

        # جلب العيادات مع معلومات المناطق (بناءً على الهيكل الفعلي)
        clinics = conn.execute('''
            SELECT
                c.id,
                c.name,
                a.name as area_name
            FROM clinics c
            LEFT JOIN areas a ON c.area_id = a.id
            ORDER BY a.name, c.name
        ''').fetchall()



        result = []
        for clinic in clinics:
            # حساب عدد سجلات الصرف لكل عيادة
            dispensed_count = conn.execute(
                'SELECT COUNT(*) FROM dispensed WHERE clinic_id = ?',
                (clinic['id'],)
            ).fetchone()[0]

            # حساب عدد سجلات الأنسولين لكل عيادة
            insulin_count = conn.execute(
                'SELECT COUNT(*) FROM insulin_dispensed WHERE clinic_id = ?',
                (clinic['id'],)
            ).fetchone()[0]

            result.append({
                'id': clinic['id'],
                'name': clinic['name'],
                'branch_name': clinic['area_name'] if clinic['area_name'] else 'غير محدد',
                'area_name': clinic['area_name'] if clinic['area_name'] else 'غير محدد',
                'dispensed_count': dispensed_count,
                'insulin_count': insulin_count
            })

        conn.close()
        return result

    except Exception as e:
        print(f"خطأ في الحصول على العيادات: {e}")
        return []

def restore_backup(backup_zip_path):
    """استعادة نسخة احتياطية من ملف مضغوط"""
    try:
        # التحقق من وجود الملف
        if not os.path.exists(backup_zip_path):
            print(f"ملف النسخة الاحتياطية غير موجود: {backup_zip_path}")
            return False

        # إنشاء مجلد مؤقت للاستخراج
        temp_dir = 'temp_restore'
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        os.makedirs(temp_dir)

        # استخراج الملف المضغوط
        with zipfile.ZipFile(backup_zip_path, 'r') as zipf:
            zipf.extractall(temp_dir)

        # البحث عن ملف قاعدة البيانات
        db_file = None
        for file in os.listdir(temp_dir):
            if file.endswith('.db'):
                db_file = os.path.join(temp_dir, file)
                break

        if not db_file:
            print("لم يتم العثور على ملف قاعدة البيانات في النسخة الاحتياطية")
            shutil.rmtree(temp_dir)
            return False

        # التحقق من سلامة قاعدة البيانات
        try:
            test_conn = sqlite3.connect(db_file)
            test_conn.execute('SELECT COUNT(*) FROM sqlite_master')
            test_conn.close()
        except Exception as e:
            print(f"ملف قاعدة البيانات تالف: {e}")
            shutil.rmtree(temp_dir)
            return False

        # نسخ قاعدة البيانات المستعادة
        target_path = 'instance/medicine_dispenser.db'
        shutil.copy2(db_file, target_path)

        # تنظيف المجلد المؤقت
        shutil.rmtree(temp_dir)

        print(f"تم استعادة النسخة الاحتياطية بنجاح من: {backup_zip_path}")
        return True

    except Exception as e:
        print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
        # تنظيف المجلد المؤقت في حالة الخطأ
        if os.path.exists('temp_restore'):
            shutil.rmtree('temp_restore')
        return False

def get_backup_info(backup_zip_path):
    """الحصول على معلومات النسخة الاحتياطية"""
    try:
        info = {
            'size': 0,
            'date': None,
            'tables_count': 0,
            'valid': False
        }

        if os.path.exists(backup_zip_path):
            # حجم الملف
            info['size'] = round(os.path.getsize(backup_zip_path) / 1024, 2)  # KB

            # تاريخ الملف
            info['date'] = datetime.fromtimestamp(os.path.getmtime(backup_zip_path))

            # التحقق من صحة الملف
            try:
                with zipfile.ZipFile(backup_zip_path, 'r') as zipf:
                    files = zipf.namelist()
                    if any(f.endswith('.db') for f in files):
                        info['valid'] = True
            except:
                pass

        return info

    except Exception as e:
        print(f"خطأ في الحصول على معلومات النسخة الاحتياطية: {e}")
        return {'size': 0, 'date': None, 'tables_count': 0, 'valid': False}

def init_db():
    conn = get_db_connection()
    
    # قراءة وتنفيذ ملف schema.sql
    with open('schema.sql', 'r', encoding='utf-8') as f:
        schema = f.read()
        conn.executescript(schema)
    
    # إنشاء بعض البيانات الافتراضية إذا كانت الجداول فارغة
    cur = conn.cursor()
    
    # التحقق من وجود فروع
    cur.execute('SELECT COUNT(*) FROM branches')
    if cur.fetchone()[0] == 0:
        cur.execute("INSERT INTO branches (name) VALUES ('فرع سوهاج')")
    
    # التحقق من وجود تصنيفات الأدوية
    cur.execute('SELECT COUNT(*) FROM drug_categories')
    if cur.fetchone()[0] == 0:
        cur.execute("INSERT INTO drug_categories (name) VALUES ('مسكنات'), ('مضادات حيوية'), ('أدوية ضغط'), ('أدوية سكر')")
    
    conn.commit()
    conn.close()

# دالة لتنسيق التاريخ إلى شهر/سنة
def format_month_year(date_str):
    """تحويل التاريخ من YYYY-MM-DD إلى MM/YYYY"""
    if not date_str:
        return ""
    try:
        if isinstance(date_str, str):
            # إذا كان التاريخ بصيغة YYYY-MM-DD
            if len(date_str) >= 7:
                year, month = date_str[:4], date_str[5:7]
                return f"{month}/{year}"
        return date_str
    except:
        return date_str

# إضافة متغير now للقوالب
@app.context_processor
def inject_now():
    return {
        'now': datetime.now(),
        'format_month_year': format_month_year
    }

# فحص وإنشاء جداول الأحكام القضائية المفقودة
def ensure_judicial_tables():
    """التأكد من وجود جداول الأحكام القضائية"""
    conn = get_db_connection()
    try:
        # فحص وجود جدول judicial_dispensed
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='judicial_dispensed'")
        if not cursor.fetchone():
            print("🔧 إنشاء جداول الأحكام القضائية المفقودة...")

            # إنشاء جدول المرضى
            conn.execute('''CREATE TABLE IF NOT EXISTS judicial_patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                diagnosis TEXT,
                court_ruling_date DATE NOT NULL,
                treatment_start_date DATE NOT NULL,
                clinic_id INTEGER NOT NULL,
                area_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (clinic_id) REFERENCES clinics (id),
                FOREIGN KEY (area_id) REFERENCES areas (id)
            )''')

            # إنشاء جدول أدوية أحكام المحكمة
            conn.execute('''CREATE TABLE IF NOT EXISTS judicial_medicines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                unit TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )''')

            # إنشاء جدول الأدوية المصروفة للأحكام القضائية
            conn.execute('''CREATE TABLE IF NOT EXISTS judicial_dispensed (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                diagnosis TEXT NOT NULL,
                medicine_name TEXT NOT NULL,
                unit TEXT NOT NULL,
                unit_price REAL NOT NULL,
                monthly_dose REAL NOT NULL,
                monthly_cost REAL NOT NULL,
                dispense_month TEXT NOT NULL,
                clinic_id INTEGER NOT NULL,
                area_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES judicial_patients (id),
                FOREIGN KEY (clinic_id) REFERENCES clinics (id),
                FOREIGN KEY (area_id) REFERENCES areas (id)
            )''')

            conn.commit()
            print("✅ تم إنشاء جداول الأحكام القضائية بنجاح!")
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول الأحكام القضائية: {e}")
    finally:
        conn.close()

# تهيئة قاعدة البيانات عند بدء التشغيل
try:
    if not os.path.exists('instance/medicine_dispenser.db'):
        init_db()
        print("تم إنشاء قاعدة البيانات وتهيئتها بنجاح!")
    else:
        # التأكد من وجود جداول الأحكام القضائية
        ensure_judicial_tables()
except Exception as e:
    print(f"خطأ في تهيئة قاعدة البيانات: {e}")
    # إنشاء قاعدة بيانات بسيطة
    conn = get_db_connection()
    conn.execute('CREATE TABLE IF NOT EXISTS branches (id INTEGER PRIMARY KEY, name TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS drug_categories (id INTEGER PRIMARY KEY, name TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS areas (id INTEGER PRIMARY KEY, name TEXT, branch_id INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS clinics (id INTEGER PRIMARY KEY, name TEXT, area_id INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS drugs (id INTEGER PRIMARY KEY, name TEXT, scientific_name TEXT, category_id INTEGER, unit TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS dispensed (id INTEGER PRIMARY KEY, drug_id INTEGER, clinic_id INTEGER, dispense_month DATE)')
    conn.execute('CREATE TABLE IF NOT EXISTS dispensed_details (id INTEGER PRIMARY KEY, dispensed_id INTEGER, quantity REAL, price REAL, cases_count INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_dispensed (id INTEGER PRIMARY KEY, name TEXT, type TEXT, unit TEXT, cases_count INTEGER, quantity REAL, price REAL, cost REAL, rate REAL, balance REAL, category TEXT, clinic_id INTEGER, area_id INTEGER, dispense_month DATE, insulin_code_id INTEGER)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_codes (id INTEGER PRIMARY KEY, code INTEGER, name TEXT, description TEXT, unit TEXT, type TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_types (id INTEGER PRIMARY KEY, name TEXT, description TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_categories (id INTEGER PRIMARY KEY, name TEXT, description TEXT)')
    conn.execute('CREATE TABLE IF NOT EXISTS insulin_units (id INTEGER PRIMARY KEY, name TEXT UNIQUE, description TEXT, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)')
    conn.execute('CREATE TABLE IF NOT EXISTS drug_groups (id INTEGER PRIMARY KEY, name TEXT, cost REAL, clinic_id INTEGER, area_id INTEGER, dispense_month DATE, group_code_id INTEGER)')

    # جداول الأحكام القضائية
    conn.execute('''CREATE TABLE IF NOT EXISTS judicial_patients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        diagnosis TEXT,
        court_ruling_date DATE NOT NULL,
        treatment_start_date DATE NOT NULL,
        clinic_id INTEGER NOT NULL,
        area_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics (id),
        FOREIGN KEY (area_id) REFERENCES areas (id)
    )''')

    conn.execute('''CREATE TABLE IF NOT EXISTS judicial_medicines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        unit TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )''')

    conn.execute('''CREATE TABLE IF NOT EXISTS judicial_dispensed (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        patient_id INTEGER NOT NULL,
        diagnosis TEXT NOT NULL,
        medicine_name TEXT NOT NULL,
        unit TEXT NOT NULL,
        unit_price REAL NOT NULL,
        monthly_dose REAL NOT NULL,
        monthly_cost REAL NOT NULL,
        dispense_month TEXT NOT NULL,
        clinic_id INTEGER NOT NULL,
        area_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patient_id) REFERENCES judicial_patients (id),
        FOREIGN KEY (clinic_id) REFERENCES clinics (id),
        FOREIGN KEY (area_id) REFERENCES areas (id)
    )''')

    # جداول الأحكام القضائية
    conn.execute('''CREATE TABLE IF NOT EXISTS judicial_patients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        court_ruling_date DATE NOT NULL,
        treatment_start_date DATE NOT NULL,
        clinic_id INTEGER NOT NULL,
        area_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (clinic_id) REFERENCES clinics (id),
        FOREIGN KEY (area_id) REFERENCES areas (id)
    )''')

    conn.execute('''CREATE TABLE IF NOT EXISTS judicial_dispensed (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        patient_id INTEGER NOT NULL,
        diagnosis TEXT NOT NULL,
        medicine_name TEXT NOT NULL,
        unit TEXT NOT NULL,
        unit_price REAL NOT NULL,
        monthly_dose REAL NOT NULL,
        monthly_cost REAL NOT NULL,
        dispense_month TEXT NOT NULL,
        clinic_id INTEGER NOT NULL,
        area_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patient_id) REFERENCES judicial_patients (id),
        FOREIGN KEY (clinic_id) REFERENCES clinics (id),
        FOREIGN KEY (area_id) REFERENCES areas (id)
    )''')

    # جدول أدوية أحكام المحكمة
    conn.execute('''CREATE TABLE IF NOT EXISTS judicial_medicines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        unit TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )''')
    conn.execute('CREATE TABLE IF NOT EXISTS drug_group_codes (id INTEGER PRIMARY KEY, code INTEGER, name TEXT, description TEXT)')

    # التأكد من إنشاء جداول الأحكام القضائية
    conn.commit()
    print("تم إنشاء جداول الأحكام القضائية")

    # إضافة بيانات افتراضية للاختبار
    try:
        # فروع افتراضية
        conn.execute("INSERT OR IGNORE INTO branches (id, name) VALUES (1, 'الفرع الرئيسي')")
        conn.execute("INSERT OR IGNORE INTO branches (id, name) VALUES (2, 'فرع المدينة')")

        # مناطق افتراضية
        conn.execute("INSERT OR IGNORE INTO areas (id, name, branch_id) VALUES (1, 'منطقة الشمال', 1)")
        conn.execute("INSERT OR IGNORE INTO areas (id, name, branch_id) VALUES (2, 'منطقة الجنوب', 1)")
        conn.execute("INSERT OR IGNORE INTO areas (id, name, branch_id) VALUES (3, 'منطقة الوسط', 2)")

        # عيادات افتراضية
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (1, 'عيادة الباطنة', 1)")
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (2, 'عيادة الأطفال', 1)")
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (3, 'عيادة القلب', 2)")
        conn.execute("INSERT OR IGNORE INTO clinics (id, name, area_id) VALUES (4, 'عيادة السكر', 3)")

        # تصنيفات الأدوية
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (1, 'مضادات حيوية')")
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (2, 'مسكنات')")
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (3, 'أدوية القلب')")
        conn.execute("INSERT OR IGNORE INTO drug_categories (id, name) VALUES (4, 'أدوية السكر')")

        # أدوية افتراضية
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (1, 'أموكسيسيلين', 'Amoxicillin', 1)")
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (2, 'باراسيتامول', 'Paracetamol', 2)")
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (3, 'كونكور', 'Concor', 3)")
        conn.execute("INSERT OR IGNORE INTO drugs (id, name, scientific_name, category_id) VALUES (4, 'جلوكوفاج', 'Glucophage', 4)")

        # أنواع الأنسولين
        conn.execute("INSERT OR IGNORE INTO insulin_types (id, name, description) VALUES (1, 'أنسولين سريع المفعول', 'يبدأ مفعوله خلال 15 دقيقة')")
        conn.execute("INSERT OR IGNORE INTO insulin_types (id, name, description) VALUES (2, 'أنسولين متوسط المفعول', 'يبدأ مفعوله خلال 1-2 ساعة')")
        conn.execute("INSERT OR IGNORE INTO insulin_types (id, name, description) VALUES (3, 'أنسولين طويل المفعول', 'يستمر مفعوله 24 ساعة')")

        # فئات الأنسولين
        conn.execute("INSERT OR IGNORE INTO insulin_categories (id, name, description) VALUES (1, 'أنسولين بشري', 'أنسولين مصنع ليشبه الأنسولين البشري')")
        conn.execute("INSERT OR IGNORE INTO insulin_categories (id, name, description) VALUES (2, 'أنسولين تناظري', 'أنسولين معدل وراثياً')")

        # وحدات الأنسولين
        conn.execute("INSERT OR IGNORE INTO insulin_units (id, name, description) VALUES (1, 'فيال', 'فيال أنسولين')")
        conn.execute("INSERT OR IGNORE INTO insulin_units (id, name, description) VALUES (2, 'قلم', 'قلم أنسولين')")
        conn.execute("INSERT OR IGNORE INTO insulin_units (id, name, description) VALUES (3, 'خرطوشة', 'خرطوشة أنسولين')")
        conn.execute("INSERT OR IGNORE INTO insulin_units (id, name, description) VALUES (4, 'علبة', 'علبة أنسولين')")
        conn.execute("INSERT OR IGNORE INTO insulin_units (id, name, description) VALUES (5, 'أمبولة', 'أمبولة أنسولين')")
        conn.execute("INSERT OR IGNORE INTO insulin_units (id, name, description) VALUES (6, 'حقنة معبأة', 'حقنة معبأة مسبقاً')")

        # الأعمدة unit و type موجودة الآن في جدول insulin_codes
        print("جدول insulin_codes محدث مع الأعمدة الجديدة")

        # أكواد الأنسولين مع الوحدة والنوع
        conn.execute("INSERT OR IGNORE INTO insulin_codes (id, code, name, description, unit, type) VALUES (100, 100, 'نوفورابيد', 'أنسولين سريع المفعول', 'قلم', 'أنسولين سريع المفعول')")
        conn.execute("INSERT OR IGNORE INTO insulin_codes (id, code, name, description, unit, type) VALUES (200, 200, 'لانتوس', 'أنسولين طويل المفعول', 'فيال', 'أنسولين طويل المفعول')")
        conn.execute("INSERT OR IGNORE INTO insulin_codes (id, code, name, description, unit, type) VALUES (300, 300, 'هيومالوج', 'أنسولين سريع المفعول', 'خرطوشة', 'أنسولين سريع المفعول')")
        conn.execute("INSERT OR IGNORE INTO insulin_codes (id, code, name, description, unit, type) VALUES (400, 400, 'ليفيمير', 'أنسولين متوسط المفعول', 'قلم', 'أنسولين متوسط المفعول')")

        # أكواد مجموعات الأدوية
        conn.execute("INSERT OR IGNORE INTO drug_group_codes (id, code, name, description) VALUES (1, 100, 'مجموعة القلب', 'أدوية القلب والأوعية الدموية')")
        conn.execute("INSERT OR IGNORE INTO drug_group_codes (id, code, name, description) VALUES (2, 200, 'مجموعة السكر', 'أدوية السكري')")
        conn.execute("INSERT OR IGNORE INTO drug_group_codes (id, code, name, description) VALUES (3, 300, 'مجموعة المضادات', 'المضادات الحيوية')")

        # بيانات تجريبية للمنصرف
        current_month = datetime.now().strftime('%Y-%m-01')

        # منصرف أدوية تجريبي
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (1, 1, 1, ?)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (2, 2, 2, ?)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (3, 3, 3, ?)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO dispensed (id, drug_id, clinic_id, dispense_month) VALUES (4, 4, 4, ?)", (current_month,))

        # تفاصيل المنصرف
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (1, 1, 100, 5.50, 10)")
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (2, 2, 200, 3.25, 20)")
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (3, 3, 150, 12.75, 15)")
        conn.execute("INSERT OR IGNORE INTO dispensed_details (id, dispensed_id, quantity, price, cases_count) VALUES (4, 4, 300, 8.90, 30)")

        # منصرف أنسولين تجريبي لفروع مختلفة
        # فرع القاهرة (branch_id=1)
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (1, 'نوفورابيد', 'سريع المفعول', 'وحدة', 8, 800, 25.50, 204.00, 1.0, 0, 'بشري', 1, 1, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (2, 'لانتوس', 'طويل المفعول', 'وحدة', 6, 600, 45.75, 274.50, 1.0, 0, 'تناظري', 1, 1, ?, 2)", (current_month,))

        # فرع الجيزة (branch_id=2)
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (3, 'هيومالوج', 'سريع المفعول', 'وحدة', 4, 400, 28.75, 115.00, 1.0, 0, 'تناظري', 3, 2, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (4, 'ليفيمير', 'متوسط المفعول', 'وحدة', 3, 350, 38.25, 133.88, 1.0, 0, 'تناظري', 3, 2, ?, 2)", (current_month,))

        # فرع الإسكندرية (branch_id=3)
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (5, 'أبيدرا', 'سريع المفعول', 'وحدة', 2, 250, 32.50, 81.25, 1.0, 0, 'تناظري', 5, 3, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO insulin_dispensed (id, name, type, unit, cases_count, quantity, price, cost, rate, balance, category, clinic_id, area_id, dispense_month, insulin_code_id) VALUES (6, 'تريسيبا', 'طويل المفعول', 'وحدة', 2, 180, 52.75, 105.50, 1.0, 0, 'تناظري', 5, 3, ?, 2)", (current_month,))

        # مجموعات دوائية تجريبية لفروع مختلفة
        # فرع القاهرة (branch_id=1)
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (1, 'مجموعة أدوية القلب المتقدمة', 1850.75, 1, 1, ?, 1)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (2, 'مجموعة أدوية الضغط العالي', 1320.50, 1, 1, ?, 1)", (current_month,))

        # فرع الجيزة (branch_id=2)
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (3, 'مجموعة أدوية السكر المتطورة', 1150.25, 3, 2, ?, 2)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (4, 'مجموعة أدوية الكولسترول', 890.75, 3, 2, ?, 2)", (current_month,))

        # فرع الإسكندرية (branch_id=3)
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (5, 'مجموعة المضادات الحيوية', 750.50, 5, 3, ?, 3)", (current_month,))
        conn.execute("INSERT OR IGNORE INTO drug_groups (id, name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (6, 'مجموعة أدوية الجهاز التنفسي', 620.25, 5, 3, ?, 3)", (current_month,))

        print("تم إضافة البيانات الافتراضية والتجريبية بنجاح")
    except Exception as e:
        print(f"خطأ في إضافة البيانات الافتراضية: {e}")

    conn.commit()
    conn.close()
    print("تم إنشاء قاعدة بيانات بسيطة مع بيانات افتراضية")

# المسارات الرئيسية
@app.route('/')
def index():
    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('index.html', branches=branches)

@app.route('/copyright')
def copyright():
    current_year = datetime.now().year
    return render_template('copyright.html', current_year=current_year)

@app.route('/designer')
def designer():
    return render_template('designer.html')

# مسارات إدارة الفروع
@app.route('/manage/branches', methods=['GET', 'POST'])
def manage_branches():
    if request.method == 'POST':
        name = request.form.get('name')
        if name:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO branches (name) VALUES (?)', (name,))
                conn.commit()
                flash('تم إضافة الفرع بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الفرع موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم الفرع', 'danger')

    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('manage_branches.html', branches=branches)

# حذف وتعديل الفروع
@app.route('/manage/branches/<int:branch_id>/delete', methods=['POST'])
def delete_branch(branch_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM branches WHERE id = ?', (branch_id,))
        conn.commit()
        flash('تم حذف الفرع بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف الفرع: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_branches'))

@app.route('/manage/branches/update', methods=['POST'])
def update_branch():
    branch_id = request.form.get('branch_id')
    name = request.form.get('name')

    if branch_id and name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE branches SET name = ? WHERE id = ?', (name, branch_id))
            conn.commit()
            flash('تم تحديث الفرع بنجاح', 'success')
        except sqlite3.IntegrityError:
            flash('هذا الفرع موجود بالفعل', 'danger')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث الفرع: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم الفرع', 'danger')
    return redirect(url_for('manage_branches'))

# مسارات إدارة المناطق
@app.route('/manage/areas', methods=['GET', 'POST'])
def manage_areas():
    if request.method == 'POST':
        name = request.form.get('name')
        branch_id = request.form.get('branch_id')
        if name and branch_id:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO areas (name, branch_id) VALUES (?, ?)', (name, branch_id))
                conn.commit()
                flash('تم إضافة المنطقة بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذه المنطقة موجودة بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم المنطقة والفرع', 'danger')

    conn = get_db_connection()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    conn.close()
    return render_template('manage_areas.html', areas=areas, branches=branches)

# حذف وتعديل المناطق
@app.route('/manage/areas/<int:area_id>/delete', methods=['POST'])
def delete_area(area_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM areas WHERE id = ?', (area_id,))
        conn.commit()
        flash('تم حذف المنطقة بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف المنطقة: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_areas'))

@app.route('/manage/areas/update', methods=['POST'])
def update_area():
    area_id = request.form.get('area_id')
    name = request.form.get('name')
    branch_id = request.form.get('branch_id')

    if area_id and name and branch_id:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE areas SET name = ?, branch_id = ? WHERE id = ?',
                       (name, branch_id, area_id))
            conn.commit()
            flash('تم تحديث المنطقة بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث المنطقة: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
    return redirect(url_for('manage_areas'))

# مسارات إدارة العيادات
@app.route('/manage/clinics', methods=['GET', 'POST'])
def manage_clinics():
    if request.method == 'POST':
        name = request.form.get('name')
        area_id = request.form.get('area_id')
        if name and area_id:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO clinics (name, area_id) VALUES (?, ?)', (name, area_id))
                conn.commit()
                flash('تم إضافة العيادة بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذه العيادة موجودة بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم العيادة والمنطقة', 'danger')

    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    areas = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    conn.close()
    return render_template('manage_clinics.html', clinics=clinics, areas=areas)

# حذف وتعديل العيادات
@app.route('/manage/clinics/<int:clinic_id>/delete', methods=['POST'])
def delete_clinic(clinic_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM clinics WHERE id = ?', (clinic_id,))
        conn.commit()
        flash('تم حذف العيادة بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف العيادة: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_clinics'))

@app.route('/manage/clinics/update', methods=['POST'])
def update_clinic():
    clinic_id = request.form.get('clinic_id')
    name = request.form.get('name')
    area_id = request.form.get('area_id')

    if clinic_id and name and area_id:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE clinics SET name = ?, area_id = ? WHERE id = ?',
                       (name, area_id, clinic_id))
            conn.commit()
            flash('تم تحديث العيادة بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث العيادة: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
    return redirect(url_for('manage_clinics'))

# مسارات إدارة تصنيفات الأدوية
@app.route('/manage/drug_categories_new', methods=['GET', 'POST'])
def manage_drug_categories_new():
    if request.method == 'POST':
        name = request.form.get('name')
        if name:
            conn = get_db_connection()
            try:
                conn.execute('INSERT INTO drug_categories (name) VALUES (?)', (name,))
                conn.commit()
                flash('تم إضافة التصنيف بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا التصنيف موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم التصنيف', 'danger')

    conn = get_db_connection()
    categories = conn.execute('''
        SELECT
            dc.id,
            dc.name,
            COUNT(d.id) as drug_count
        FROM
            drug_categories dc
        LEFT JOIN
            drugs d ON dc.id = d.category_id
        GROUP BY
            dc.id, dc.name
        ORDER BY
            dc.name
    ''').fetchall()
    conn.close()
    return render_template('manage_drug_categories_new.html', categories=categories)

# مسار تحديث تصنيف الأدوية
@app.route('/manage/drug_categories/update', methods=['POST'])
def update_drug_category():
    category_id = request.form.get('category_id')
    name = request.form.get('name')

    if category_id and name:
        conn = get_db_connection()
        try:
            conn.execute('UPDATE drug_categories SET name = ? WHERE id = ?', (name, category_id))
            conn.commit()
            flash('تم تحديث التصنيف بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث التصنيف: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم التصنيف', 'danger')

    return redirect(url_for('manage_drug_categories_new'))

@app.route('/manage/drug_categories/delete/<int:category_id>', methods=['POST'])
def delete_drug_category(category_id):
    conn = get_db_connection()
    try:
        # التحقق من وجود أدوية مرتبطة بهذا التصنيف
        drugs_count = conn.execute('SELECT COUNT(*) FROM drugs WHERE category_id = ?', (category_id,)).fetchone()[0]

        if drugs_count > 0:
            flash(f'لا يمكن حذف هذا التصنيف لأنه مرتبط بـ {drugs_count} دواء', 'warning')
        else:
            conn.execute('DELETE FROM drug_categories WHERE id = ?', (category_id,))
            conn.commit()
            flash('تم حذف التصنيف بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف التصنيف: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_drug_categories_new'))

# مسارات إدارة الأدوية
@app.route('/manage/drugs/new', methods=['GET', 'POST'])
def manage_drugs_new():
    if request.method == 'POST':
        name = request.form.get('name')
        scientific_name = request.form.get('scientific_name')
        category_id = request.form.get('category_id')
        unit = request.form.get('unit')

        if name and category_id:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO drugs (name, scientific_name, category_id, unit) VALUES (?, ?, ?, ?)',
                    (name, scientific_name, category_id, unit)
                )
                conn.commit()
                flash('تم إضافة الدواء بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الدواء موجود بالفعل', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال اسم الدواء والتصنيف', 'danger')

    conn = get_db_connection()
    drugs = conn.execute('''
        SELECT drugs.*, drug_categories.name as category_name
        FROM drugs
        JOIN drug_categories ON drugs.category_id = drug_categories.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    conn.close()
    return render_template('manage_drugs_new.html', drugs=drugs, categories=categories)

@app.route('/manage/drugs/delete/<int:drug_id>', methods=['POST'])
def delete_drug(drug_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM drugs WHERE id = ?', (drug_id,))
        conn.commit()
        flash('تم حذف الدواء بنجاح', 'success')
    except:
        flash('حدث خطأ أثناء حذف الدواء', 'danger')
    finally:
        conn.close()
    return redirect(url_for('manage_drugs_new'))

# مسارات صرف الأدوية
@app.route('/dispense/new', methods=['GET', 'POST'])
def dispense_new():
    print(f"=== تم الوصول لدالة dispense_new - Method: {request.method} ===")
    if request.method == 'POST':
        print("=== تم استلام طلب POST لصرف الأدوية ===")
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        items_data = request.form.get('items_data')
        total_cost = request.form.get('total_cost')

        print(f"بيانات الطلب: clinic_id={clinic_id}, dispense_month={dispense_month}")
        print(f"items_data={items_data}")
        print(f"total_cost={total_cost}")

        if clinic_id and dispense_month and items_data:
            try:
                # تحويل البيانات من JSON إلى قائمة
                import json
                items = json.loads(items_data)

                print(f"عدد العناصر: {len(items)}")

                if not items:
                    flash('يرجى إضافة عنصر واحد على الأقل إلى القائمة', 'danger')
                    return redirect(url_for('dispense_new'))

                # تحويل التاريخ
                dispense_date = f"{dispense_month}-01"
                print(f"تاريخ الصرف المحول: {dispense_date}")

                conn = get_db_connection()

                # الحصول على معرف المنطقة من العيادة
                area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                if not area_result:
                    flash('العيادة المحددة غير موجودة', 'danger')
                    return redirect(url_for('dispense_new'))

                area_id = area_result['area_id']

                # إضافة كل عنصر في القائمة
                skipped_items = []  # قائمة العناصر المتخطاة
                added_items = []    # قائمة العناصر المضافة

                print(f"=== بدء معالجة {len(items)} عنصر ===")
                for item in items:
                    print(f"معالجة العنصر: {item}")

                    # التحقق من عدم تكرار الصنف بنفس الكمية والسعر في نفس العيادة والشهر
                    print(f"البحث عن تكرار: drug_id={item['drug_id']}, clinic_id={clinic_id}, dispense_date={dispense_date}")
                    print(f"الكمية الجديدة: {item['quantity']}, السعر الجديد: {item['price']}")

                    # البحث عن جميع السجلات المطابقة (ليس فقط الأول)
                    existing_records = conn.execute('''
                        SELECT d.id, dd.quantity, dd.price, dr.name as drug_name
                        FROM dispensed d
                        JOIN dispensed_details dd ON d.id = dd.dispensed_id
                        JOIN drugs dr ON d.drug_id = dr.id
                        WHERE d.drug_id = ? AND d.clinic_id = ? AND d.dispense_month = ?
                    ''', (item['drug_id'], clinic_id, dispense_date)).fetchall()

                    print(f"عدد السجلات الموجودة: {len(existing_records)}")

                    # التحقق من وجود تطابق تام في الكمية والسعر
                    duplicate_found = False
                    for existing_record in existing_records:
                        print(f"فحص سجل: الكمية={existing_record['quantity']}, السعر={existing_record['price']}")
                        if (float(existing_record['quantity']) == float(item['quantity']) and
                            float(existing_record['price']) == float(item['price'])):
                            # نفس الكمية والسعر - منع التكرار
                            drug_name = existing_record['drug_name']
                            print(f"تم منع تكرار الدواء: {drug_name} - نفس الكمية والسعر")
                            skipped_items.append(drug_name)
                            duplicate_found = True
                            break

                    if duplicate_found:
                        continue  # تخطي هذا العنصر

                    if existing_records:
                        print("السماح بتكرار الدواء - كمية أو سعر مختلف")
                    else:
                        print("لا يوجد سجل مكرر - السماح بالإضافة")

                    # إنشاء سجل المنصرف
                    conn.execute(
                        'INSERT INTO dispensed (drug_id, clinic_id, dispense_month) VALUES (?, ?, ?)',
                        (item['drug_id'], clinic_id, dispense_date)
                    )

                    # الحصول على معرف السجل المضاف
                    dispensed_id = conn.execute('SELECT last_insert_rowid()').fetchone()[0]

                    # إضافة تفاصيل المنصرف
                    conn.execute(
                        'INSERT INTO dispensed_details (dispensed_id, quantity, price, cases_count) VALUES (?, ?, ?, ?)',
                        (dispensed_id, float(item['quantity']), float(item['price']), int(item['cases_count']))
                    )

                    # إضافة اسم الدواء للعناصر المضافة
                    drug_name = conn.execute('SELECT name FROM drugs WHERE id = ?', (item['drug_id'],)).fetchone()['name']
                    added_items.append(drug_name)

                conn.commit()
                conn.close()

                # إنشاء رسائل مناسبة
                print(f"=== ملخص العملية ===")
                print(f"العناصر المضافة: {len(added_items)} - {added_items}")
                print(f"العناصر المتخطاة: {len(skipped_items)} - {skipped_items}")
                print(f"إجمالي العناصر المرسلة: {len(items)}")

                messages = []
                if added_items:
                    messages.append(f'تم تسجيل {len(added_items)} عنصر بنجاح')
                    print(f"تم حفظ {len(added_items)} عنصر بنجاح: {added_items}")

                if skipped_items:
                    messages.append(f'تم تخطي {len(skipped_items)} عنصر مكرر (نفس الكمية والسعر): {", ".join(skipped_items)}')
                    print(f"تم تخطي العناصر المكررة: {skipped_items}")

                # عرض الرسائل
                for message in messages:
                    if 'تخطي' in message:
                        flash(message, 'warning')
                    else:
                        flash(message, 'success')

                # إرجاع استجابة JSON للـ AJAX
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'success': True,
                        'message': ' | '.join(messages),
                        'added_count': len(added_items),
                        'skipped_count': len(skipped_items)
                    })

                return redirect(url_for('dispense_new'))

            except json.JSONDecodeError as e:
                print(f"خطأ في تحليل JSON: {e}")
                flash('حدث خطأ في تنسيق البيانات', 'danger')
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': 'خطأ في تنسيق البيانات'}), 400
                return redirect(url_for('dispense_new'))
            except Exception as e:
                print(f"خطأ في حفظ البيانات: {e}")
                flash(f'حدث خطأ: {str(e)}', 'danger')
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': str(e)}), 500
                return redirect(url_for('dispense_new'))
        else:
            print("بيانات مفقودة")
            flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': 'بيانات مفقودة'}), 400
            return redirect(url_for('dispense_new'))

    # جلب البيانات اللازمة للعرض
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()

    # جلب معاملات الفلترة
    filter_clinic = request.args.get('filter_clinic')
    filter_category = request.args.get('filter_category')
    filter_month = request.args.get('filter_month')

    # بناء شروط الفلترة
    filter_conditions = []
    filter_params = []

    if filter_clinic:
        filter_conditions.append("d.clinic_id = ?")
        filter_params.append(filter_clinic)

    if filter_category:
        filter_conditions.append("dr.category_id = ?")
        filter_params.append(filter_category)

    if filter_month:
        filter_conditions.append("strftime('%Y-%m', d.dispense_month) = ?")
        filter_params.append(filter_month)

    # بناء الاستعلام مع الفلاتر
    where_clause = ""
    if filter_conditions:
        where_clause = "WHERE " + " AND ".join(filter_conditions)

    dispensed_items_raw = conn.execute(f'''
        SELECT
            d.id,
            c.name as clinic_name,
            dr.name as drug_name,
            dr.unit as drug_unit,
            dc.name as category_name,
            dd.quantity,
            dd.price,
            dd.cases_count,
            (dd.quantity * dd.price) as total_cost,
            d.dispense_month,
            strftime('%m/%Y', d.dispense_month) as formatted_date
        FROM dispensed d
        JOIN clinics c ON d.clinic_id = c.id
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN drug_categories dc ON dr.category_id = dc.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        {where_clause}
        ORDER BY d.id DESC
        LIMIT 50
    ''', filter_params).fetchall()

    # إضافة معلومات إمكانية التعديل/الحذف لكل سجل
    dispensed_items = []
    for item in dispensed_items_raw:
        item_dict = dict(item)
        can_modify, months_diff = can_edit_or_delete(item['dispense_month'])
        item_dict['can_modify'] = can_modify
        item_dict['months_diff'] = months_diff
        dispensed_items.append(item_dict)

    conn.close()
    
    # جلب معرف العيادة من المعاملات إذا تم تمريره
    selected_clinic_id = request.args.get('clinic_id')
    
    return render_template('dispense_new.html',
                         clinics=clinics,
                         categories=categories,
                         dispensed_items=dispensed_items,
                         selected_clinic_id=selected_clinic_id)

# مسار صرف الأنسولين
@app.route('/insulin/dispense', methods=['GET', 'POST'])
def insulin_dispense():
    if request.method == 'POST':
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        items_data = request.form.get('items_data')

        if all([clinic_id, dispense_month, items_data]):
            try:
                items = json.loads(items_data)
                dispense_date = f"{dispense_month}-01"

                conn = get_db_connection()
                area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()['area_id']

                # قوائم لتتبع العناصر المضافة والمتخطاة
                added_items = []
                skipped_items = []

                for item in items:
                    # التحقق من عدم تكرار الصنف بنفس الكمية والسعر في نفس العيادة والشهر
                    existing_record = conn.execute('''
                        SELECT id FROM insulin_dispensed
                        WHERE name = ? AND clinic_id = ? AND dispense_month = ?
                        AND quantity = ? AND price = ?
                    ''', (item['name'], clinic_id, dispense_date, item['quantity'], item['price'])).fetchone()

                    if existing_record:
                        # تخطي العنصر المكرر
                        skipped_items.append(item['name'])
                        print(f"تم تخطي الأنسولين المكرر: {item['name']} - نفس الكمية والسعر")
                        continue

                    # إضافة العنصر إذا لم يكن مكرراً
                    conn.execute(
                        '''INSERT INTO insulin_dispensed
                           (name, type, unit, cases_count, quantity, price, cost, rate, balance, category,
                            clinic_id, area_id, dispense_month, insulin_code_id)
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                        (item['name'], item['type'], item['unit'], item['cases_count'], item['quantity'],
                         item['price'], item['cost'], item['rate'], item['balance'], item['category'],
                         clinic_id, area_id, dispense_date, item.get('insulin_code_id'))
                    )
                    added_items.append(item['name'])

                conn.commit()

                # إنشاء رسائل مناسبة
                messages = []
                if added_items:
                    messages.append(f'تم إضافة {len(added_items)} من الأنسولين بنجاح')
                    print(f"تم حفظ {len(added_items)} عنصر بنجاح: {added_items}")

                if skipped_items:
                    messages.append(f'تم تخطي {len(skipped_items)} عنصر مكرر (نفس الكمية والسعر): {", ".join(skipped_items)}')
                    print(f"تم تخطي العناصر المكررة: {skipped_items}")

                # عرض الرسائل
                for message in messages:
                    if 'تخطي' in message:
                        flash(message, 'warning')
                    else:
                        flash(message, 'success')

                # إرجاع استجابة JSON للـ AJAX
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'success': True,
                        'message': ' | '.join(messages),
                        'added_count': len(added_items),
                        'skipped_count': len(skipped_items),
                        'added_items': added_items,
                        'skipped_items': skipped_items
                    })

            except Exception as e:
                error_msg = f'حدث خطأ: {str(e)}'
                flash(error_msg, 'danger')
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': error_msg}), 500
            finally:
                conn.close()
        else:
            error_msg = 'يرجى إدخال جميع البيانات المطلوبة'
            flash(error_msg, 'danger')
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': error_msg}), 400
        return redirect(url_for('insulin_dispense'))

    # جلب البيانات اللازمة للعرض
    conn = get_db_connection()
    branches = conn.execute('SELECT * FROM branches').fetchall()
    insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()
    insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()
    insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()

    # جلب قائمة العيادات للفلترة
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name, clinics.name
    ''').fetchall()

    # جلب معاملات الفلترة
    filter_clinic = request.args.get('filter_clinic')
    filter_type = request.args.get('filter_type')
    filter_month = request.args.get('filter_month')

    # بناء شروط الفلترة
    filter_conditions = []
    filter_params = []

    if filter_clinic:
        filter_conditions.append("i.clinic_id = ?")
        filter_params.append(filter_clinic)

    if filter_type:
        filter_conditions.append("i.type = ?")
        filter_params.append(filter_type)

    if filter_month:
        filter_conditions.append("strftime('%Y-%m', i.dispense_month) = ?")
        filter_params.append(filter_month)

    # بناء الاستعلام مع الفلاتر
    where_clause = ""
    if filter_conditions:
        where_clause = "WHERE " + " AND ".join(filter_conditions)

    saved_items_raw = conn.execute(f'''
        SELECT
            i.*,
            c.name as clinic_name,
            a.name as area_name,
            b.name as branch_name,
            ic.name as code_name,
            ic.code as code_value,
            CASE
                WHEN i.dispense_month IS NOT NULL AND i.dispense_month != ''
                THEN strftime('%m/%Y', i.dispense_month || '-01')
                ELSE 'غير محدد'
            END as formatted_date
        FROM insulin_dispensed i
        JOIN clinics c ON i.clinic_id = c.id
        JOIN areas a ON i.area_id = a.id
        JOIN branches b ON a.branch_id = b.id
        LEFT JOIN insulin_codes ic ON i.insulin_code_id = ic.id
        {where_clause}
        ORDER BY i.dispense_month DESC
        LIMIT 100
    ''', filter_params).fetchall()

    # إضافة معلومات إمكانية التعديل/الحذف لكل سجل
    saved_items = []
    for item in saved_items_raw:
        item_dict = dict(item)
        can_modify, months_diff = can_edit_or_delete(item['dispense_month'])
        item_dict['can_modify'] = can_modify
        item_dict['months_diff'] = months_diff
        saved_items.append(item_dict)

    conn.close()

    return render_template('insulin_dispense.html',
                         branches=branches,
                         clinics=clinics,
                         insulin_codes=insulin_codes,
                         insulin_types=insulin_types,
                         insulin_categories=insulin_categories,
                         saved_items=saved_items)

# مسار تحديث منصرف الأنسولين
@app.route('/insulin/update', methods=['POST'])
def update_insulin_dispensed():
    """تحديث بيانات منصرف الأنسولين"""

    insulin_id = request.form.get('insulin_id')
    name = request.form.get('name')
    type_name = request.form.get('type')
    unit = request.form.get('unit')
    quantity = request.form.get('quantity')
    cases_count = request.form.get('cases_count')
    price = request.form.get('price')
    cost = request.form.get('cost')
    rate = request.form.get('rate')
    balance = request.form.get('balance')
    category = request.form.get('category')
    clinic_id = request.form.get('clinic_id')
    dispense_month = request.form.get('dispense_month')
    insulin_code_id = request.form.get('insulin_code_id')



    if insulin_id and name and type_name and unit and quantity and price and clinic_id and dispense_month:
        conn = get_db_connection()
        try:
            # التحقق من وجود السجل والحصول على تاريخ الصرف
            insulin_record = conn.execute(
                'SELECT dispense_month FROM insulin_dispensed WHERE id = ?',
                (insulin_id,)
            ).fetchone()

            if not insulin_record:
                flash('السجل غير موجود', 'danger')
                return redirect(url_for('insulin_dispense'))

            # التحقق من إمكانية التعديل
            can_modify, months_diff = can_edit_or_delete(insulin_record['dispense_month'])
            if not can_modify:
                flash(f'لا يمكن تعديل هذا السجل. مر عليه {months_diff} أشهر (الحد الأقصى 4 أشهر)', 'danger')
                return redirect(url_for('insulin_dispense'))

            # الحصول على area_id من clinic_id
            area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
            if not area_result:
                flash('العيادة المحددة غير موجودة', 'danger')
                return redirect(url_for('insulin_dispense'))

            area_id = area_result[0]

            # تحويل التاريخ إلى تنسيق قاعدة البيانات
            dispense_date = f"{dispense_month}-01"

            # حساب التكلفة إذا لم تكن موجودة
            if not cost:
                cost = float(quantity) * float(price)



            # تحديث بيانات الأنسولين
            conn.execute('''
                UPDATE insulin_dispensed
                SET name = ?, type = ?, unit = ?, quantity = ?, cases_count = ?,
                    price = ?, cost = ?, rate = ?, balance = ?, category = ?,
                    clinic_id = ?, area_id = ?, dispense_month = ?, insulin_code_id = ?
                WHERE id = ?
            ''', (name, type_name, unit, float(quantity), int(cases_count),
                  float(price), float(cost), float(rate or 0), float(balance),
                  category, int(clinic_id), area_id, dispense_date,
                  int(insulin_code_id) if insulin_code_id else None, int(insulin_id)))

            conn.commit()
            flash('تم تحديث بيانات الأنسولين بنجاح', 'success')

        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        missing_fields = []
        if not insulin_id: missing_fields.append('معرف الأنسولين')
        if not name: missing_fields.append('الاسم')
        if not type_name: missing_fields.append('النوع')
        if not unit: missing_fields.append('الوحدة')
        if not quantity: missing_fields.append('الكمية')
        if not price: missing_fields.append('السعر')
        if not clinic_id: missing_fields.append('العيادة')
        if not dispense_month: missing_fields.append('شهر الصرف')

        flash(f'يرجى إدخال البيانات المطلوبة: {", ".join(missing_fields)}', 'danger')

    return redirect(url_for('insulin_dispense'))

# مسار حذف منصرف الأنسولين
@app.route('/insulin/<int:insulin_id>/delete', methods=['POST'])
def delete_insulin_dispensed(insulin_id):
    """حذف منصرف الأنسولين"""

    conn = get_db_connection()
    try:
        # الحصول على معلومات السجل قبل الحذف
        insulin_info = conn.execute(
            'SELECT name, dispense_month FROM insulin_dispensed WHERE id = ?',
            (insulin_id,)
        ).fetchone()

        if not insulin_info:
            flash('السجل المطلوب حذفه غير موجود', 'danger')
            return redirect(url_for('insulin_dispense'))

        insulin_name = insulin_info['name']
        dispense_month = insulin_info['dispense_month']

        # التحقق من إمكانية الحذف
        can_modify, months_diff = can_edit_or_delete(dispense_month)
        if not can_modify:
            flash(f'لا يمكن حذف هذا السجل. مر عليه {months_diff} أشهر (الحد الأقصى 4 أشهر)', 'danger')
            return redirect(url_for('insulin_dispense'))

        # حذف السجل
        conn.execute('DELETE FROM insulin_dispensed WHERE id = ?', (insulin_id,))
        conn.commit()

        flash(f'تم حذف "{insulin_name}" من منصرف الأنسولين بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('insulin_dispense'))

# مسارات الأحكام القضائية
@app.route('/judicial/dispense', methods=['GET', 'POST'])
def judicial_dispense():
    """صفحة منصرف أدوية الأحكام القضائية"""
    if request.method == 'POST':
        # إضافة مريض جديد أو دواء جديد
        action = request.form.get('action')

        if action == 'add_patient':
            # إضافة مريض جديد
            name = request.form.get('patient_name')
            diagnosis = request.form.get('patient_diagnosis')
            court_ruling_date = request.form.get('court_ruling_date')
            treatment_start_date = request.form.get('treatment_start_date')
            clinic_id = request.form.get('clinic_id')

            if name and court_ruling_date and treatment_start_date and clinic_id:
                conn = get_db_connection()
                try:
                    # الحصول على area_id من clinic_id
                    area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                    if not area_result:
                        flash('العيادة المحددة غير موجودة', 'danger')
                        return redirect(url_for('judicial_dispense'))

                    area_id = area_result[0]

                    # إضافة المريض
                    conn.execute('''
                        INSERT INTO judicial_patients (name, diagnosis, court_ruling_date, treatment_start_date, clinic_id, area_id)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (name, diagnosis, court_ruling_date, treatment_start_date, clinic_id, area_id))

                    conn.commit()
                    flash('تم إضافة المريض بنجاح', 'success')

                except Exception as e:
                    flash(f'حدث خطأ: {str(e)}', 'danger')
                finally:
                    conn.close()
            else:
                flash('يرجى إدخال جميع البيانات المطلوبة للمريض', 'danger')

        elif action == 'add_medicine':
            # إضافة دواء جديد
            patient_id = request.form.get('patient_id')
            diagnosis = request.form.get('diagnosis')
            medicine_name = request.form.get('medicine_name')
            unit = request.form.get('unit')
            unit_price = request.form.get('unit_price')
            monthly_dose = request.form.get('monthly_dose')
            dispense_month = request.form.get('dispense_month')
            clinic_id = request.form.get('clinic_id')

            if all([patient_id, diagnosis, medicine_name, unit, unit_price, monthly_dose, dispense_month, clinic_id]):
                conn = get_db_connection()
                try:
                    # الحصول على area_id من clinic_id
                    area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                    if not area_result:
                        flash('العيادة المحددة غير موجودة', 'danger')
                        return redirect(url_for('judicial_dispense'))

                    area_id = area_result[0]

                    # حساب التكلفة الشهرية
                    monthly_cost = float(unit_price) * float(monthly_dose)

                    # إضافة الدواء
                    conn.execute('''
                        INSERT INTO judicial_dispensed
                        (patient_id, diagnosis, medicine_name, unit, unit_price, monthly_dose, monthly_cost, dispense_month, clinic_id, area_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (patient_id, diagnosis, medicine_name, unit, unit_price, monthly_dose, monthly_cost, dispense_month, clinic_id, area_id))

                    conn.commit()
                    flash('تم إضافة الدواء بنجاح', 'success')

                except Exception as e:
                    flash(f'حدث خطأ: {str(e)}', 'danger')
                finally:
                    conn.close()
            else:
                flash('يرجى إدخال جميع البيانات المطلوبة للدواء', 'danger')

        return redirect(url_for('judicial_dispense'))

    # جلب البيانات للعرض
    conn = get_db_connection()

    # جلب العيادات
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name, clinics.name
    ''').fetchall()

    # جلب قائمة أدوية أحكام المحكمة
    judicial_medicines = conn.execute('''
        SELECT * FROM judicial_medicines
        ORDER BY name
    ''').fetchall()

    # جلب المرضى
    patients = conn.execute('''
        SELECT jp.*, c.name as clinic_name, a.name as area_name, b.name as branch_name
        FROM judicial_patients jp
        JOIN clinics c ON jp.clinic_id = c.id
        JOIN areas a ON jp.area_id = a.id
        JOIN branches b ON a.branch_id = b.id
        ORDER BY jp.name
    ''').fetchall()

    # جلب معاملات الفلترة
    filter_clinic = request.args.get('filter_clinic')
    filter_patient = request.args.get('filter_patient')
    filter_month = request.args.get('filter_month')

    # بناء شروط الفلترة للأدوية
    filter_conditions = []
    filter_params = []

    if filter_clinic:
        filter_conditions.append("jd.clinic_id = ?")
        filter_params.append(filter_clinic)

    if filter_patient:
        filter_conditions.append("jd.patient_id = ?")
        filter_params.append(filter_patient)

    if filter_month:
        filter_conditions.append("jd.dispense_month = ?")
        filter_params.append(filter_month)

    # بناء الاستعلام مع الفلاتر
    where_clause = ""
    if filter_conditions:
        where_clause = "WHERE " + " AND ".join(filter_conditions)

    # جلب الأدوية المصروفة مع الفلترة
    medicines = conn.execute(f'''
        SELECT jd.*, jp.name as patient_name, jp.court_ruling_date, jp.treatment_start_date,
               c.name as clinic_name, a.name as area_name, b.name as branch_name
        FROM judicial_dispensed jd
        JOIN judicial_patients jp ON jd.patient_id = jp.id
        JOIN clinics c ON jd.clinic_id = c.id
        JOIN areas a ON jd.area_id = a.id
        JOIN branches b ON a.branch_id = b.id
        {where_clause}
        ORDER BY jd.dispense_month DESC, jp.name
    ''', filter_params).fetchall()

    conn.close()

    return render_template('judicial_dispense.html',
                         clinics=clinics,
                         patients=patients,
                         medicines=medicines,
                         judicial_medicines=judicial_medicines)

# API للحصول على بيانات المريض
@app.route('/api/judicial/patient/<int:patient_id>')
def get_judicial_patient(patient_id):
    """الحصول على بيانات المريض"""
    conn = get_db_connection()
    try:
        patient = conn.execute('''
            SELECT jp.*, c.name as clinic_name
            FROM judicial_patients jp
            JOIN clinics c ON jp.clinic_id = c.id
            WHERE jp.id = ?
        ''', (patient_id,)).fetchone()

        if patient:
            # معالجة آمنة للتشخيص في حالة عدم وجود العمود
            try:
                diagnosis = patient['diagnosis'] if patient['diagnosis'] else ''
            except (KeyError, IndexError):
                diagnosis = ''

            return jsonify({
                'success': True,
                'patient': {
                    'id': patient['id'],
                    'name': patient['name'],
                    'diagnosis': diagnosis,
                    'court_ruling_date': patient['court_ruling_date'],
                    'treatment_start_date': patient['treatment_start_date'],
                    'clinic_id': patient['clinic_id'],
                    'clinic_name': patient['clinic_name']
                }
            })
        else:
            return jsonify({'success': False, 'error': 'المريض غير موجود'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
    finally:
        conn.close()

# إنشاء جداول الأحكام القضائية
@app.route('/judicial/create_tables')
def create_judicial_tables():
    """إنشاء جداول الأحكام القضائية"""
    conn = get_db_connection()
    try:
        # إنشاء جدول المرضى
        conn.execute('''CREATE TABLE IF NOT EXISTS judicial_patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            diagnosis TEXT,
            court_ruling_date DATE NOT NULL,
            treatment_start_date DATE NOT NULL,
            clinic_id INTEGER NOT NULL,
            area_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (clinic_id) REFERENCES clinics (id),
            FOREIGN KEY (area_id) REFERENCES areas (id)
        )''')

        # إضافة عمود التشخيص للجدول الموجود إذا لم يكن موجوداً
        try:
            conn.execute('ALTER TABLE judicial_patients ADD COLUMN diagnosis TEXT')
        except:
            pass  # العمود موجود بالفعل

        # إنشاء جدول أدوية أحكام المحكمة
        conn.execute('''CREATE TABLE IF NOT EXISTS judicial_medicines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            unit TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )''')

        # إنشاء جدول الأدوية المصروفة
        conn.execute('''CREATE TABLE IF NOT EXISTS judicial_dispensed (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id INTEGER NOT NULL,
            diagnosis TEXT NOT NULL,
            medicine_name TEXT NOT NULL,
            unit TEXT NOT NULL,
            unit_price REAL NOT NULL,
            monthly_dose REAL NOT NULL,
            monthly_cost REAL NOT NULL,
            dispense_month TEXT NOT NULL,
            clinic_id INTEGER NOT NULL,
            area_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES judicial_patients (id),
            FOREIGN KEY (clinic_id) REFERENCES clinics (id),
            FOREIGN KEY (area_id) REFERENCES areas (id)
        )''')

        conn.commit()
        flash('تم إنشاء جداول الأحكام القضائية بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ في إنشاء الجداول: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('judicial_dispense'))

# إضافة بيانات تجريبية للأحكام القضائية
@app.route('/judicial/add_sample_data')
def add_judicial_sample_data():
    """إضافة بيانات تجريبية للأحكام القضائية"""
    conn = get_db_connection()
    try:
        # إضافة مرضى تجريبيين
        sample_patients = [
            ('أحمد محمد علي', 'ارتفاع ضغط الدم', '2024-01-15', '2024-02-01', 1, 1),
            ('فاطمة أحمد حسن', 'السكري النوع الثاني', '2024-02-20', '2024-03-01', 2, 1),
            ('محمد عبدالله سالم', 'التهاب المفاصل الروماتويدي', '2024-03-10', '2024-03-15', 3, 2),
        ]

        for patient in sample_patients:
            conn.execute('''
                INSERT OR IGNORE INTO judicial_patients (name, diagnosis, court_ruling_date, treatment_start_date, clinic_id, area_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', patient)

        # إضافة أدوية تجريبية
        sample_medicines = [
            (1, 'ارتفاع ضغط الدم', 'أملوديبين 5 مجم', 'قرص', 2.50, 30, 75.00, '2024-08', 1, 1),
            (1, 'ارتفاع ضغط الدم', 'لوسارتان 50 مجم', 'قرص', 3.00, 30, 90.00, '2024-08', 1, 1),
            (2, 'السكري النوع الثاني', 'ميتفورمين 500 مجم', 'قرص', 1.50, 60, 90.00, '2024-08', 2, 1),
            (3, 'التهاب المفاصل', 'ديكلوفيناك 50 مجم', 'قرص', 2.00, 60, 120.00, '2024-08', 3, 2),
        ]

        for medicine in sample_medicines:
            conn.execute('''
                INSERT OR IGNORE INTO judicial_dispensed
                (patient_id, diagnosis, medicine_name, unit, unit_price, monthly_dose, monthly_cost, dispense_month, clinic_id, area_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', medicine)

        # إضافة أدوية تجريبية لأحكام المحكمة
        sample_judicial_medicines = [
            ('أملوديبين 5 مجم', 'قرص', 'دواء لعلاج ارتفاع ضغط الدم'),
            ('لوسارتان 50 مجم', 'قرص', 'دواء لعلاج ارتفاع ضغط الدم'),
            ('ميتفورمين 500 مجم', 'قرص', 'دواء لعلاج السكري النوع الثاني'),
            ('ديكلوفيناك 50 مجم', 'قرص', 'مضاد للالتهاب ومسكن للألم'),
            ('أوميبرازول 20 مجم', 'كبسولة', 'دواء لعلاج قرحة المعدة'),
            ('سيمفاستاتين 20 مجم', 'قرص', 'دواء لخفض الكوليسترول'),
        ]

        for medicine in sample_judicial_medicines:
            conn.execute('''
                INSERT OR IGNORE INTO judicial_medicines (name, unit, description)
                VALUES (?, ?, ?)
            ''', medicine)

        # إضافة بيانات تجريبية للأدوية المصروفة
        sample_dispensed_medicines = [
            (1, 'ارتفاع ضغط الدم', 'أملوديبين 5 مجم', 'قرص', 2.50, 30, 75.00, '2024-01', 1, 1),
            (1, 'ارتفاع ضغط الدم', 'لوسارتان 50 مجم', 'قرص', 3.00, 30, 90.00, '2024-02', 1, 1),
            (2, 'السكري النوع الثاني', 'ميتفورمين 500 مجم', 'قرص', 1.50, 60, 90.00, '2024-01', 2, 1),
            (2, 'السكري النوع الثاني', 'أملوديبين 5 مجم', 'قرص', 2.50, 30, 75.00, '2024-02', 2, 1),
            (3, 'التهاب المفاصل الروماتويدي', 'ديكلوفيناك 50 مجم', 'قرص', 1.75, 60, 105.00, '2024-01', 3, 2),
        ]

        for medicine in sample_dispensed_medicines:
            conn.execute('''
                INSERT OR IGNORE INTO judicial_dispensed
                (patient_id, diagnosis, medicine_name, unit, unit_price, monthly_dose, monthly_cost, dispense_month, clinic_id, area_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', medicine)

        conn.commit()
        flash('تم إضافة البيانات التجريبية بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('judicial_dispense'))

# تحديث بيانات المريض
@app.route('/judicial/patient/update', methods=['POST'])
def update_judicial_patient():
    """تحديث بيانات مريض الأحكام القضائية"""
    patient_id = request.form.get('patient_id')
    name = request.form.get('patient_name')
    diagnosis = request.form.get('patient_diagnosis')
    court_ruling_date = request.form.get('court_ruling_date')
    treatment_start_date = request.form.get('treatment_start_date')
    clinic_id = request.form.get('clinic_id')

    if all([patient_id, name, court_ruling_date, treatment_start_date, clinic_id]):
        conn = get_db_connection()
        try:
            # الحصول على area_id من clinic_id
            area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
            if not area_result:
                flash('العيادة المحددة غير موجودة', 'danger')
                return redirect(url_for('judicial_dispense'))

            area_id = area_result[0]

            # تحديث بيانات المريض
            conn.execute('''
                UPDATE judicial_patients
                SET name = ?, diagnosis = ?, court_ruling_date = ?, treatment_start_date = ?,
                    clinic_id = ?, area_id = ?
                WHERE id = ?
            ''', (name, diagnosis, court_ruling_date, treatment_start_date, clinic_id, area_id, patient_id))

            conn.commit()
            flash('تم تحديث بيانات المريض بنجاح', 'success')

        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('judicial_dispense'))

# تحديث دواء الأحكام القضائية
@app.route('/judicial/update', methods=['POST'])
def update_judicial_medicine():
    """تحديث دواء الأحكام القضائية"""
    medicine_id = request.form.get('medicine_id')
    diagnosis = request.form.get('diagnosis')
    medicine_name = request.form.get('medicine_name')
    unit = request.form.get('unit')
    unit_price = request.form.get('unit_price')
    monthly_dose = request.form.get('monthly_dose')
    dispense_month = request.form.get('dispense_month')

    if all([medicine_id, diagnosis, medicine_name, unit, unit_price, monthly_dose, dispense_month]):
        conn = get_db_connection()
        try:
            # حساب التكلفة الشهرية
            monthly_cost = float(unit_price) * float(monthly_dose)

            # تحديث الدواء
            conn.execute('''
                UPDATE judicial_dispensed
                SET diagnosis = ?, medicine_name = ?, unit = ?, unit_price = ?,
                    monthly_dose = ?, monthly_cost = ?, dispense_month = ?
                WHERE id = ?
            ''', (diagnosis, medicine_name, unit, unit_price, monthly_dose, monthly_cost, dispense_month, medicine_id))

            conn.commit()
            flash('تم تحديث الدواء بنجاح', 'success')

        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('judicial_dispense'))

# حذف دواء الأحكام القضائية
@app.route('/judicial/<int:medicine_id>/delete', methods=['POST'])
def delete_judicial_medicine(medicine_id):
    """حذف دواء الأحكام القضائية"""
    conn = get_db_connection()
    try:
        # الحصول على معلومات الدواء قبل الحذف
        medicine_info = conn.execute(
            'SELECT medicine_name FROM judicial_dispensed WHERE id = ?',
            (medicine_id,)
        ).fetchone()

        if not medicine_info:
            flash('الدواء المطلوب حذفه غير موجود', 'danger')
            return redirect(url_for('judicial_dispense'))

        medicine_name = medicine_info['medicine_name']

        # حذف الدواء
        conn.execute('DELETE FROM judicial_dispensed WHERE id = ?', (medicine_id,))
        conn.commit()

        flash(f'تم حذف "{medicine_name}" بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('judicial_dispense'))

# حذف مريض الأحكام القضائية
@app.route('/judicial/patient/<int:patient_id>/delete', methods=['POST'])
def delete_judicial_patient(patient_id):
    """حذف مريض الأحكام القضائية"""
    conn = get_db_connection()
    try:
        # التحقق من وجود أدوية مرتبطة بالمريض
        medicines_count = conn.execute(
            'SELECT COUNT(*) as count FROM judicial_dispensed WHERE patient_id = ?',
            (patient_id,)
        ).fetchone()

        if medicines_count['count'] > 0:
            flash('لا يمكن حذف المريض لوجود أدوية مرتبطة به. يرجى حذف الأدوية أولاً', 'danger')
            return redirect(url_for('judicial_dispense'))

        # الحصول على معلومات المريض قبل الحذف
        patient_info = conn.execute(
            'SELECT name FROM judicial_patients WHERE id = ?',
            (patient_id,)
        ).fetchone()

        if not patient_info:
            flash('المريض المطلوب حذفه غير موجود', 'danger')
            return redirect(url_for('judicial_dispense'))

        patient_name = patient_info['name']

        # حذف المريض
        conn.execute('DELETE FROM judicial_patients WHERE id = ?', (patient_id,))
        conn.commit()

        flash(f'تم حذف المريض "{patient_name}" بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('judicial_dispense'))

# إدارة أدوية أحكام المحكمة
@app.route('/judicial/medicines', methods=['GET', 'POST'])
def manage_judicial_medicines():
    """إدارة أدوية أحكام المحكمة"""
    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'add':
            # إضافة دواء جديد
            name = request.form.get('medicine_name')
            unit = request.form.get('unit')
            description = request.form.get('description')

            if name and unit:
                conn = get_db_connection()
                try:
                    conn.execute('''
                        INSERT INTO judicial_medicines (name, unit, description)
                        VALUES (?, ?, ?)
                    ''', (name, unit, description))
                    conn.commit()
                    flash('تم إضافة الدواء بنجاح', 'success')
                except Exception as e:
                    if 'UNIQUE constraint failed' in str(e):
                        flash('هذا الدواء موجود بالفعل', 'danger')
                    else:
                        flash(f'حدث خطأ: {str(e)}', 'danger')
                finally:
                    conn.close()
            else:
                flash('يرجى إدخال اسم الدواء والوحدة', 'danger')

        elif action == 'update':
            # تحديث دواء
            medicine_id = request.form.get('medicine_id')
            name = request.form.get('medicine_name')
            unit = request.form.get('unit')
            description = request.form.get('description')

            if medicine_id and name and unit:
                conn = get_db_connection()
                try:
                    conn.execute('''
                        UPDATE judicial_medicines
                        SET name = ?, unit = ?, description = ?
                        WHERE id = ?
                    ''', (name, unit, description, medicine_id))
                    conn.commit()
                    flash('تم تحديث الدواء بنجاح', 'success')
                except Exception as e:
                    if 'UNIQUE constraint failed' in str(e):
                        flash('اسم الدواء موجود بالفعل', 'danger')
                    else:
                        flash(f'حدث خطأ: {str(e)}', 'danger')
                finally:
                    conn.close()
            else:
                flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

        return redirect(url_for('manage_judicial_medicines'))

    # جلب قائمة الأدوية
    conn = get_db_connection()
    medicines = conn.execute('''
        SELECT * FROM judicial_medicines
        ORDER BY name
    ''').fetchall()
    conn.close()

    return render_template('manage_judicial_medicines.html', medicines=medicines)

# حذف دواء من أدوية أحكام المحكمة
@app.route('/judicial/medicines/<int:medicine_id>/delete', methods=['POST'])
def delete_judicial_medicine_item(medicine_id):
    """حذف دواء من قائمة أدوية أحكام المحكمة"""
    conn = get_db_connection()
    try:
        # التحقق من وجود سجلات مرتبطة بهذا الدواء
        usage_count = conn.execute(
            'SELECT COUNT(*) as count FROM judicial_dispensed WHERE medicine_name = (SELECT name FROM judicial_medicines WHERE id = ?)',
            (medicine_id,)
        ).fetchone()

        if usage_count['count'] > 0:
            flash('لا يمكن حذف هذا الدواء لوجود سجلات صرف مرتبطة به', 'danger')
            return redirect(url_for('manage_judicial_medicines'))

        # الحصول على معلومات الدواء قبل الحذف
        medicine_info = conn.execute(
            'SELECT name FROM judicial_medicines WHERE id = ?',
            (medicine_id,)
        ).fetchone()

        if not medicine_info:
            flash('الدواء المطلوب حذفه غير موجود', 'danger')
            return redirect(url_for('manage_judicial_medicines'))

        medicine_name = medicine_info['name']

        # حذف الدواء
        conn.execute('DELETE FROM judicial_medicines WHERE id = ?', (medicine_id,))
        conn.commit()

        flash(f'تم حذف "{medicine_name}" بنجاح', 'success')

    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('manage_judicial_medicines'))

# API للحصول على بيانات الدواء
@app.route('/api/judicial/medicine/<int:medicine_id>')
def get_judicial_medicine(medicine_id):
    """الحصول على بيانات الدواء"""
    conn = get_db_connection()
    try:
        medicine = conn.execute('''
            SELECT * FROM judicial_medicines WHERE id = ?
        ''', (medicine_id,)).fetchone()

        if medicine:
            return jsonify({
                'success': True,
                'medicine': {
                    'id': medicine['id'],
                    'name': medicine['name'],
                    'unit': medicine['unit'],
                    'description': medicine['description'] if medicine['description'] else ''
                }
            })
        else:
            return jsonify({'success': False, 'error': 'الدواء غير موجود'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
    finally:
        conn.close()

# تقرير الأحكام القضائية
@app.route('/judicial/report', methods=['GET', 'POST'])
def judicial_report():
    """تقرير الأحكام القضائية"""
    conn = get_db_connection()

    # جلب البيانات الأساسية للفلاتر وتحويلها إلى قواميس
    branches_rows = conn.execute('SELECT * FROM branches ORDER BY name').fetchall()
    branches = [dict(row) for row in branches_rows]

    areas_rows = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name
    ''').fetchall()
    areas = [dict(row) for row in areas_rows]

    clinics_rows = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name, clinics.name
    ''').fetchall()
    clinics = [dict(row) for row in clinics_rows]

    report_data = []
    report_title = "تقرير الأحكام القضائية"
    period_desc = "جميع الفترات"

    # تعيين القيم الافتراضية
    scope = request.form.get('scope', 'all') if request.method == 'POST' else 'all'
    scope_id = request.form.get('scope_id') if request.method == 'POST' else None
    report_type = request.form.get('report_type', 'medicine') if request.method == 'POST' else 'medicine'
    detail_level = request.form.get('detail_level', 'summary') if request.method == 'POST' else 'summary'
    date_range = request.form.get('date_range', 'month') if request.method == 'POST' else 'month'
    start_date = request.form.get('start_date') if request.method == 'POST' else None
    end_date = request.form.get('end_date') if request.method == 'POST' else None

    # بناء شروط الفلترة (يعمل لكل من GET و POST)
    where_conditions = []
    params = []

    # فلترة النطاق
    if scope == 'branch' and scope_id:
        where_conditions.append("b.id = ?")
        params.append(scope_id)
        branch_info = conn.execute("SELECT name FROM branches WHERE id = ?", (scope_id,)).fetchone()
        if branch_info:
            report_title += f" - {branch_info['name']}"
    elif scope == 'area' and scope_id:
        where_conditions.append("a.id = ?")
        params.append(scope_id)
        area_info = conn.execute("""
            SELECT areas.name, branches.name as branch_name
            FROM areas
            JOIN branches ON areas.branch_id = branches.id
            WHERE areas.id = ?
        """, (scope_id,)).fetchone()
        if area_info:
            report_title += f" - {area_info['branch_name']} - {area_info['name']}"
    elif scope == 'clinic' and scope_id:
        where_conditions.append("c.id = ?")
        params.append(scope_id)
        clinic_info = conn.execute("""
            SELECT clinics.name, areas.name as area_name, branches.name as branch_name
            FROM clinics
            JOIN areas ON clinics.area_id = areas.id
            JOIN branches ON areas.branch_id = branches.id
            WHERE clinics.id = ?
        """, (scope_id,)).fetchone()
        if clinic_info:
            report_title += f" - {clinic_info['branch_name']} - {clinic_info['area_name']} - {clinic_info['name']}"

    # معالجة الفترات الزمنية
    current_date = datetime.now()

    if date_range == 'month':
        # الشهر الحالي
        start_of_month = current_date.replace(day=1).strftime('%Y-%m')
        where_conditions.append("jd.dispense_month = ?")
        params.append(start_of_month)
        period_desc = f"الشهر الحالي ({start_of_month})"

    elif date_range == 'current_and_previous_month':
        # الشهر الحالي والسابق
        current_month = current_date.strftime('%Y-%m')
        prev_month = (current_date.replace(day=1) - timedelta(days=1)).strftime('%Y-%m')
        where_conditions.append("jd.dispense_month IN (?, ?)")
        params.extend([current_month, prev_month])
        period_desc = f"الشهر الحالي والسابق ({prev_month} - {current_month})"

    elif date_range == 'last_3_months':
        # آخر 3 أشهر
        months = []
        for i in range(3):
            month_date = current_date.replace(day=1) - timedelta(days=i*30)
            months.append(month_date.strftime('%Y-%m'))
        placeholders = ','.join(['?' for _ in months])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(months)
        period_desc = f"آخر 3 أشهر ({months[-1]} - {months[0]})"

    elif date_range == 'last_6_months':
        # آخر 6 أشهر
        months = []
        for i in range(6):
            month_date = current_date.replace(day=1) - timedelta(days=i*30)
            months.append(month_date.strftime('%Y-%m'))
        placeholders = ','.join(['?' for _ in months])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(months)
        period_desc = f"آخر 6 أشهر ({months[-1]} - {months[0]})"

    elif date_range == 'quarter':
        # الربع الحالي
        quarter = (current_date.month - 1) // 3 + 1
        quarter_months = [(quarter - 1) * 3 + i + 1 for i in range(3)]
        quarter_months_str = [f"{current_date.year}-{str(m).zfill(2)}" for m in quarter_months]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع {quarter} من {current_date.year}"

    elif date_range == 'q1':
        # الربع الأول
        quarter_months_str = [f"{current_date.year}-01", f"{current_date.year}-02", f"{current_date.year}-03"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الأول من {current_date.year}"

    elif date_range == 'q2':
        # الربع الثاني
        quarter_months_str = [f"{current_date.year}-04", f"{current_date.year}-05", f"{current_date.year}-06"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الثاني من {current_date.year}"

    elif date_range == 'q3':
        # الربع الثالث
        quarter_months_str = [f"{current_date.year}-07", f"{current_date.year}-08", f"{current_date.year}-09"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الثالث من {current_date.year}"

    elif date_range == 'q4':
        # الربع الرابع
        quarter_months_str = [f"{current_date.year}-10", f"{current_date.year}-11", f"{current_date.year}-12"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الرابع من {current_date.year}"

    elif date_range == 'year':
        # السنة الحالية
        where_conditions.append("jd.dispense_month LIKE ?")
        params.append(f'{current_date.year}-%')
        period_desc = f"السنة الحالية ({current_date.year})"

    elif date_range == 'custom' and start_date and end_date:
        # فترة مخصصة
        start_month = start_date[:7] if len(start_date) >= 7 else start_date
        end_month = end_date[:7] if len(end_date) >= 7 else end_date
        where_conditions.append("jd.dispense_month BETWEEN ? AND ?")
        params.extend([start_month, end_month])

        # تحسين عرض الفترة
        try:
            start_dt = datetime.strptime(start_month, '%Y-%m')
            end_dt = datetime.strptime(end_month, '%Y-%m')
            start_formatted = start_dt.strftime('%m/%Y')
            end_formatted = end_dt.strftime('%m/%Y')
            period_desc = f"من {start_formatted} إلى {end_formatted}"
        except:
            period_desc = f"من {start_month} إلى {end_month}"
    else:
        period_desc = "جميع الفترات"

    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

    # تنفيذ التقرير حسب النوع
    if report_type == 'medicine':
        if detail_level == 'summary':
            # تقرير الأدوية الإجمالي
            report_data = conn.execute(f"""
                SELECT
                    jd.medicine_name,
                    COUNT(DISTINCT jd.patient_id) as case_count,
                    SUM(jd.monthly_dose) as total_quantity,
                    SUM(jd.monthly_cost) as total_cost,
                    AVG(jd.monthly_cost) as avg_cost
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                GROUP BY jd.medicine_name
                ORDER BY total_cost DESC
            """, params).fetchall()
            report_title += " - تكلفة الأدوية (إجمالي)"
        else:
            # تقرير الأدوية التفصيلي
            report_data = conn.execute(f"""
                SELECT
                    jd.medicine_name,
                    jd.unit,
                    jd.monthly_dose as quantity,
                    jp.name as patient_name,
                    jd.monthly_cost as cost,
                    jd.unit_price as avg_cost,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                ORDER BY jd.medicine_name, jp.name
            """, params).fetchall()
            report_title += " - تكلفة الأدوية (تفصيلي)"

    elif report_type == 'patient':
        if detail_level == 'summary':
            # تقرير المرضى الإجمالي
            report_data = conn.execute(f"""
                SELECT
                    jp.name as patient_name,
                    jd.medicine_name,
                    SUM(jd.monthly_dose) as total_quantity,
                    SUM(jd.monthly_cost) as total_cost
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                GROUP BY jp.id, jd.medicine_name
                ORDER BY jp.name, jd.medicine_name
            """, params).fetchall()
            report_title += " - بيانات المرضى (إجمالي)"
        else:
            # تقرير المرضى التفصيلي
            report_data = conn.execute(f"""
                SELECT
                    jp.name as patient_name,
                    jp.diagnosis,
                    jd.medicine_name,
                    jd.unit,
                    jd.unit_price,
                    jd.monthly_dose,
                    jd.monthly_cost,
                    jp.court_ruling_date,
                    jp.treatment_start_date,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                ORDER BY jp.name, jd.medicine_name
            """, params).fetchall()
            report_title += " - بيانات المرضى (تفصيلي)"

    # حساب الإحصائيات السريعة
    stats = {}
    if report_data:
        if report_type == 'medicine':
            if detail_level == 'summary':
                # إحصائيات تقرير الأدوية الإجمالي
                stats['total_medicines'] = len(report_data)
                stats['total_cases'] = sum(row['case_count'] for row in report_data)
                stats['total_cost'] = sum(row['total_cost'] for row in report_data)
                stats['total_quantity'] = sum(row['total_quantity'] for row in report_data)
            else:
                # إحصائيات تقرير الأدوية التفصيلي
                unique_medicines = set(row['medicine_name'] for row in report_data)
                unique_patients = set(row['patient_name'] for row in report_data)
                stats['total_medicines'] = len(unique_medicines)
                stats['total_cases'] = len(unique_patients)
                stats['total_cost'] = sum(row['monthly_cost'] for row in report_data)
                stats['total_quantity'] = sum(row['monthly_dose'] for row in report_data)
        else:
            # إحصائيات تقرير المرضى
            unique_medicines = set(row['medicine_name'] for row in report_data)
            unique_patients = set(row['patient_name'] for row in report_data)
            stats['total_medicines'] = len(unique_medicines)
            stats['total_cases'] = len(unique_patients)
            if detail_level == 'summary':
                stats['total_cost'] = sum(row['total_cost'] for row in report_data)
                stats['total_quantity'] = sum(row['total_quantity'] for row in report_data)
            else:
                stats['total_cost'] = sum(row['monthly_cost'] for row in report_data)
                stats['total_quantity'] = sum(row['monthly_dose'] for row in report_data)
    else:
        stats = {
            'total_medicines': 0,
            'total_cases': 0,
            'total_cost': 0,
            'total_quantity': 0
        }

    conn.close()

    return render_template('judicial_report.html',
                         branches=branches,
                         areas=areas,
                         clinics=clinics,
                         report_data=report_data,
                         report_title=report_title,
                         period_desc=period_desc,
                         stats=stats,
                         current_year=datetime.now().year,
                         request=request)


# مسارات التقارير
@app.route('/reports')
def reports():
    conn = get_db_connection()

    # تحويل Row objects إلى dictionaries للتوافق مع JSON
    areas_rows = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    areas = [dict(row) for row in areas_rows]

    clinics_rows = conn.execute('''
        SELECT clinics.*, areas.name as area_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
    ''').fetchall()
    clinics = [dict(row) for row in clinics_rows]

    branches_rows = conn.execute('SELECT * FROM branches').fetchall()
    branches = [dict(row) for row in branches_rows]

    categories_rows = conn.execute('SELECT * FROM drug_categories').fetchall()
    categories = [dict(row) for row in categories_rows]

    insulin_categories_rows = conn.execute('SELECT * FROM insulin_categories').fetchall()
    insulin_categories = [dict(row) for row in insulin_categories_rows]

    # تحميل أنواع الأنسولين (هيئة، مدعم، إلخ)
    insulin_types_rows = conn.execute('SELECT * FROM insulin_types').fetchall()
    insulin_types = [dict(row) for row in insulin_types_rows]

    # إضافة الأدوية والأنسولين للتقرير التفصيلي
    drugs_rows = conn.execute('SELECT id, name, category_id FROM drugs ORDER BY name').fetchall()
    drugs = [dict(row) for row in drugs_rows]

    # الحصول على الأنسولين من جدول insulin_dispensed (أسماء فريدة)
    insulin_rows = conn.execute('''
        SELECT DISTINCT name, type
        FROM insulin_dispensed
        WHERE name IS NOT NULL AND name != ''
        ORDER BY name
    ''').fetchall()

    # تحويل إلى تنسيق مناسب مع ربط بأنواع الأنسولين
    insulin = []
    for row in insulin_rows:
        # البحث عن النوع المطابق في insulin_types
        type_match = None
        for insulin_type in insulin_types:
            if insulin_type['name'] == row['type']:
                type_match = insulin_type
                break

        insulin.append({
            'id': row['name'],  # استخدام الاسم كـ ID
            'name': row['name'],
            'type': row['type'] or 'غير محدد',  # نوع الأنسولين
            'type_id': type_match['id'] if type_match else None  # ID النوع للفلترة
        })

    conn.close()
    return render_template('reports_new.html',
                         areas=areas,
                         clinics=clinics,
                         branches=branches,
                         categories=categories,
                         insulin_categories=insulin_categories,
                         insulin_types=insulin_types,
                         drugs=drugs,
                         insulin=insulin)

# تقرير المقارنة
@app.route('/reports/comparison')
def comparison_report():
    """تقرير مقارنة بين الفروع أو المناطق أو العيادات"""
    # الحصول على معلمات التقرير
    scope_type = request.args.get('scope_type', 'all')  # نوع النطاق
    parent_id = request.args.get('parent_id')  # معرف الفرع أو المنطقة
    date_range = request.args.get('date_range', 'month')  # الفترة الزمنية
    category_id = request.args.get('category_id')  # تصنيف الدواء

    start_month = request.args.get('start_month')
    end_month = request.args.get('end_month')

    print(f"تقرير المقارنة - المعاملات: scope_type={scope_type}, parent_id={parent_id}, date_range={date_range}, category_id={category_id}")

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية
        date_params = []
        current_year = datetime.now().year

        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_filter = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params.append(current_month)
            period_desc = f"الشهر الحالي ({current_month})"
        elif date_range == 'quarter':
            current_quarter = (datetime.now().month - 1) // 3 + 1
            quarter_months = {
                1: ['01', '02', '03'],
                2: ['04', '05', '06'],
                3: ['07', '08', '09'],
                4: ['10', '11', '12']
            }
            months = quarter_months[current_quarter]
            date_filter = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months])})"
            date_params = [f"{current_year}-{month}" for month in months]
            period_desc = f"الربع {current_quarter} من {current_year}"
        elif date_range == 'q1':
            months = ['01', '02', '03']
            date_filter = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months])})"
            date_params = [f"{current_year}-{month}" for month in months]
            period_desc = f"الربع الأول (يناير - مارس) {current_year}"
        elif date_range == 'q2':
            months = ['04', '05', '06']
            date_filter = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months])})"
            date_params = [f"{current_year}-{month}" for month in months]
            period_desc = f"الربع الثاني (أبريل - يونيو) {current_year}"
        elif date_range == 'q3':
            months = ['07', '08', '09']
            date_filter = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months])})"
            date_params = [f"{current_year}-{month}" for month in months]
            period_desc = f"الربع الثالث (يوليو - سبتمبر) {current_year}"
        elif date_range == 'q4':
            months = ['10', '11', '12']
            date_filter = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months])})"
            date_params = [f"{current_year}-{month}" for month in months]
            period_desc = f"الربع الرابع (أكتوبر - ديسمبر) {current_year}"
        elif date_range == 'year':
            date_filter = "AND strftime('%Y', d.dispense_month) = ?"
            date_params.append(str(current_year))
            period_desc = f"السنة الحالية ({current_year})"
        elif date_range == 'custom' and start_month and end_month:
            date_filter = "AND strftime('%Y-%m', d.dispense_month) BETWEEN ? AND ?"
            date_params.extend([start_month, end_month])
            period_desc = f"من {start_month} إلى {end_month}"
        else:
            date_filter = ""
            period_desc = "جميع الفترات"

        # تحديد تصفية التصنيف
        category_filter = ""
        category_params = []
        selected_category_name = "جميع التصنيفات"

        if category_id:
            category_filter = "AND dr.category_id = ?"
            category_params.append(category_id)
            category_info = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_info:
                selected_category_name = category_info['name']

        # تحديد نطاق المقارنة وعنوان التقرير
        report_title = "تقرير المقارنة"
        parent_name = ""

        if scope_type == 'all':
            # مقارنة بين جميع الفروع
            report_title = "مقارنة بين جميع الفروع"
            query = f'''
                SELECT
                    b.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE 1=1 {date_filter} {category_filter}
                GROUP BY b.id, b.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = date_params + category_params

        elif scope_type == 'branch' and parent_id:
            # مقارنة بين المناطق داخل فرع محدد
            branch = conn.execute('SELECT name FROM branches WHERE id = ?', (parent_id,)).fetchone()
            if branch:
                parent_name = branch['name']
                report_title = f"مقارنة بين مناطق فرع {parent_name}"

            query = f'''
                SELECT
                    a.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE a.branch_id = ? {date_filter} {category_filter}
                GROUP BY a.id, a.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = [parent_id] + date_params + category_params

        elif scope_type == 'area' and parent_id:
            # مقارنة بين العيادات داخل منطقة محددة
            area = conn.execute('''
                SELECT a.name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (parent_id,)).fetchone()
            if area:
                parent_name = f"{area['name']} - {area['branch_name']}"
                report_title = f"مقارنة بين عيادات منطقة {area['name']}"

            query = f'''
                SELECT
                    c.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE c.area_id = ? {date_filter} {category_filter}
                GROUP BY c.id, c.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = [parent_id] + date_params + category_params

        else:
            # افتراضي - مقارنة بين جميع الفروع
            report_title = "مقارنة بين جميع الفروع"
            query = f'''
                SELECT
                    b.name as location_name,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    SUM(dd.quantity * dd.price) as total_cost,
                    COUNT(DISTINCT dr.id) as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE 1=1 {date_filter} {category_filter}
                GROUP BY b.id, b.name
                HAVING total_cost > 0
                ORDER BY total_cost DESC
            '''
            query_params = date_params + category_params

        print(f"تنفيذ الاستعلام: {query}")
        print(f"معاملات الاستعلام: {query_params}")

        # تنفيذ الاستعلام
        comparison_data = conn.execute(query, query_params).fetchall()

        print(f"عدد النتائج من قاعدة البيانات: {len(comparison_data)}")

        # التحقق من وجود بيانات
        if not comparison_data:
            print("لا توجد بيانات للفترة والنطاق المحددين")
            flash('لا توجد بيانات للفترة والنطاق المحددين', 'warning')
            return redirect(url_for('reports'))

        # حساب الإجمالي
        total_cost = sum((item['total_cost'] if item['total_cost'] is not None else 0) for item in comparison_data)
        total_drugs = sum((item['unique_drugs'] if item['unique_drugs'] is not None else 0) for item in comparison_data)
        total_dispensed = sum((item['total_dispensed'] if item['total_dispensed'] is not None else 0) for item in comparison_data)

        print(f"الإجماليات: التكلفة={total_cost}, الأدوية={total_drugs}, المنصرف={total_dispensed}")

        # تحويل البيانات للقالب
        items = []
        for i, item in enumerate(comparison_data, 1):
            percentage = (item['total_cost'] / total_cost * 100) if total_cost > 0 else 0
            items.append({
                'rank': i,
                'name': item['location_name'],
                'drugs_count': item['unique_drugs'] if item['unique_drugs'] is not None else 0,
                'total_cost': item['total_cost'] if item['total_cost'] is not None else 0,
                'percentage': round(percentage, 2)
            })

        print(f"عدد العناصر المسترجعة: {len(items)}")
        for item in items[:3]:  # طباعة أول 3 عناصر للتحقق
            print(f"العنصر: {item['name']}, التكلفة: {item['total_cost']}, الأدوية: {item['drugs_count']}")

        return render_template('comparison_report.html',
                             items=items,
                             total_cost=total_cost,
                             total_drugs=total_drugs,
                             total_dispensed=total_dispensed,
                             scope_type=scope_type,
                             parent_id=parent_id,
                             date_range=date_range,
                             category_id=category_id,
                             report_title=report_title,
                             period=period_desc,
                             parent_name=parent_name,
                             category_name=selected_category_name,
                             current_year=datetime.now().year)

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"خطأ في تقرير المقارنة: {e}")
        print(f"تفاصيل الخطأ: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير المقارنة التفصيلي
@app.route('/reports/detailed_comparison')
def detailed_comparison_report():
    """تقرير المقارنة التفصيلي للأدوية والتصنيفات - نسخة مبسطة"""
    conn = get_db_connection()
    try:
        print("=== بداية تقرير المقارنة التفصيلي ===")

        # استلام المعاملات
        comparison_type = request.args.get('comparison_type', 'branches')
        selected_locations = request.args.getlist('locations[]')
        category_id = request.args.get('category_id')
        selected_drugs = request.args.getlist('drugs[]')
        data_type = request.args.get('data_type', 'all')  # نوع البيانات المراد عرضها
        date_filter = request.args.get('date_filter', 'month')
        start_month = request.args.get('start_month')
        end_month = request.args.get('end_month')

        print(f"المعاملات: comparison_type={comparison_type}, date_filter={date_filter}")
        print(f"المواقع المختارة: {selected_locations}")
        print(f"التصنيف المختار: {category_id}")
        print(f"الأدوية المختارة: {selected_drugs}")
        print(f"نوع البيانات المختار: {data_type}")

        # التحقق من وجود بيانات للمقارنة
        if not selected_locations or len(selected_locations) < 2:
            flash('يرجى اختيار موقعين على الأقل للمقارنة', 'warning')
            return redirect(url_for('reports'))

        # بناء فلتر التاريخ - مع دعم الأرباع والنماذج المحسنة
        if date_filter == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_filter_sql = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            # تنسيق التاريخ إلى MM/YYYY
            formatted_month = format_month_year(current_month + '-01')
            period_desc = f"الشهر الحالي ({formatted_month})"
            print(f"الشهر الحالي: {period_desc}")
        elif date_filter == 'quarter':
            current_year = datetime.now().year
            current_quarter = (datetime.now().month - 1) // 3 + 1
            quarter_months = {
                1: ['01', '02', '03'],
                2: ['04', '05', '06'],
                3: ['07', '08', '09'],
                4: ['10', '11', '12']
            }
            months = quarter_months[current_quarter]
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months])})"
            date_params = [f"{current_year}-{month}" for month in months]
            # تنسيق التواريخ إلى MM/YYYY
            start_formatted = format_month_year(f"{current_year}-{months[0]}-01")
            end_formatted = format_month_year(f"{current_year}-{months[-1]}-01")
            period_desc = f"الربع {current_quarter} من {current_year} (من {start_formatted} إلى {end_formatted})"
            print(f"الربع الحالي: {period_desc}")
        elif date_filter.startswith('quarter_'):
            current_year = datetime.now().year
            quarter_num = int(date_filter.split('_')[1])
            quarter_months = {
                1: ['01', '02', '03'],
                2: ['04', '05', '06'],
                3: ['07', '08', '09'],
                4: ['10', '11', '12']
            }
            months = quarter_months[quarter_num]
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months])})"
            date_params = [f"{current_year}-{month}" for month in months]
            # تنسيق التواريخ إلى MM/YYYY
            start_formatted = format_month_year(f"{current_year}-{months[0]}-01")
            end_formatted = format_month_year(f"{current_year}-{months[-1]}-01")
            period_desc = f"الربع {quarter_num} من {current_year} (من {start_formatted} إلى {end_formatted})"
            print(f"ربع محدد: {period_desc}")
        elif date_filter == 'year':
            current_year = datetime.now().year
            date_filter_sql = "AND strftime('%Y', d.dispense_month) = ?"
            date_params = [str(current_year)]
            period_desc = f"السنة الحالية ({current_year})"
            print(f"السنة الحالية: {period_desc}")
        elif date_filter == 'last_3_months':
            # آخر 3 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(3):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months_list])})"
            date_params = months_list
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            period_desc = f"آخر 3 أشهر (من {start_formatted} إلى {end_formatted})"
            print(f"آخر 3 أشهر: {period_desc}")
        elif date_filter == 'last_6_months':
            # آخر 6 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(6):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months_list])})"
            date_params = months_list
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            period_desc = f"آخر 6 أشهر (من {start_formatted} إلى {end_formatted})"
            print(f"آخر 6 أشهر: {period_desc}")
        elif date_filter == 'last_12_months':
            # آخر 12 شهر (سنة كاملة)
            current_date = datetime.now()
            months_list = []
            for i in range(12):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months_list])})"
            date_params = months_list
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            period_desc = f"آخر 12 شهر (من {start_formatted} إلى {end_formatted})"
            print(f"آخر 12 شهر: {period_desc}")
        elif date_filter == 'current_and_previous_month':
            # الشهر الحالي والشهر السابق
            current_date = datetime.now()
            current_month = current_date.strftime('%Y-%m')
            previous_month = (current_date.replace(day=1) - timedelta(days=1)).strftime('%Y-%m')
            months_list = [previous_month, current_month]
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months_list])})"
            date_params = months_list
            start_formatted = format_month_year(previous_month + '-01')
            end_formatted = format_month_year(current_month + '-01')
            period_desc = f"الشهر الحالي والسابق (من {start_formatted} إلى {end_formatted})"
            print(f"الشهر الحالي والسابق: {period_desc}")
        elif date_filter == 'first_half_year':
            # النصف الأول من السنة (يناير - يونيو)
            current_year = datetime.now().year
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(1, 7)]
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months_list])})"
            date_params = months_list
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            period_desc = f"النصف الأول من {current_year} (من {start_formatted} إلى {end_formatted})"
            print(f"النصف الأول من السنة: {period_desc}")
        elif date_filter == 'second_half_year':
            # النصف الثاني من السنة (يوليو - ديسمبر)
            current_year = datetime.now().year
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(7, 13)]
            date_filter_sql = f"AND strftime('%Y-%m', d.dispense_month) IN ({','.join(['?' for _ in months_list])})"
            date_params = months_list
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            period_desc = f"النصف الثاني من {current_year} (من {start_formatted} إلى {end_formatted})"
            print(f"النصف الثاني من السنة: {period_desc}")
        elif date_filter == 'custom' and start_month and end_month:
            date_filter_sql = "AND strftime('%Y-%m', d.dispense_month) BETWEEN ? AND ?"
            date_params = [start_month, end_month]
            # تنسيق التواريخ إلى MM/YYYY
            start_formatted = format_month_year(start_month + '-01')
            end_formatted = format_month_year(end_month + '-01')
            period_desc = f"فترة مخصصة (من {start_formatted} إلى {end_formatted})"
            print(f"فترة مخصصة: {period_desc}")
        else:
            # الشهر الحالي (افتراضي)
            current_month = datetime.now().strftime('%Y-%m')
            date_filter_sql = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            # تنسيق التاريخ إلى MM/YYYY
            formatted_month = format_month_year(current_month + '-01')
            period_desc = f"الشهر الحالي ({formatted_month})"

        # بناء فلتر التصنيف - مبسط
        category_filter_sql = ""
        category_params = []
        category_name = "جميع التصنيفات"

        if category_id:
            category_filter_sql = "AND dr.category_id = ?"
            category_params.append(category_id)
            # نفس طريقة التقرير الشهري
            category_info = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_info:
                category_name = category_info['name']

        # بناء فلتر الأدوية والأنسولين
        drugs_filter_sql = ""
        insulin_filter_sql = ""
        drugs_params = []
        insulin_params = []
        drugs_names = []
        insulin_names = []

        if selected_drugs:
            # فصل الأدوية عن الأنسولين
            drug_ids = []
            insulin_ids = []

            for item in selected_drugs:
                if item.startswith('drug_'):
                    drug_ids.append(item.replace('drug_', ''))
                elif item.startswith('insulin_'):
                    insulin_ids.append(item.replace('insulin_', ''))

            # فلتر الأدوية
            if drug_ids:
                placeholders = ','.join(['?' for _ in drug_ids])
                drugs_filter_sql = f"AND dr.id IN ({placeholders})"
                drugs_params.extend(drug_ids)

                # الحصول على أسماء الأدوية
                drugs_query = f"SELECT name FROM drugs WHERE id IN ({placeholders})"
                drugs_rows = conn.execute(drugs_query, drug_ids).fetchall()
                for row in drugs_rows:
                    drugs_names.append(row['name'])

            # فلتر الأنسولين
            if insulin_ids:
                placeholders = ','.join(['?' for _ in insulin_ids])
                insulin_filter_sql = f"AND i.name IN ({placeholders})"
                insulin_params.extend(insulin_ids)
                insulin_names.extend(insulin_ids)

        # بناء الاستعلام الأساسي حسب نوع المقارنة
        if comparison_type == 'branches':
            location_join = '''
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
            '''
            location_filter = f"AND b.id IN ({','.join(['?' for _ in selected_locations])})"
            location_group = 'b.id, b.name'
            location_name_field = 'b.name'
        elif comparison_type == 'areas':
            location_join = 'JOIN areas a ON c.area_id = a.id'
            location_filter = f"AND a.id IN ({','.join(['?' for _ in selected_locations])})"
            location_group = 'a.id, a.name'
            location_name_field = 'a.name'
        else:  # clinics
            location_join = ''
            location_filter = f"AND c.id IN ({','.join(['?' for _ in selected_locations])})"
            location_group = 'c.id, c.name'
            location_name_field = 'c.name'

        # استعلام الأدوية والأنسولين
        comparison_data = []

        # تحليل الاختيارات بشكل أكثر دقة
        drugs_selected = selected_drugs and any(item.startswith('drug_') for item in selected_drugs)
        insulin_selected = selected_drugs and any(item.startswith('insulin_') for item in selected_drugs)
        no_specific_drugs_selected = not selected_drugs
        category_selected = bool(category_id)

        print(f"تحليل الاختيارات:")
        print(f"selected_drugs: {selected_drugs}")
        print(f"drugs_selected: {drugs_selected}")
        print(f"insulin_selected: {insulin_selected}")
        print(f"no_specific_drugs_selected: {no_specific_drugs_selected}")
        print(f"category_selected: {category_selected}")
        print(f"data_type: {data_type}")

        # منطق استعلام الأدوية حسب نوع البيانات المختار:
        should_query_drugs = False
        if data_type in ['all', 'drugs']:
            should_query_drugs = (
                drugs_selected or
                (no_specific_drugs_selected and category_selected) or
                (no_specific_drugs_selected and not category_selected and data_type == 'all') or
                (data_type == 'drugs')  # إذا تم اختيار "الأدوية فقط" فعرض الأدوية دائماً
            )

        print(f"should_query_drugs: {should_query_drugs}")

        if should_query_drugs:
            drugs_query = f'''
                SELECT
                    {location_name_field} as location_name,
                    dr.name as drug_name,
                    'دواء' as item_type,
                    COUNT(d.id) as cases_count,
                    SUM(dd.quantity) as total_quantity,
                    SUM(dd.quantity * dd.price) as total_cost,
                    AVG(dd.price) as avg_price
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                {location_join}
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                JOIN drugs dr ON d.drug_id = dr.id
                WHERE 1=1
                {date_filter_sql}
                {category_filter_sql}
                {drugs_filter_sql}
                {location_filter}
                GROUP BY {location_group}, dr.id, dr.name
                ORDER BY {location_name_field}, total_cost DESC
            '''

            drugs_params_all = date_params + category_params + drugs_params + selected_locations
            print(f"استعلام الأدوية: {drugs_query}")
            print(f"معاملات الأدوية: {drugs_params_all}")

            # تنفيذ استعلام الأدوية
            drugs_results = conn.execute(drugs_query, drugs_params_all).fetchall()
            print(f"عدد نتائج الأدوية: {len(drugs_results)}")

            # معالجة نتائج الأدوية
            for row in drugs_results:
                item = {
                    'location_name': row['location_name'],
                    'drug_name': row['drug_name'],
                    'item_type': row['item_type'],
                    'cases_count': row['cases_count'] or 0,
                    'total_quantity': row['total_quantity'] or 0,
                    'total_cost': row['total_cost'] or 0,
                    'avg_price': row['avg_price'] or 0
                }
                comparison_data.append(item)

        # منطق استعلام الأنسولين حسب نوع البيانات المختار:
        should_query_insulin = False
        if data_type in ['all', 'insulin']:
            should_query_insulin = (
                insulin_selected or
                (no_specific_drugs_selected and not category_selected and data_type == 'all') or
                (data_type == 'insulin')  # إذا تم اختيار "الأنسولين فقط" فعرض الأنسولين دائماً
            )

        print(f"تحليل اختيارات الأنسولين:")
        print(f"insulin_selected: {insulin_selected}")
        print(f"should_query_insulin: {should_query_insulin}")

        if should_query_insulin:
            insulin_query = f'''
                SELECT
                    {location_name_field} as location_name,
                    i.name as drug_name,
                    'أنسولين' as item_type,
                    SUM(i.cases_count) as cases_count,
                    SUM(i.quantity) as total_quantity,
                    SUM(i.cost) as total_cost,
                    AVG(i.price) as avg_price
                FROM insulin_dispensed i
                JOIN clinics c ON i.clinic_id = c.id
                {location_join}
                WHERE 1=1
                {date_filter_sql.replace('d.dispense_month', 'i.dispense_month')}
                {insulin_filter_sql}
                {location_filter}
                GROUP BY {location_group}, i.name
                ORDER BY {location_name_field}, total_cost DESC
            '''

            insulin_params_all = date_params + insulin_params + selected_locations
            print(f"استعلام الأنسولين: {insulin_query}")
            print(f"معاملات الأنسولين: {insulin_params_all}")

            # تنفيذ استعلام الأنسولين
            insulin_results = conn.execute(insulin_query, insulin_params_all).fetchall()
            print(f"عدد نتائج الأنسولين: {len(insulin_results)}")

            # معالجة نتائج الأنسولين
            for row in insulin_results:
                item = {
                    'location_name': row['location_name'],
                    'drug_name': row['drug_name'],
                    'item_type': row['item_type'],
                    'cases_count': row['cases_count'] or 0,
                    'total_quantity': row['total_quantity'] or 0,
                    'total_cost': row['total_cost'] or 0,
                    'avg_price': row['avg_price'] or 0
                }
                comparison_data.append(item)

        print(f"عدد النتائج: {len(comparison_data)}")

        # تنظيم البيانات - نفس طريقة الاختبار
        locations_data = {}
        for item in comparison_data:
            location = item['location_name']
            if location not in locations_data:
                locations_data[location] = {
                    'name': location,
                    'drugs': [],
                    'total_cases': 0,
                    'total_quantity': 0,
                    'total_cost': 0
                }

            locations_data[location]['drugs'].append({
                'name': item['drug_name'],
                'type': item['item_type'],
                'cases_count': item['cases_count'],
                'total_quantity': item['total_quantity'],
                'total_cost': item['total_cost'],
                'avg_price': item['avg_price']
            })

            locations_data[location]['total_cases'] += item['cases_count']
            locations_data[location]['total_quantity'] += item['total_quantity']
            locations_data[location]['total_cost'] += item['total_cost']

        # إحصائيات
        total_cases = sum(item['cases_count'] for item in comparison_data)
        total_quantity = sum(item['total_quantity'] for item in comparison_data)
        total_cost = sum(item['total_cost'] for item in comparison_data)
        avg_cost = total_cost / total_cases if total_cases > 0 else 0

        # الحصول على أسماء المواقع - نفس طريقة الاختبار
        if comparison_type == 'branches':
            locations_query = f"SELECT name FROM branches WHERE id IN ({','.join(['?' for _ in selected_locations])})"
        elif comparison_type == 'areas':
            locations_query = f"SELECT name FROM areas WHERE id IN ({','.join(['?' for _ in selected_locations])})"
        else:
            locations_query = f"SELECT name FROM clinics WHERE id IN ({','.join(['?' for _ in selected_locations])})"

        locations_results = conn.execute(locations_query, selected_locations).fetchall()
        locations_names = [row['name'] for row in locations_results]

        return render_template('detailed_comparison_report.html',
                             locations_data=list(locations_data.values()),
                             comparison_type=comparison_type,
                             comparison_type_text={'branches': 'الفروع', 'areas': 'المناطق', 'clinics': 'العيادات'}[comparison_type],
                             selected_locations_names=locations_names,
                             category_name=category_name,
                             drugs_names=drugs_names,
                             period_desc=period_desc,
                             total_cases=total_cases,
                             total_quantity=total_quantity,
                             total_cost=total_cost,
                             avg_cost=avg_cost,
                             current_year=datetime.now().year,
                             date_filter=date_filter,
                             start_month=start_month,
                             end_month=end_month,
                             format_month_year=format_month_year)

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"خطأ في تقرير المقارنة التفصيلي: {e}")
        print(f"تفاصيل الخطأ: {error_details}")
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير شهري
@app.route('/reports/monthly')
def monthly_report():
    month = request.args.get('month')
    area_id = request.args.get('area_id')
    category_id = request.args.get('category_id')
    scope_type = request.args.get('scope_type', 'area')

    if not month:
        flash('يرجى اختيار الشهر', 'warning')
        return redirect(url_for('reports'))

    conn = get_db_connection()
    try:
        # بناء الاستعلام حسب النطاق
        where_conditions = [f"strftime('%Y-%m', d.dispense_month) = '{month}'"]

        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if category_id:
            where_conditions.append(f"dc.id = {category_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات الأدوية المنصرفة مع تفاصيل أكثر
        drugs_data = conn.execute(f'''
            SELECT
                dr.name as drug_name,
                dc.name as category_name,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                dd.price as price,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as total_cases,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
            GROUP BY dr.id, dr.name, dc.name, c.name, a.name, b.name, dd.price
            ORDER BY c.name, dc.name, total_cost DESC
        ''').fetchall()

        clinics = {}
        area_categories = {}
        branch_categories = {}

        for item in drugs_data:
            clinic_name = item['clinic_name']
            category_name = item['category_name']
            area_name = item['area_name']
            branch_name = item['branch_name']

            drug_key = (item['drug_name'], item['price'])
            drug_info = {
                'drug_name': item['drug_name'],
                'quantity': item['total_quantity'],
                'price': item['price'],
                'cases': item['total_cases'],
                'cost': item['total_cost']
            }

            # تجميع بيانات العيادات
            if clinic_name not in clinics:
                clinics[clinic_name] = {
                    'name': clinic_name,
                    'area_name': area_name,
                    'branch_name': branch_name,
                    'categories': {},
                    'total_cost': 0
                }

            if category_name not in clinics[clinic_name]['categories']:
                clinics[clinic_name]['categories'][category_name] = {
                    'name': category_name,
                    'drugs': {},
                    'total_cost': 0
                }

            if drug_key not in clinics[clinic_name]['categories'][category_name]['drugs']:
                clinics[clinic_name]['categories'][category_name]['drugs'][drug_key] = drug_info.copy()
            else:
                existing = clinics[clinic_name]['categories'][category_name]['drugs'][drug_key]
                existing['quantity'] += drug_info['quantity']
                existing['cases'] += drug_info['cases']
                existing['cost'] += drug_info['cost']

            clinics[clinic_name]['categories'][category_name]['total_cost'] += item['total_cost']
            clinics[clinic_name]['total_cost'] += item['total_cost']

            # تجميع بيانات المنطقة
            if category_name not in area_categories:
                area_categories[category_name] = {
                    'name': category_name,
                    'drugs': {},
                    'total_cost': 0
                }

            if drug_key not in area_categories[category_name]['drugs']:
                area_categories[category_name]['drugs'][drug_key] = drug_info.copy()
            else:
                existing = area_categories[category_name]['drugs'][drug_key]
                existing['quantity'] += drug_info['quantity']
                existing['cases'] += drug_info['cases']
                existing['cost'] += drug_info['cost']

            area_categories[category_name]['total_cost'] += item['total_cost']

            # تجميع بيانات الفرع
            if category_name not in branch_categories:
                branch_categories[category_name] = {
                    'name': category_name,
                    'drugs': {},
                    'total_cost': 0
                }

            if drug_key not in branch_categories[category_name]['drugs']:
                branch_categories[category_name]['drugs'][drug_key] = drug_info.copy()
            else:
                existing = branch_categories[category_name]['drugs'][drug_key]
                existing['quantity'] += drug_info['quantity']
                existing['cases'] += drug_info['cases']
                existing['cost'] += drug_info['cost']

            branch_categories[category_name]['total_cost'] += item['total_cost']

        # تحويل القواميس إلى قوائم
        clinics_list = []
        for clinic_name, clinic_data in clinics.items():
            for category in clinic_data['categories'].values():
                category['drugs'] = list(category['drugs'].values())
            clinic_data['categories'] = list(clinic_data['categories'].values())
            clinics_list.append(clinic_data)

        area_categories_list = []
        for cat in area_categories.values():
            cat['drugs'] = list(cat['drugs'].values())
            area_categories_list.append(cat)

        branch_categories_list = []
        for cat in branch_categories.values():
            cat['drugs'] = list(cat['drugs'].values())
            branch_categories_list.append(cat)

        # إحصائيات عامة
        total_cost = sum(item['total_cost'] for item in drugs_data)
        total_quantity = sum(item['total_quantity'] for item in drugs_data)
        total_cases = sum(item['total_cases'] for item in drugs_data)
        unique_drugs = len(set(item['drug_name'] for item in drugs_data))

        # معلومات المنطقة والفئة المحددة
        area_name = "جميع المناطق"
        branch_name = "جميع الفروع"
        category_name = "جميع التصنيفات"

        if area_id:
            area_info = conn.execute('''
                SELECT a.name as area_name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (area_id,)).fetchone()
            if area_info:
                area_name = area_info['area_name']
                branch_name = area_info['branch_name']

        if category_id:
            category_info = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_info:
                category_name = category_info['name']

        return render_template('new_monthly_report.html',
                             clinics=clinics_list,
                             area_categories=area_categories_list,
                             branch_categories=branch_categories_list,
                             month=month,
                             area_name=area_name,
                             branch_name=branch_name,
                             category_name=category_name,
                             scope_type=scope_type,
                             total_cost=total_cost,
                             total_quantity=total_quantity,
                             total_cases=total_cases,
                             unique_drugs=unique_drugs)

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()
# تقرير الأنسولين الجديد
@app.route('/reports/insulin')
def insulin_report_new():
    scope_type = request.args.get('scope_type', 'all')
    date_range = request.args.get('date_range', 'month')
    category = request.args.get('category')
    insulin_type = request.args.get('type')  # نوع الأنسولين (مدعم/هيئة)
    group_by = request.args.get('group_by', 'name_price')  # طريقة التجميع
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية
        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month}'"
        elif date_range == 'custom' and start_date and end_date:
            date_filter = f"i.dispense_month BETWEEN '{start_date}' AND '{end_date}'"
        else:
            date_filter = "1=1"

        # بناء شروط إضافية
        where_conditions = [date_filter]
        if category:
            where_conditions.append(f"i.category = '{category}'")
        if insulin_type:
            where_conditions.append(f"i.type = '{insulin_type}'")
        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات الأنسولين المنصرف
        try:
            print(f"🔍 استعلام الأنسولين مع الشروط: {where_clause}")
            print(f"🔍 المعاملات: branch_id={branch_id}, area_id={area_id}, clinic_id={clinic_id}")

            # التحقق من وجود جدول insulin_dispensed
            table_exists = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='insulin_dispensed'").fetchone()
            if not table_exists:
                print("❌ جدول insulin_dispensed غير موجود!")
            else:
                print("✅ جدول insulin_dispensed موجود")
                # عرض بنية الجدول
                columns = conn.execute("PRAGMA table_info(insulin_dispensed)").fetchall()
                print("📋 أعمدة جدول insulin_dispensed:")
                for col in columns:
                    print(f"  - {col[1]} ({col[2]})")

            insulin_data = conn.execute(f'''
                SELECT
                    COALESCE(i.name, 'غير محدد') as name,
                    COALESCE(i.type, 'غير محدد') as type,
                    COALESCE(i.category, 'غير محدد') as category,
                    COALESCE(i.unit, 'وحدة') as unit,
                    COALESCE(i.price, 0) as price,
                    COALESCE(SUM(i.quantity), 0) as quantity,
                    COALESCE(SUM(i.cases_count), 0) as cases_count,
                    COALESCE(SUM(i.cost), 0) as cost,
                    COALESCE(c.name, 'غير محدد') as clinic_name,
                    COALESCE(a.name, 'غير محدد') as area_name,
                    COALESCE(b.name, 'غير محدد') as branch_name
                FROM insulin_dispensed i
                JOIN clinics c ON i.clinic_id = c.id
                JOIN areas a ON i.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                GROUP BY i.name, i.type, i.category, i.unit, i.price, c.name, a.name, b.name
                ORDER BY cost DESC
            ''').fetchall()

            print(f"عدد النتائج من قاعدة البيانات: {len(insulin_data)}")

        except Exception as e:
            print(f"خطأ في استعلام الأنسولين: {e}")
            insulin_data = []

        # إذا لم توجد بيانات، نعرض رسالة للمستخدم
        if not insulin_data:
            print("لا توجد بيانات في قاعدة البيانات")
            # نعيد قائمة فارغة بدلاً من البيانات التجريبية
            insulin_data = []

        # إحصائيات عامة
        total_cost = sum(float(item['cost']) for item in insulin_data)
        total_quantity = sum(float(item['quantity']) for item in insulin_data)
        total_cases = sum(int(item['cases_count']) for item in insulin_data)
        unique_types = len(set(item['type'] for item in insulin_data))

        # تحويل البيانات للقالب
        insulin_items = []
        for item in insulin_data:
            insulin_items.append({
                'name': str(item['name']),
                'type': str(item['type']),
                'category': str(item['category']),
                'unit': str(item['unit']),
                'price': float(item['price']),
                'quantity': float(item['quantity']),
                'cases_count': int(item['cases_count']),
                'cost': float(item['cost']),
                'location_name': str(item['branch_name'])
            })

        return render_template('insulin_report_simple.html',
                             insulin_items=insulin_items,
                             total_cost=total_cost,
                             total_quantity=total_quantity,
                             total_cases=total_cases,
                             unique_types=unique_types,
                             scope_type=scope_type,
                             date_range=date_range,
                             report_title='تقرير الأنسولين',
                             period=f'الشهر الحالي ({datetime.now().strftime("%Y-%m")})' if date_range == 'month' else 'فترة مخصصة',
                             scope_name='جميع المناطق',
                             scope_type_text='النطاق',
                             group_by='category',
                             show_location=True,
                             location_type='الفرع',
                             now=datetime.now(),
                             category='',
                             current_year=datetime.now().year)
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء تقرير الأنسولين: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير الأنسولين المحسن
@app.route('/reports/insulin-enhanced')
def insulin_report_enhanced():
    """تقرير الأنسولين المحسن مع خيارات تصفية إضافية"""
    # استلام المعاملات
    date_range = request.args.get('date_range', 'month')
    category = request.args.get('category')  # فئة المريض (هيئة/طلاب/رضع/مرأة معيلة)
    insulin_type = request.args.get('type')  # نوع الأنسولين (مدعم/هيئة)
    filter_type = request.args.get('filter_type', 'category')  # نوع التصفية (فئة/نوع)
    scope_type = request.args.get('scope_type', 'all')  # نطاق التقرير
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية - نفس المنطق المحسن من التقارير الأخرى
        current_year = datetime.now().year
        current_month = datetime.now().month

        if date_range == 'month':
            current_month_str = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month_str}'"
            # تنسيق التاريخ إلى MM/YYYY
            formatted_month = format_month_year(current_month_str + '-01')
            date_range_text = f"الشهر الحالي ({formatted_month})"
        elif date_range == 'current_and_previous_month':
            # الشهر الحالي والشهر السابق
            current_date = datetime.now()
            current_month_str = current_date.strftime('%Y-%m')
            previous_month = (current_date.replace(day=1) - timedelta(days=1)).strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{previous_month}', '{current_month_str}')"
            start_formatted = format_month_year(previous_month + '-01')
            end_formatted = format_month_year(current_month_str + '-01')
            date_range_text = f"الشهر الحالي والسابق (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_3_months':
            # آخر 3 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(3):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 3 أشهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_6_months':
            # آخر 6 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(6):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 6 أشهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_12_months':
            # آخر 12 شهر (سنة كاملة)
            current_date = datetime.now()
            months_list = []
            for i in range(12):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 12 شهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'first_half_year':
            # النصف الأول من السنة (يناير - يونيو)
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(1, 7)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"النصف الأول من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'second_half_year':
            # النصف الثاني من السنة (يوليو - ديسمبر)
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(7, 13)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"النصف الثاني من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'quarter':
            # تحديد الربع الحالي
            current_quarter = (current_month - 1) // 3 + 1
            quarter_months = {
                1: ['01', '02', '03'],
                2: ['04', '05', '06'],
                3: ['07', '08', '09'],
                4: ['10', '11', '12']
            }
            months = quarter_months[current_quarter]
            months_str = "', '".join([f"{current_year}-{month}" for month in months])
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(f"{current_year}-{months[0]}-01")
            end_formatted = format_month_year(f"{current_year}-{months[-1]}-01")
            date_range_text = f"الربع {current_quarter} من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q1':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(1, 4)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الأول {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q2':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(4, 7)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الثاني {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q3':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(7, 10)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الثالث {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q4':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(10, 13)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الرابع {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'year':
            date_filter = f"strftime('%Y', i.dispense_month) = '{current_year}'"
            date_range_text = f"السنة الحالية ({current_year})"
        elif date_range == 'custom':
            if start_date and end_date:
                # إذا كان التنسيق YYYY-MM (شهر) نحوله إلى تاريخ كامل
                if len(start_date) == 7:  # YYYY-MM
                    start_date_full = start_date + '-01'
                    # آخر يوم في الشهر
                    from calendar import monthrange
                    year, month = map(int, end_date.split('-'))
                    last_day = monthrange(year, month)[1]
                    end_date_full = end_date + f'-{last_day:02d}'
                else:  # تاريخ كامل
                    start_date_full = start_date
                    end_date_full = end_date

                date_filter = f"i.dispense_month BETWEEN '{start_date_full}' AND '{end_date_full}'"
                # تنسيق التواريخ إلى MM/YYYY
                start_formatted = format_month_year(start_date_full)
                end_formatted = format_month_year(end_date_full)
                date_range_text = f"فترة مخصصة (من {start_formatted} إلى {end_formatted})"
            else:
                # إذا لم يتم تحديد تواريخ، استخدم الشهر الحالي كافتراضي
                current_month_str = datetime.now().strftime('%Y-%m')
                date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month_str}'"
                formatted_month = format_month_year(current_month_str + '-01')
                date_range_text = f"الشهر الحالي ({formatted_month}) - لم يتم تحديد فترة مخصصة"
        else:
            # افتراضي: الشهر الحالي
            current_month_str = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month_str}'"
            formatted_month = format_month_year(current_month_str + '-01')
            date_range_text = f"الشهر الحالي ({formatted_month})"

        # بناء شروط إضافية
        where_conditions = [date_filter]

        # تحديد نوع التصفية (فئة أو نوع)
        if filter_type == 'category' and category:
            where_conditions.append(f"i.category = '{category}'")
            filter_text = f"الفئة: {category}"
        elif filter_type == 'type' and insulin_type:
            where_conditions.append(f"i.type = '{insulin_type}'")
            filter_text = f"النوع: {insulin_type}"
        else:
            filter_text = "جميع الأنواع والفئات"

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # تحديد معلومات المكان
        if clinic_id:
            location_type = "العيادة"
            show_location = True
        elif area_id:
            location_type = "المنطقة"
            show_location = True
        elif branch_id:
            location_type = "الفرع"
            show_location = True
        else:
            location_type = "الموقع"
            show_location = False

        # بناء عنوان التقرير
        report_title = "تقرير الأنسولين"
        if clinic_id:
            clinic_name = conn.execute("SELECT name FROM clinics WHERE id = ?", (clinic_id,)).fetchone()
            if clinic_name:
                report_title += f" - عيادة {clinic_name['name']}"
        elif area_id:
            area_name = conn.execute("SELECT name FROM areas WHERE id = ?", (area_id,)).fetchone()
            if area_name:
                report_title += f" - منطقة {area_name['name']}"
        elif branch_id:
            branch_name = conn.execute("SELECT name FROM branches WHERE id = ?", (branch_id,)).fetchone()
            if branch_name:
                report_title += f" - فرع {branch_name['name']}"

        # استعلام البيانات
        print(f"🔍 استعلام الأنسولين المحسن مع الشروط: {where_clause}")

        insulin_data = conn.execute(f'''
            SELECT
                i.id,
                i.name,
                i.type,
                i.category,
                i.unit,
                i.price,
                i.quantity,
                i.cases_count,
                i.rate as rate,
                COALESCE(i.balance, 0) as id_number,
                (i.price * i.quantity) as cost,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name
            FROM insulin_dispensed i
            LEFT JOIN clinics c ON i.clinic_id = c.id
            LEFT JOIN areas a ON c.area_id = a.id
            LEFT JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
            ORDER BY i.name, i.price
        ''').fetchall()

        # حساب الإجماليات
        total_cost = sum(item['cost'] for item in insulin_data)
        total_quantity = sum(item['quantity'] for item in insulin_data)
        total_cases = sum(item['cases_count'] for item in insulin_data)

        # الحصول على قائمة أنواع الأنسولين
        insulin_types = conn.execute('SELECT id, name FROM insulin_types ORDER BY name').fetchall()

        # الحصول على قائمة فئات المرضى
        patient_categories = [
            {'id': 1, 'name': 'هيئة'},
            {'id': 2, 'name': 'طلاب'},
            {'id': 3, 'name': 'رضع'},
            {'id': 4, 'name': 'مرأة معيلة'}
        ]

        # إضافة التاريخ الحالي
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M')

        # الحصول على قائمة الفروع
        branches = conn.execute('SELECT id, name FROM branches ORDER BY name').fetchall()

        # الحصول على قائمة المناطق إذا تم تحديد فرع
        areas = None
        if branch_id:
            areas = conn.execute('SELECT id, name FROM areas WHERE branch_id = ? ORDER BY name', (branch_id,)).fetchall()

        # الحصول على قائمة العيادات إذا تم تحديد منطقة
        clinics = None
        if area_id:
            clinics = conn.execute('SELECT id, name FROM clinics WHERE area_id = ? ORDER BY name', (area_id,)).fetchall()

        return render_template('insulin_report_enhanced.html',
                            insulin_items=insulin_data,
                            title=report_title,
                            show_location=show_location,
                            location_type=location_type,
                            date_range_text=date_range_text,
                            filter_type=filter_type,
                            filter_text=filter_text,
                            total_cost=total_cost,
                            total_quantity=total_quantity,
                            total_cases=total_cases,
                            current_date=current_date,
                            branches=branches,
                            areas=areas,
                            clinics=clinics,
                            scope_type=scope_type)

    except Exception as e:
        print(f"خطأ في تقرير الأنسولين المحسن: {e}")
        import traceback
        traceback.print_exc()
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

    finally:
        conn.close()

# تقرير الأنسولين المجمع
@app.route('/reports/insulin-grouped')
def insulin_report_grouped():
    """تقرير الأنسولين المجمع حسب الفئة والنوع"""
    # استلام المعاملات
    date_range = request.args.get('date_range', 'month')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    start_month = request.args.get('start_month')
    end_month = request.args.get('end_month')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية - نفس المنطق المحسن من التقارير الأخرى
        current_year = datetime.now().year
        current_month = datetime.now().month

        if date_range == 'month':
            current_month_str = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month_str}'"
            # تنسيق التاريخ إلى MM/YYYY
            formatted_month = format_month_year(current_month_str + '-01')
            date_range_text = f"الشهر الحالي ({formatted_month})"
        elif date_range == 'current_and_previous_month':
            # الشهر الحالي والشهر السابق
            current_date = datetime.now()
            current_month_str = current_date.strftime('%Y-%m')
            previous_month = (current_date.replace(day=1) - timedelta(days=1)).strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{previous_month}', '{current_month_str}')"
            start_formatted = format_month_year(previous_month + '-01')
            end_formatted = format_month_year(current_month_str + '-01')
            date_range_text = f"الشهر الحالي والسابق (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_3_months':
            # آخر 3 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(3):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 3 أشهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_6_months':
            # آخر 6 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(6):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 6 أشهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_12_months':
            # آخر 12 شهر (سنة كاملة)
            current_date = datetime.now()
            months_list = []
            for i in range(12):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 12 شهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'first_half_year':
            # النصف الأول من السنة (يناير - يونيو)
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(1, 7)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"النصف الأول من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'second_half_year':
            # النصف الثاني من السنة (يوليو - ديسمبر)
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(7, 13)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"النصف الثاني من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'quarter':
            # تحديد الربع الحالي
            current_quarter = (current_month - 1) // 3 + 1
            quarter_months = {
                1: ['01', '02', '03'],
                2: ['04', '05', '06'],
                3: ['07', '08', '09'],
                4: ['10', '11', '12']
            }
            months = quarter_months[current_quarter]
            months_str = "', '".join([f"{current_year}-{month}" for month in months])
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(f"{current_year}-{months[0]}-01")
            end_formatted = format_month_year(f"{current_year}-{months[-1]}-01")
            date_range_text = f"الربع {current_quarter} من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q1':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(1, 4)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الأول {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q2':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(4, 7)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الثاني {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q3':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(7, 10)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الثالث {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'q4':
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(10, 13)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', i.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"الربع الرابع {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'year':
            date_filter = f"strftime('%Y', i.dispense_month) = '{current_year}'"
            date_range_text = f"السنة الحالية ({current_year})"
        elif date_range == 'custom':
            if start_month and end_month:
                date_filter = f"strftime('%Y-%m', i.dispense_month) BETWEEN '{start_month}' AND '{end_month}'"
                # تنسيق التواريخ إلى MM/YYYY
                start_formatted = format_month_year(start_month + '-01')
                end_formatted = format_month_year(end_month + '-01')
                date_range_text = f"فترة مخصصة (من {start_formatted} إلى {end_formatted})"
            elif start_date and end_date:
                date_filter = f"i.dispense_month BETWEEN '{start_date}' AND '{end_date}'"
                date_range_text = f"الفترة من {start_date} إلى {end_date}"
            else:
                date_filter = "1=1"
                date_range_text = "فترة مخصصة غير محددة"

        else:
            date_filter = "1=1"
            date_range_text = "كل الفترات"

        # بناء شروط إضافية
        where_conditions = [date_filter]

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # تحديد معلومات المكان
        if clinic_id:
            location_type = "العيادة"
            show_location = True
        elif area_id:
            location_type = "المنطقة"
            show_location = True
        elif branch_id:
            location_type = "الفرع"
            show_location = True
        else:
            location_type = "الموقع"
            show_location = False

        # بناء عنوان التقرير
        report_title = "تقرير الأنسولين المجمع"
        if clinic_id:
            clinic_name = conn.execute("SELECT name FROM clinics WHERE id = ?", (clinic_id,)).fetchone()
            if clinic_name:
                report_title += f" - عيادة {clinic_name['name']}"
        elif area_id:
            area_name = conn.execute("SELECT name FROM areas WHERE id = ?", (area_id,)).fetchone()
            if area_name:
                report_title += f" - منطقة {area_name['name']}"
        elif branch_id:
            branch_name = conn.execute("SELECT name FROM branches WHERE id = ?", (branch_id,)).fetchone()
            if branch_name:
                report_title += f" - فرع {branch_name['name']}"

        # استعلام البيانات
        print(f"🔍 استعلام الأنسولين المجمع مع الشروط: {where_clause}")

        # الحصول على جميع الفئات والأنواع المتوفرة
        categories_and_types = conn.execute(f'''
            SELECT DISTINCT category, type
            FROM insulin_dispensed i
            LEFT JOIN clinics c ON i.clinic_id = c.id
            LEFT JOIN areas a ON c.area_id = a.id
            LEFT JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
            ORDER BY category, type
        ''').fetchall()

        # إنشاء قاموس لتخزين البيانات المجمعة
        grouped_data = {}
        total_cost = 0
        total_quantity = 0
        total_cases = 0

        # استعلام البيانات لكل مجموعة (فئة + نوع)
        for group in categories_and_types:
            category = group['category']
            insulin_type = group['type']

            group_data = conn.execute(f'''
                SELECT
                    i.id,
                    i.name,
                    i.type,
                    i.category,
                    i.unit,
                    i.price,
                    i.quantity,
                    i.cases_count,
                    COALESCE(i.rate, '-') as rate,
                    COALESCE(i.balance, 0) as id_number,
                    (i.price * i.quantity) as cost,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name
                FROM insulin_dispensed i
                LEFT JOIN clinics c ON i.clinic_id = c.id
                LEFT JOIN areas a ON c.area_id = a.id
                LEFT JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause} AND i.category = ? AND i.type = ?
                ORDER BY i.name, i.price
            ''', (category, insulin_type)).fetchall()

            # حساب الإجماليات لهذه المجموعة
            group_cost = sum(item['cost'] for item in group_data)
            group_quantity = sum(item['quantity'] for item in group_data)
            group_cases = sum(item['cases_count'] for item in group_data)

            # إضافة إلى الإجماليات الكلية
            total_cost += group_cost
            total_quantity += group_quantity
            total_cases += group_cases

            # تخزين البيانات والإحصائيات
            group_key = f"{category} - {insulin_type}"
            grouped_data[group_key] = {
                'data': group_data,
                'cost': group_cost,
                'quantity': group_quantity,
                'cases': group_cases,
                'category': category,
                'type': insulin_type
            }

        # إضافة التاريخ الحالي
        current_date = datetime.now().strftime('%Y-%m-%d %H:%M')

        return render_template('insulin_report_grouped.html',
                            grouped_data=grouped_data,
                            title=report_title,
                            show_location=show_location,
                            location_type=location_type,
                            date_range_text=date_range_text,
                            total_cost=total_cost,
                            total_quantity=total_quantity,
                            total_cases=total_cases,
                            current_date=current_date)

    except Exception as e:
        import traceback
        print(f"خطأ في تقرير الأنسولين المجمع: {e}")
        traceback.print_exc()
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

    finally:
        conn.close()

# تصدير تقرير الأنسولين المجمع إلى Excel
@app.route('/reports/insulin-grouped/export-excel')
def export_insulin_grouped_excel():
    """تصدير تقرير الأنسولين المجمع إلى Excel"""
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    except ImportError:
        flash('مكتبة openpyxl غير مثبتة. يرجى تثبيتها لتصدير ملفات Excel', 'error')
        return redirect(url_for('insulin_report_grouped'))

    # استلام المعاملات
    date_range = request.args.get('date_range', 'month')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    start_month = request.args.get('start_month')
    end_month = request.args.get('end_month')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية (نفس المنطق من التقرير الأصلي)
        current_year = datetime.now().year
        current_month = datetime.now().month

        if date_range == 'month':
            current_month_str = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month_str}'"
            date_range_text = f"شهر {datetime.now().strftime('%Y-%m')}"
        elif date_range == 'quarter':
            current_quarter = (current_month - 1) // 3 + 1
            quarter_start_month = (current_quarter - 1) * 3 + 1
            quarter_end_month = quarter_start_month + 2
            quarter_start = f"{current_year}-{quarter_start_month:02d}-01"
            if quarter_end_month == 12:
                quarter_end = f"{current_year}-12-31"
            else:
                quarter_end = f"{current_year}-{quarter_end_month+1:02d}-01"
            date_filter = f"i.dispense_month >= '{quarter_start}' AND i.dispense_month < '{quarter_end}'"
            date_range_text = f"الربع {current_quarter} من سنة {current_year}"
        elif date_range == 'q1':
            date_filter = f"i.dispense_month >= '{current_year}-01-01' AND i.dispense_month < '{current_year}-04-01'"
            date_range_text = f"الربع الأول (يناير - مارس) {current_year}"
        elif date_range == 'q2':
            date_filter = f"i.dispense_month >= '{current_year}-04-01' AND i.dispense_month < '{current_year}-07-01'"
            date_range_text = f"الربع الثاني (أبريل - يونيو) {current_year}"
        elif date_range == 'q3':
            date_filter = f"i.dispense_month >= '{current_year}-07-01' AND i.dispense_month < '{current_year}-10-01'"
            date_range_text = f"الربع الثالث (يوليو - سبتمبر) {current_year}"
        elif date_range == 'q4':
            date_filter = f"i.dispense_month >= '{current_year}-10-01' AND i.dispense_month <= '{current_year}-12-31'"
            date_range_text = f"الربع الرابع (أكتوبر - ديسمبر) {current_year}"
        elif date_range == 'year':
            date_filter = f"strftime('%Y', i.dispense_month) = '{current_year}'"
            date_range_text = f"سنة {current_year}"
        elif date_range == 'custom':
            if start_month and end_month:
                date_filter = f"strftime('%Y-%m', i.dispense_month) BETWEEN '{start_month}' AND '{end_month}'"
                date_range_text = f"الفترة من {start_month} إلى {end_month}"
            elif start_date and end_date:
                date_filter = f"i.dispense_month BETWEEN '{start_date}' AND '{end_date}'"
                date_range_text = f"الفترة من {start_date} إلى {end_date}"
            else:
                date_filter = "1=1"
                date_range_text = "فترة مخصصة غير محددة"
        else:
            date_filter = "1=1"
            date_range_text = "كل الفترات"

        # بناء شروط إضافية
        where_conditions = [date_filter]
        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بناء عنوان التقرير
        report_title = "تقرير الأنسولين المجمع"
        if clinic_id:
            clinic_name = conn.execute("SELECT name FROM clinics WHERE id = ?", (clinic_id,)).fetchone()
            if clinic_name:
                report_title += f" - عيادة {clinic_name['name']}"
        elif area_id:
            area_name = conn.execute("SELECT name FROM areas WHERE id = ?", (area_id,)).fetchone()
            if area_name:
                report_title += f" - منطقة {area_name['name']}"
        elif branch_id:
            branch_name = conn.execute("SELECT name FROM branches WHERE id = ?", (branch_id,)).fetchone()
            if branch_name:
                report_title += f" - فرع {branch_name['name']}"

        # الحصول على جميع الفئات والأنواع المتوفرة
        categories_and_types = conn.execute(f'''
            SELECT DISTINCT category, type
            FROM insulin_dispensed i
            LEFT JOIN clinics c ON i.clinic_id = c.id
            LEFT JOIN areas a ON c.area_id = a.id
            LEFT JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
            ORDER BY category, type
        ''').fetchall()

        # إنشاء ملف Excel
        wb = Workbook()
        wb.remove(wb.active)

        # تنسيق الخلايا
        header_font = Font(bold=True, color="FFFFFF", size=12)
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        title_font = Font(bold=True, size=14)
        center_alignment = Alignment(horizontal="center", vertical="center")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # إنشاء صفحة لكل مجموعة (فئة + نوع)
        for group in categories_and_types:
            category = group['category']
            insulin_type = group['type']

            # إنشاء اسم الصفحة
            sheet_name = f"{category}-{insulin_type}"[:31]  # Excel sheet name limit
            ws = wb.create_sheet(sheet_name)

            # إضافة عنوان التقرير
            ws.merge_cells('A1:L1')
            title_cell = ws['A1']
            title_cell.value = f"{report_title} - {category} - {insulin_type}"
            title_cell.font = title_font
            title_cell.alignment = center_alignment

            # إضافة معلومات التقرير
            ws.merge_cells('A2:L2')
            info_cell = ws['A2']
            info_cell.value = f"الفترة: {date_range_text} | تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            info_cell.alignment = center_alignment

            # إضافة العناوين
            headers = ["#", "الصنف", "النوع", "الفئة", "الوحدة", "السعر", "الكمية", "المعدل", "الرصيد", "عدد الحالات", "التكلفة", "الموقع"]
            for col_num, header in enumerate(headers, 1):
                cell = ws.cell(row=4, column=col_num)
                cell.value = header
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
                cell.border = border

            # الحصول على البيانات لهذه المجموعة
            group_data = conn.execute(f'''
                SELECT
                    i.id,
                    i.name,
                    i.type,
                    i.category,
                    i.unit,
                    i.price,
                    i.quantity,
                    i.rate,
                    COALESCE(i.balance, 0) as balance,
                    i.cases_count,
                    (i.price * i.quantity) as cost,
                    COALESCE(c.name, a.name, b.name) as location_name
                FROM insulin_dispensed i
                LEFT JOIN clinics c ON i.clinic_id = c.id
                LEFT JOIN areas a ON c.area_id = a.id
                LEFT JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause} AND i.category = ? AND i.type = ?
                ORDER BY i.name, i.price
            ''', (category, insulin_type)).fetchall()

            # إضافة البيانات
            for row_num, item in enumerate(group_data, 5):
                ws.cell(row=row_num, column=1, value=row_num-4)
                ws.cell(row=row_num, column=2, value=item['name'])
                ws.cell(row=row_num, column=3, value=item['type'])
                ws.cell(row=row_num, column=4, value=item['category'])
                ws.cell(row=row_num, column=5, value=item['unit'])
                ws.cell(row=row_num, column=6, value=item['price'])
                ws.cell(row=row_num, column=7, value=item['quantity'])
                ws.cell(row=row_num, column=8, value=item['rate'] or '-')
                ws.cell(row=row_num, column=9, value=item['balance'])
                ws.cell(row=row_num, column=10, value=item['cases_count'])
                ws.cell(row=row_num, column=11, value=item['cost'])
                ws.cell(row=row_num, column=12, value=item['location_name'])

                # إضافة حدود للخلايا
                for col in range(1, 13):
                    ws.cell(row=row_num, column=col).border = border

            # إضافة إجمالي المجموعة
            if group_data:
                total_row = len(group_data) + 5
                ws.merge_cells(f'A{total_row}:I{total_row}')
                total_cell = ws[f'A{total_row}']
                total_cell.value = "إجمالي المجموعة:"
                total_cell.font = Font(bold=True)
                total_cell.alignment = center_alignment

                total_cases = sum(item['cases_count'] for item in group_data)
                total_cost = sum(item['cost'] for item in group_data)

                ws.cell(row=total_row, column=10, value=total_cases).font = Font(bold=True)
                ws.cell(row=total_row, column=11, value=total_cost).font = Font(bold=True)
                ws.cell(row=total_row, column=12, value="-").font = Font(bold=True)

            # تعديل عرض الأعمدة
            column_widths = [5, 25, 12, 15, 10, 10, 10, 10, 10, 12, 12, 20]
            for i, width in enumerate(column_widths, 1):
                from openpyxl.utils import get_column_letter
                ws.column_dimensions[get_column_letter(i)].width = width

        # حفظ الملف
        from io import BytesIO
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"تقرير_الأنسولين_المجمع_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('insulin_report_grouped'))

    finally:
        conn.close()

# تصدير تقرير الأنسولين المجمع إلى PDF (معطل - يتم التصدير من JavaScript)
@app.route('/reports/insulin-grouped/export-pdf')
def export_insulin_grouped_pdf_disabled():
    """تصدير تقرير الأنسولين المجمع إلى PDF - معطل"""
    flash('تم تعطيل تصدير PDF من الخادم. يتم التصدير الآن من المتصفح مباشرة.', 'info')
    return redirect(url_for('insulin_report_grouped'))
    try:
        # التحقق من وجود مكتبة WeasyPrint
        try:
            import weasyprint
        except ImportError:
            flash('مكتبة WeasyPrint غير مثبتة. يرجى تثبيتها باستخدام الأمر: pip install weasyprint', 'error')
            return redirect(url_for('insulin_report_grouped'))

        # استلام المعاملات
        date_range = request.args.get('date_range', 'month')
        branch_id = request.args.get('branch_id')
        area_id = request.args.get('area_id')
        clinic_id = request.args.get('clinic_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # استدعاء نفس الدالة التي تعرض التقرير ولكن مع قالب مختلف
        # نستخدم نفس المنطق الموجود في دالة insulin_report_grouped
        conn = get_db_connection()
        try:
            # تحديد الفترة الزمنية (نفس المنطق من التقرير الأصلي)
            current_year = datetime.now().year
            current_month = datetime.now().month

            if date_range == 'month':
                current_month_str = datetime.now().strftime('%Y-%m')
                date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month_str}'"
                date_range_text = f"شهر {datetime.now().strftime('%Y-%m')}"
            elif date_range == 'quarter':
                current_quarter = (current_month - 1) // 3 + 1
                quarter_start_month = (current_quarter - 1) * 3 + 1
                quarter_end_month = quarter_start_month + 2
                quarter_start = f"{current_year}-{quarter_start_month:02d}-01"
                if quarter_end_month == 12:
                    quarter_end = f"{current_year}-12-31"
                else:
                    quarter_end = f"{current_year}-{quarter_end_month+1:02d}-01"
                date_filter = f"i.dispense_month >= '{quarter_start}' AND i.dispense_month < '{quarter_end}'"
                date_range_text = f"الربع {current_quarter} من سنة {current_year}"
            elif date_range == 'q1':
                date_filter = f"i.dispense_month >= '{current_year}-01-01' AND i.dispense_month < '{current_year}-04-01'"
                date_range_text = f"الربع الأول (يناير - مارس) {current_year}"
            elif date_range == 'q2':
                date_filter = f"i.dispense_month >= '{current_year}-04-01' AND i.dispense_month < '{current_year}-07-01'"
                date_range_text = f"الربع الثاني (أبريل - يونيو) {current_year}"
            elif date_range == 'q3':
                date_filter = f"i.dispense_month >= '{current_year}-07-01' AND i.dispense_month < '{current_year}-10-01'"
                date_range_text = f"الربع الثالث (يوليو - سبتمبر) {current_year}"
            elif date_range == 'q4':
                date_filter = f"i.dispense_month >= '{current_year}-10-01' AND i.dispense_month <= '{current_year}-12-31'"
                date_range_text = f"الربع الرابع (أكتوبر - ديسمبر) {current_year}"
            elif date_range == 'year':
                date_filter = f"strftime('%Y', i.dispense_month) = '{current_year}'"
                date_range_text = f"سنة {current_year}"
            elif date_range == 'custom' and start_date and end_date:
                date_filter = f"i.dispense_month BETWEEN '{start_date}' AND '{end_date}'"
                date_range_text = f"الفترة من {start_date} إلى {end_date}"
            else:
                date_filter = "1=1"
                date_range_text = "كل الفترات"

            # بناء شروط إضافية
            where_conditions = [date_filter]
            if branch_id:
                where_conditions.append(f"b.id = {branch_id}")
            if area_id:
                where_conditions.append(f"a.id = {area_id}")
            if clinic_id:
                where_conditions.append(f"c.id = {clinic_id}")

            where_clause = " AND ".join(where_conditions)

            # بناء عنوان التقرير
            report_title = "تقرير الأنسولين المجمع"
            show_location = False
            location_type = ""

            if clinic_id:
                clinic_name = conn.execute("SELECT name FROM clinics WHERE id = ?", (clinic_id,)).fetchone()
                if clinic_name:
                    report_title += f" - عيادة {clinic_name['name']}"
                    show_location = True
                    location_type = "العيادة"
            elif area_id:
                area_name = conn.execute("SELECT name FROM areas WHERE id = ?", (area_id,)).fetchone()
                if area_name:
                    report_title += f" - منطقة {area_name['name']}"
                    show_location = True
                    location_type = "المنطقة"
            elif branch_id:
                branch_name = conn.execute("SELECT name FROM branches WHERE id = ?", (branch_id,)).fetchone()
                if branch_name:
                    report_title += f" - فرع {branch_name['name']}"
                    show_location = True
                    location_type = "الفرع"

            # الحصول على جميع الفئات والأنواع المتوفرة
            categories_and_types = conn.execute(f'''
                SELECT DISTINCT category, type
                FROM insulin_dispensed i
                LEFT JOIN clinics c ON i.clinic_id = c.id
                LEFT JOIN areas a ON c.area_id = a.id
                LEFT JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                ORDER BY category, type
            ''').fetchall()

            # إنشاء قائمة لتخزين بيانات كل مجموعة
            groups_data = []

            # إجماليات عامة
            total_cases_all = 0
            total_cost_all = 0

            # الحصول على بيانات كل مجموعة
            for group in categories_and_types:
                category = group['category']
                insulin_type = group['type']

                # الحصول على البيانات لهذه المجموعة
                group_data = conn.execute(f'''
                    SELECT
                        i.id,
                        i.name,
                        i.type,
                        i.category,
                        i.unit,
                        i.price,
                        i.quantity,
                        i.rate,
                        COALESCE(i.balance, 0) as balance,
                        i.cases_count,
                        (i.price * i.quantity) as cost,
                        c.name as clinic_name,
                        a.name as area_name,
                        b.name as branch_name
                    FROM insulin_dispensed i
                    LEFT JOIN clinics c ON i.clinic_id = c.id
                    LEFT JOIN areas a ON c.area_id = a.id
                    LEFT JOIN branches b ON a.branch_id = b.id
                    WHERE {where_clause} AND i.category = ? AND i.type = ?
                    ORDER BY i.name, i.price
                ''', (category, insulin_type)).fetchall()

                # حساب إجماليات المجموعة
                group_total_cases = sum(item['cases_count'] for item in group_data)
                group_total_cost = sum(item['cost'] for item in group_data)

                # إضافة إلى الإجماليات العامة
                total_cases_all += group_total_cases
                total_cost_all += group_total_cost

                # إضافة المجموعة إلى القائمة
                groups_data.append({
                    'category': category,
                    'type': insulin_type,
                    'data': group_data,
                    'total_cases': group_total_cases,
                    'total_cost': group_total_cost
                })

            # إنشاء HTML للتقرير
            from flask import render_template
            html_content = render_template(
                'insulin_report_grouped_pdf.html',
                title=report_title,
                date_range_text=date_range_text,
                current_date=datetime.now().strftime('%Y-%m-%d %H:%M'),
                groups_data=groups_data,
                show_location=show_location,
                location_type=location_type,
                total_cases_all=total_cases_all,
                total_cost_all=total_cost_all
            )

            # تحويل HTML إلى PDF
            try:
                from weasyprint import HTML
                # إنشاء كائن HTML وتحويله إلى PDF
                html_doc = HTML(string=html_content)
                pdf_content = html_doc.write_pdf()

                # إنشاء استجابة مع ملف PDF
                response = make_response(pdf_content)
                response.headers['Content-Type'] = 'application/pdf'
                response.headers['Content-Disposition'] = f'attachment; filename=تقرير_الأنسولين_المجمع_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'

                return response
            except ImportError:
                flash('مكتبة WeasyPrint غير مثبتة. يرجى تثبيتها باستخدام: pip install weasyprint', 'error')
                return redirect(url_for('insulin_report_grouped'))
            except Exception as pdf_error:
                print(f"خطأ في تحويل PDF: {pdf_error}")
                import traceback
                traceback.print_exc()
                flash(f'حدث خطأ أثناء تحويل التقرير إلى PDF: {str(pdf_error)}', 'error')
                return redirect(url_for('insulin_report_grouped'))

        except Exception as e:
            flash(f'حدث خطأ أثناء تصدير التقرير: {str(e)}', 'error')
            return redirect(url_for('insulin_report_grouped'))

        finally:
            conn.close()

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('insulin_report_grouped'))

# تصدير تقرير الأنسولين المحسن إلى Excel
@app.route('/reports/insulin-enhanced/export-excel')
def export_insulin_enhanced_excel():
    """تصدير تقرير الأنسولين المحسن إلى Excel"""
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    except ImportError:
        flash('مكتبة openpyxl غير مثبتة. يرجى تثبيتها لتصدير ملفات Excel', 'error')
        return redirect(url_for('insulin_report_enhanced'))

    # استلام المعاملات (نفس المعاملات من التقرير الأصلي)
    date_range = request.args.get('date_range', 'month')
    category = request.args.get('category')
    insulin_type = request.args.get('type')
    filter_type = request.args.get('filter_type', 'category')
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية (نفس المنطق من التقرير الأصلي)
        current_year = datetime.now().year
        current_month = datetime.now().month

        if date_range == 'month':
            current_month_str = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', i.dispense_month) = '{current_month_str}'"
            date_range_text = f"شهر {datetime.now().strftime('%Y-%m')}"
        elif date_range == 'quarter':
            current_quarter = (current_month - 1) // 3 + 1
            quarter_start_month = (current_quarter - 1) * 3 + 1
            quarter_end_month = quarter_start_month + 2
            quarter_start = f"{current_year}-{quarter_start_month:02d}-01"
            if quarter_end_month == 12:
                quarter_end = f"{current_year}-12-31"
            else:
                quarter_end = f"{current_year}-{quarter_end_month+1:02d}-01"
            date_filter = f"i.dispense_month >= '{quarter_start}' AND i.dispense_month < '{quarter_end}'"
            date_range_text = f"الربع {current_quarter} من سنة {current_year}"
        elif date_range == 'q1':
            date_filter = f"i.dispense_month >= '{current_year}-01-01' AND i.dispense_month < '{current_year}-04-01'"
            date_range_text = f"الربع الأول (يناير - مارس) {current_year}"
        elif date_range == 'q2':
            date_filter = f"i.dispense_month >= '{current_year}-04-01' AND i.dispense_month < '{current_year}-07-01'"
            date_range_text = f"الربع الثاني (أبريل - يونيو) {current_year}"
        elif date_range == 'q3':
            date_filter = f"i.dispense_month >= '{current_year}-07-01' AND i.dispense_month < '{current_year}-10-01'"
            date_range_text = f"الربع الثالث (يوليو - سبتمبر) {current_year}"
        elif date_range == 'q4':
            date_filter = f"i.dispense_month >= '{current_year}-10-01' AND i.dispense_month <= '{current_year}-12-31'"
            date_range_text = f"الربع الرابع (أكتوبر - ديسمبر) {current_year}"
        elif date_range == 'year':
            date_filter = f"strftime('%Y', i.dispense_month) = '{current_year}'"
            date_range_text = f"سنة {current_year}"
        elif date_range == 'custom' and start_date and end_date:
            date_filter = f"i.dispense_month BETWEEN '{start_date}' AND '{end_date}'"
            date_range_text = f"الفترة من {start_date} إلى {end_date}"
        else:
            date_filter = "1=1"
            date_range_text = "كل الفترات"

        # بناء شروط إضافية
        where_conditions = [date_filter]

        # تحديد نوع التصفية (فئة أو نوع)
        if filter_type == 'category' and category:
            where_conditions.append(f"i.category = '{category}'")
            filter_text = f"الفئة: {category}"
        elif filter_type == 'type' and insulin_type:
            where_conditions.append(f"i.type = '{insulin_type}'")
            filter_text = f"النوع: {insulin_type}"
        else:
            filter_text = "جميع الأنواع والفئات"

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بناء عنوان التقرير
        report_title = "تقرير الأنسولين المحسن"
        if clinic_id:
            clinic_name = conn.execute("SELECT name FROM clinics WHERE id = ?", (clinic_id,)).fetchone()
            if clinic_name:
                report_title += f" - عيادة {clinic_name['name']}"
        elif area_id:
            area_name = conn.execute("SELECT name FROM areas WHERE id = ?", (area_id,)).fetchone()
            if area_name:
                report_title += f" - منطقة {area_name['name']}"
        elif branch_id:
            branch_name = conn.execute("SELECT name FROM branches WHERE id = ?", (branch_id,)).fetchone()
            if branch_name:
                report_title += f" - فرع {branch_name['name']}"

        # استعلام البيانات
        insulin_data = conn.execute(f'''
            SELECT
                i.id,
                i.name,
                i.type,
                i.category,
                i.unit,
                i.price,
                i.quantity,
                i.cases_count,
                i.rate,
                COALESCE(i.balance, 0) as balance,
                (i.price * i.quantity) as cost,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name
            FROM insulin_dispensed i
            LEFT JOIN clinics c ON i.clinic_id = c.id
            LEFT JOIN areas a ON c.area_id = a.id
            LEFT JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
            ORDER BY i.name, i.price
        ''').fetchall()

        # إنشاء ملف Excel
        wb = Workbook()
        ws = wb.active
        ws.title = "تقرير الأنسولين المحسن"

        # تنسيق الخلايا
        header_font = Font(bold=True, color="FFFFFF", size=12)
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        title_font = Font(bold=True, size=14)
        center_alignment = Alignment(horizontal="center", vertical="center")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # إضافة عنوان التقرير
        ws.merge_cells('A1:L1')
        title_cell = ws['A1']
        title_cell.value = report_title
        title_cell.font = title_font
        title_cell.alignment = center_alignment

        # إضافة معلومات التقرير
        ws.merge_cells('A2:L2')
        info_cell = ws['A2']
        info_cell.value = f"الفترة: {date_range_text} | التصفية: {filter_text} | تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        info_cell.alignment = center_alignment

        # إضافة العناوين
        headers = ["#", "الصنف", "النوع", "الفئة", "الوحدة", "السعر", "الكمية", "المعدل", "الرصيد", "عدد الحالات", "التكلفة", "الموقع"]
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=4, column=col_num)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment
            cell.border = border

        # إضافة البيانات
        for row_num, item in enumerate(insulin_data, 5):
            ws.cell(row=row_num, column=1, value=row_num-4)
            ws.cell(row=row_num, column=2, value=item['name'])
            ws.cell(row=row_num, column=3, value=item['type'])
            ws.cell(row=row_num, column=4, value=item['category'])
            ws.cell(row=row_num, column=5, value=item['unit'])
            ws.cell(row=row_num, column=6, value=item['price'])
            ws.cell(row=row_num, column=7, value=item['quantity'])
            ws.cell(row=row_num, column=8, value=item['rate'] or '-')
            ws.cell(row=row_num, column=9, value=item['balance'])
            ws.cell(row=row_num, column=10, value=item['cases_count'])
            ws.cell(row=row_num, column=11, value=item['cost'])

            # تحديد الموقع حسب النطاق
            if clinic_id:
                location = item['clinic_name']
            elif area_id:
                location = item['area_name']
            elif branch_id:
                location = item['branch_name']
            else:
                location = f"{item['branch_name']} - {item['area_name']} - {item['clinic_name']}"

            ws.cell(row=row_num, column=12, value=location)

            # إضافة حدود للخلايا
            for col in range(1, 13):
                ws.cell(row=row_num, column=col).border = border

        # إضافة الإجماليات
        if insulin_data:
            total_row = len(insulin_data) + 5
            ws.merge_cells(f'A{total_row}:I{total_row}')
            total_cell = ws[f'A{total_row}']
            total_cell.value = "الإجمالي:"
            total_cell.font = Font(bold=True)
            total_cell.alignment = center_alignment

            total_cases = sum(item['cases_count'] for item in insulin_data)
            total_cost = sum(item['cost'] for item in insulin_data)

            ws.cell(row=total_row, column=10, value=total_cases).font = Font(bold=True)
            ws.cell(row=total_row, column=11, value=total_cost).font = Font(bold=True)
            ws.cell(row=total_row, column=12, value="-").font = Font(bold=True)

        # تعديل عرض الأعمدة
        column_widths = [5, 25, 12, 15, 10, 10, 10, 10, 10, 12, 12, 25]
        for i, width in enumerate(column_widths, 1):
            from openpyxl.utils import get_column_letter
            ws.column_dimensions[get_column_letter(i)].width = width

        # حفظ الملف
        from io import BytesIO
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        # إنشاء اسم الملف
        filename = f"تقرير_الأنسولين_المحسن_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('insulin_report_enhanced'))

    finally:
        conn.close()

# API endpoints للحصول على المناطق والعيادات
@app.route('/api/areas/<int:branch_id>')
def get_areas_by_branch(branch_id):
    """الحصول على المناطق حسب الفرع"""
    conn = get_db_connection()
    try:
        areas = conn.execute('SELECT id, name FROM areas WHERE branch_id = ? ORDER BY name', (branch_id,)).fetchall()
        return jsonify([{'id': area['id'], 'name': area['name']} for area in areas])
    finally:
        conn.close()

@app.route('/api/clinics/<int:area_id>')
def get_clinics_by_area(area_id):
    """الحصول على العيادات حسب المنطقة"""
    conn = get_db_connection()
    try:
        clinics = conn.execute('SELECT id, name FROM clinics WHERE area_id = ? ORDER BY name', (area_id,)).fetchall()
        return jsonify([{'id': clinic['id'], 'name': clinic['name']} for clinic in clinics])
    finally:
        conn.close()

# تقرير الأدوية
@app.route('/reports/drugs')
def drugs_report():
    category_id = request.args.get('category_id')
    date_range = request.args.get('date_range', 'month')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # تحديد الفترة الزمنية - نفس المنطق المحسن من تقرير المقارنة التفصيلية
        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', d.dispense_month) = '{current_month}'"
            # تنسيق التاريخ إلى MM/YYYY
            formatted_month = format_month_year(current_month + '-01')
            date_range_text = f"الشهر الحالي ({formatted_month})"
        elif date_range == 'current_and_previous_month':
            # الشهر الحالي والشهر السابق
            current_date = datetime.now()
            current_month = current_date.strftime('%Y-%m')
            previous_month = (current_date.replace(day=1) - timedelta(days=1)).strftime('%Y-%m')
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{previous_month}', '{current_month}')"
            start_formatted = format_month_year(previous_month + '-01')
            end_formatted = format_month_year(current_month + '-01')
            date_range_text = f"الشهر الحالي والسابق (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_3_months':
            # آخر 3 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(3):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 3 أشهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_6_months':
            # آخر 6 أشهر
            current_date = datetime.now()
            months_list = []
            for i in range(6):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 6 أشهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'last_12_months':
            # آخر 12 شهر (سنة كاملة)
            current_date = datetime.now()
            months_list = []
            for i in range(12):
                month_date = current_date - timedelta(days=30*i)
                months_list.append(month_date.strftime('%Y-%m'))
            months_list.reverse()  # ترتيب تصاعدي
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"آخر 12 شهر (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'quarter':
            # احتساب الربع الحالي
            current_year = datetime.now().year
            current_quarter = (datetime.now().month - 1) // 3 + 1
            quarter_months = {
                1: ['01', '02', '03'],
                2: ['04', '05', '06'],
                3: ['07', '08', '09'],
                4: ['10', '11', '12']
            }
            months = quarter_months[current_quarter]
            months_str = "', '".join([f"{current_year}-{month}" for month in months])
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(f"{current_year}-{months[0]}-01")
            end_formatted = format_month_year(f"{current_year}-{months[-1]}-01")
            date_range_text = f"الربع {current_quarter} من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range.startswith('quarter_'):
            current_year = datetime.now().year
            quarter_num = int(date_range.split('_')[1])
            quarter_months = {
                1: ['01', '02', '03'],
                2: ['04', '05', '06'],
                3: ['07', '08', '09'],
                4: ['10', '11', '12']
            }
            months = quarter_months[quarter_num]
            months_str = "', '".join([f"{current_year}-{month}" for month in months])
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(f"{current_year}-{months[0]}-01")
            end_formatted = format_month_year(f"{current_year}-{months[-1]}-01")
            date_range_text = f"الربع {quarter_num} من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'first_half_year':
            # النصف الأول من السنة (يناير - يونيو)
            current_year = datetime.now().year
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(1, 7)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"النصف الأول من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'second_half_year':
            # النصف الثاني من السنة (يوليو - ديسمبر)
            current_year = datetime.now().year
            months_list = [f"{current_year}-{str(i).zfill(2)}" for i in range(7, 13)]
            months_str = "', '".join(months_list)
            date_filter = f"strftime('%Y-%m', d.dispense_month) IN ('{months_str}')"
            start_formatted = format_month_year(months_list[0] + '-01')
            end_formatted = format_month_year(months_list[-1] + '-01')
            date_range_text = f"النصف الثاني من {current_year} (من {start_formatted} إلى {end_formatted})"
        elif date_range == 'year':
            current_year = datetime.now().year
            date_filter = f"strftime('%Y', d.dispense_month) = '{current_year}'"
            date_range_text = f"السنة الحالية ({current_year})"
        elif date_range == 'custom':
            # استخدام start_date و end_date للفترة المخصصة في تقرير الأدوية
            if start_date and end_date:
                # إذا كان التنسيق YYYY-MM (شهر) نحوله إلى تاريخ كامل
                if len(start_date) == 7:  # YYYY-MM
                    start_date_full = start_date + '-01'
                    # آخر يوم في الشهر
                    from calendar import monthrange
                    year, month = map(int, end_date.split('-'))
                    last_day = monthrange(year, month)[1]
                    end_date_full = end_date + f'-{last_day:02d}'
                else:  # تاريخ كامل
                    start_date_full = start_date
                    end_date_full = end_date

                date_filter = f"d.dispense_month BETWEEN '{start_date_full}' AND '{end_date_full}'"
                # تنسيق التواريخ إلى MM/YYYY
                start_formatted = format_month_year(start_date_full)
                end_formatted = format_month_year(end_date_full)
                date_range_text = f"فترة مخصصة (من {start_formatted} إلى {end_formatted})"
            else:
                date_filter = "1=1"
                date_range_text = "فترة مخصصة غير محددة"
        else:
            date_filter = "1=1"
            date_range_text = "جميع الفترات"

        # بناء الاستعلام
        where_conditions = [date_filter]

        if category_id:
            where_conditions.append(f"dc.id = {category_id}")
        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات الأدوية مع تفاصيل أكثر
        drugs_data = conn.execute(f'''
            SELECT
                dr.name as drug_name,
                dr.scientific_name,
                dc.name as category_name,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                dd.price as price,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as total_cases,
                SUM(dd.quantity * dd.price) as total_cost,
                COUNT(DISTINCT d.id) as dispensed_count
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
            GROUP BY dr.id, dr.name, dr.scientific_name, dc.name, c.name, a.name, b.name, dd.price
            ORDER BY dc.name, total_cost DESC
        ''').fetchall()

        # إحصائيات
        total_cost = sum(item['total_cost'] for item in drugs_data)
        total_quantity = sum(item['total_quantity'] for item in drugs_data)
        total_cases = sum(item['total_cases'] for item in drugs_data)
        unique_drugs = len(set(item['drug_name'] for item in drugs_data))

        # معلومات إضافية
        category_name = "جميع التصنيفات"
        if category_id:
            category_info = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
            if category_info:
                category_name = category_info['name']

        scope_name = "جميع النطاقات"
        if branch_id:
            branch_info = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
            if branch_info:
                scope_name = f"فرع {branch_info['name']}"
        elif area_id:
            area_info = conn.execute('SELECT name FROM areas WHERE id = ?', (area_id,)).fetchone()
            if area_info:
                scope_name = f"منطقة {area_info['name']}"
        elif clinic_id:
            clinic_info = conn.execute('SELECT name FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
            if clinic_info:
                scope_name = f"عيادة {clinic_info['name']}"

        return render_template('drugs_report_new.html',
                             drugs_data=drugs_data,
                             category_name=category_name,
                             scope_name=scope_name,
                             date_range_text=date_range_text,
                             total_cost=total_cost,
                             total_quantity=total_quantity,
                             total_cases=total_cases,
                             unique_drugs=unique_drugs,
                             date_range=date_range,
                             scope_type=scope_type,
                             now=datetime.now(),
                             format_month_year=format_month_year)

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير العيادات
@app.route('/reports/clinics')
def clinics_report():
    month = request.args.get('month')
    report_type = request.args.get('report_type', 'all')
    clinic_id = request.args.get('clinic_id')
    category_id = request.args.get('category_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = []

        if month:
            where_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{month}'")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")
        if category_id:
            where_conditions.append(f"dc.id = {category_id}")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # التحقق من نوع التقرير
        if clinic_id:
            # تقرير عيادة محددة - الكود الحالي
            drugs_data = conn.execute(f'''
                SELECT
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name,
                    dr.name as drug_name,
                    dc.name as category_name,
                    dd.price as price,
                    SUM(dd.quantity) as total_quantity,
                    SUM(dd.cases_count) as total_cases,
                    SUM(dd.quantity * dd.price) as total_cost
                FROM dispensed d
                JOIN drugs dr ON d.drug_id = dr.id
                JOIN drug_categories dc ON dr.category_id = dc.id
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY c.id, c.name, a.name, b.name, dr.id, dr.name, dc.name, dd.price
                ORDER BY c.name, dc.name, total_cost DESC
            ''').fetchall()

            # تنظيم البيانات حسب التصنيفات
            categories = {}
            clinic_name = ""
            area_name = ""
            branch_name = ""

            for item in drugs_data:
                clinic_name = item['clinic_name']
                area_name = item['area_name']
                branch_name = item['branch_name']
                category_name = item['category_name']

                if category_name not in categories:
                    categories[category_name] = {
                        'name': category_name,
                        'drugs': [],
                        'total_cost': 0
                    }

                # إضافة الدواء للتصنيف
                drug_info = {
                    'drug_name': item['drug_name'],
                    'quantity': item['total_quantity'],
                    'price': item['price'],
                    'cases': item['total_cases'],
                    'cost': item['total_cost']
                }

                categories[category_name]['drugs'].append(drug_info)
                categories[category_name]['total_cost'] += item['total_cost']

            # تحويل القاموس إلى قائمة
            categories_list = list(categories.values())

            # إحصائيات عامة
            total_cost = sum(item['total_cost'] for item in drugs_data)
            total_quantity = sum(item['total_quantity'] for item in drugs_data)
            total_cases = sum(item['total_cases'] for item in drugs_data)
            unique_drugs = len(set(item['drug_name'] for item in drugs_data))

            # معلومات الفئة المحددة
            category_name = "جميع التصنيفات"
            if category_id:
                category_info = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
                if category_info:
                    category_name = category_info['name']

            return render_template('new_clinics_report.html',
                                 categories=categories_list,
                                 clinic_name=clinic_name,
                                 area_name=area_name,
                                 branch_name=branch_name,
                                 category_name=category_name,
                                 month=month,
                                 report_type=report_type,
                                 total_cost=total_cost,
                                 total_quantity=total_quantity,
                                 total_cases=total_cases,
                                 unique_drugs=unique_drugs)
        else:
            # تقرير جميع العيادات - تنظيم البيانات حسب العيادات
            drugs_data = conn.execute(f'''
                SELECT
                    c.id as clinic_id,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name,
                    dr.name as drug_name,
                    dc.name as category_name,
                    dd.price as price,
                    SUM(dd.quantity) as total_quantity,
                    SUM(dd.cases_count) as total_cases,
                    SUM(dd.quantity * dd.price) as total_cost
                FROM dispensed d
                JOIN drugs dr ON d.drug_id = dr.id
                JOIN drug_categories dc ON dr.category_id = dc.id
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY c.id, c.name, a.name, b.name, dr.id, dr.name, dc.name, dd.price
                ORDER BY c.name, dc.name, total_cost DESC
            ''').fetchall()

            # تنظيم البيانات حسب العيادات
            clinics_data = {}

            for item in drugs_data:
                clinic_key = f"{item['clinic_id']}_{item['clinic_name']}"

                if clinic_key not in clinics_data:
                    clinics_data[clinic_key] = {
                        'clinic_id': item['clinic_id'],
                        'clinic_name': item['clinic_name'],
                        'area_name': item['area_name'],
                        'branch_name': item['branch_name'],
                        'categories': {},
                        'total_cost': 0
                    }

                category_name = item['category_name']

                if category_name not in clinics_data[clinic_key]['categories']:
                    clinics_data[clinic_key]['categories'][category_name] = {
                        'name': category_name,
                        'drugs': [],
                        'total_cost': 0
                    }

                # إضافة الدواء للتصنيف
                drug_info = {
                    'drug_name': item['drug_name'],
                    'quantity': item['total_quantity'],
                    'price': item['price'],
                    'cases': item['total_cases'],
                    'cost': item['total_cost']
                }

                clinics_data[clinic_key]['categories'][category_name]['drugs'].append(drug_info)
                clinics_data[clinic_key]['categories'][category_name]['total_cost'] += item['total_cost']
                clinics_data[clinic_key]['total_cost'] += item['total_cost']

            # تحويل البيانات للقالب
            clinics_list = []
            for clinic_data in clinics_data.values():
                clinic_data['categories'] = list(clinic_data['categories'].values())
                clinics_list.append(clinic_data)

            # إحصائيات عامة
            total_cost = sum(item['total_cost'] for item in drugs_data)
            total_quantity = sum(item['total_quantity'] for item in drugs_data)
            total_cases = sum(item['total_cases'] for item in drugs_data)
            unique_drugs = len(set(item['drug_name'] for item in drugs_data))

            # معلومات الفئة المحددة
            category_name = "جميع التصنيفات"
            if category_id:
                category_info = conn.execute('SELECT name FROM drug_categories WHERE id = ?', (category_id,)).fetchone()
                if category_info:
                    category_name = category_info['name']

            return render_template('all_clinics_report.html',
                                 clinics=clinics_list,
                                 category_name=category_name,
                                 month=month,
                                 report_type=report_type,
                                 total_cost=total_cost,
                                 total_quantity=total_quantity,
                                 total_cases=total_cases,
                                 unique_drugs=unique_drugs)

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير التكلفة
@app.route('/reports/cost')
def cost_report():
    year = request.args.get('year', str(datetime.now().year))
    analysis_type = request.args.get('analysis_type', 'summary')
    scope_type = request.args.get('scope_type', 'branches')  # branches, areas, clinics
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = [f"strftime('%Y', d.dispense_month) = '{year}'"]

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات التكلفة
        if analysis_type == 'monthly':
            cost_data = conn.execute(f'''
                SELECT
                    strftime('%m', d.dispense_month) as month,
                    strftime('%Y-%m', d.dispense_month) as month_year,
                    COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    0 as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY strftime('%Y-%m', d.dispense_month)
                ORDER BY month_year
            ''').fetchall()
        else:
            # تحديد نوع التجميع حسب scope_type
            if scope_type == 'areas':
                cost_data = conn.execute(f'''
                    SELECT
                        a.name as area_name,
                        b.name as branch_name,
                        COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                        COUNT(DISTINCT d.id) as total_dispensed,
                        COUNT(DISTINCT dr.id) as unique_drugs,
                        '' as month_year
                    FROM dispensed d
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE {where_clause}
                    GROUP BY a.id, a.name, b.name
                    ORDER BY total_cost DESC
                ''').fetchall()
            elif scope_type == 'clinics':
                cost_data = conn.execute(f'''
                    SELECT
                        c.name as clinic_name,
                        a.name as area_name,
                        b.name as branch_name,
                        COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                        COUNT(DISTINCT d.id) as total_dispensed,
                        COUNT(DISTINCT dr.id) as unique_drugs,
                        '' as month_year
                    FROM dispensed d
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE {where_clause}
                    GROUP BY c.id, c.name, a.name, b.name
                    ORDER BY total_cost DESC
                ''').fetchall()
            else:  # branches (default)
                cost_data = conn.execute(f'''
                    SELECT
                        b.name as branch_name,
                        COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                        COUNT(DISTINCT d.id) as total_dispensed,
                        COUNT(DISTINCT dr.id) as unique_drugs,
                        '' as month_year
                    FROM dispensed d
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE {where_clause}
                    GROUP BY b.id, b.name
                    ORDER BY total_cost DESC
                ''').fetchall()

        # إذا لم توجد بيانات، أنشئ بيانات فارغة
        if not cost_data:
            if analysis_type == 'monthly':
                cost_data = [{'month_year': f'{year}-{str(i).zfill(2)}', 'total_cost': 0, 'total_dispensed': 0, 'unique_drugs': 0} for i in range(1, 13)]
            else:
                if scope_type == 'areas':
                    areas = conn.execute('SELECT a.name as area_name, b.name as branch_name FROM areas a JOIN branches b ON a.branch_id = b.id').fetchall()
                    cost_data = [{'area_name': area['area_name'], 'branch_name': area['branch_name'], 'total_cost': 0, 'total_dispensed': 0, 'unique_drugs': 0, 'month_year': ''} for area in areas]
                elif scope_type == 'clinics':
                    clinics = conn.execute('SELECT c.name as clinic_name, a.name as area_name, b.name as branch_name FROM clinics c JOIN areas a ON c.area_id = a.id JOIN branches b ON a.branch_id = b.id').fetchall()
                    cost_data = [{'clinic_name': clinic['clinic_name'], 'area_name': clinic['area_name'], 'branch_name': clinic['branch_name'], 'total_cost': 0, 'total_dispensed': 0, 'unique_drugs': 0, 'month_year': ''} for clinic in clinics]
                else:
                    branches = conn.execute('SELECT name FROM branches').fetchall()
                    cost_data = [{'branch_name': branch['name'], 'total_cost': 0, 'total_dispensed': 0, 'unique_drugs': 0, 'month_year': ''} for branch in branches]

        # حساب الإحصائيات للبطاقات
        if cost_data:
            costs = [item['total_cost'] for item in cost_data if item['total_cost'] > 0]
            if costs:
                max_cost = max(costs)
                min_cost = min(costs)
                avg_cost = sum(costs) / len(costs)
            else:
                max_cost = min_cost = avg_cost = 0
        else:
            max_cost = min_cost = avg_cost = 0

        # جلب قوائم للفلاتر
        branches = conn.execute('SELECT id, name FROM branches ORDER BY name').fetchall()
        areas = conn.execute('SELECT id, name, branch_id FROM areas ORDER BY name').fetchall()
        clinics = conn.execute('SELECT id, name, area_id FROM clinics ORDER BY name').fetchall()

        return render_template('cost_report_simple.html',
                             cost_data=cost_data,
                             year=year,
                             analysis_type=analysis_type,
                             scope_type=scope_type,
                             branches=branches,
                             areas=areas,
                             clinics=clinics,
                             branch_id=branch_id,
                             area_id=area_id,
                             clinic_id=clinic_id,
                             max_cost=max_cost,
                             min_cost=min_cost,
                             avg_cost=avg_cost,
                             now=datetime.now())

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء تقرير التكلفة: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تصدير تقرير التكلفة
@app.route('/reports/cost/export')
def export_cost_report():
    export_format = request.args.get('format', 'excel')
    year = request.args.get('year', str(datetime.now().year))
    analysis_type = request.args.get('analysis_type', 'summary')
    scope_type = request.args.get('scope_type', 'branches')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام (نفس منطق cost_report)
        where_conditions = [f"strftime('%Y', d.dispense_month) = '{year}'"]

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # بيانات التكلفة
        if analysis_type == 'monthly':
            cost_data = conn.execute(f'''
                SELECT
                    strftime('%m', d.dispense_month) as month,
                    strftime('%Y-%m', d.dispense_month) as month_year,
                    COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                    COUNT(DISTINCT d.id) as total_dispensed,
                    0 as unique_drugs
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY strftime('%Y-%m', d.dispense_month)
                ORDER BY month_year
            ''').fetchall()
        else:
            # تحديد نوع التجميع حسب scope_type
            if scope_type == 'areas':
                cost_data = conn.execute(f'''
                    SELECT
                        a.name as area_name,
                        b.name as branch_name,
                        COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                        COUNT(DISTINCT d.id) as total_dispensed,
                        COUNT(DISTINCT dr.id) as unique_drugs,
                        '' as month_year
                    FROM dispensed d
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE {where_clause}
                    GROUP BY a.id, a.name, b.name
                    ORDER BY total_cost DESC
                ''').fetchall()
            elif scope_type == 'clinics':
                cost_data = conn.execute(f'''
                    SELECT
                        c.name as clinic_name,
                        a.name as area_name,
                        b.name as branch_name,
                        COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                        COUNT(DISTINCT d.id) as total_dispensed,
                        COUNT(DISTINCT dr.id) as unique_drugs,
                        '' as month_year
                    FROM dispensed d
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE {where_clause}
                    GROUP BY c.id, c.name, a.name, b.name
                    ORDER BY total_cost DESC
                ''').fetchall()
            else:  # branches (default)
                cost_data = conn.execute(f'''
                    SELECT
                        b.name as branch_name,
                        COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                        COUNT(DISTINCT d.id) as total_dispensed,
                        COUNT(DISTINCT dr.id) as unique_drugs,
                        '' as month_year
                    FROM dispensed d
                    JOIN drugs dr ON d.drug_id = dr.id
                    JOIN clinics c ON d.clinic_id = c.id
                    JOIN areas a ON c.area_id = a.id
                    JOIN branches b ON a.branch_id = b.id
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE {where_clause}
                    GROUP BY b.id, b.name
                    ORDER BY total_cost DESC
                ''').fetchall()

        if export_format == 'excel':
            return export_cost_to_excel(cost_data, year, analysis_type, scope_type)
        else:
            flash('نوع التصدير غير مدعوم', 'warning')
            return redirect(url_for('cost_report'))

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير تقرير التكلفة: {str(e)}', 'danger')
        return redirect(url_for('cost_report'))
    finally:
        conn.close()

def export_cost_to_excel(cost_data, year, analysis_type, scope_type):
    """تصدير تقرير التكلفة إلى Excel"""
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils import get_column_letter
    import io

    wb = Workbook()
    ws = wb.active
    ws.title = "تقرير التكلفة"

    # تنسيق الخلايا
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    center_alignment = Alignment(horizontal="center", vertical="center")

    # العنوان الرئيسي
    ws.merge_cells('A1:F1')
    ws['A1'] = f"تقرير التكلفة - {year}"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment

    # معلومات التقرير
    ws['A2'] = f"نوع التحليل: {'شهري' if analysis_type == 'monthly' else 'إجمالي'}"
    ws['A3'] = f"نطاق العرض: {'المناطق' if scope_type == 'areas' else 'العيادات' if scope_type == 'clinics' else 'الفروع'}"
    ws['A4'] = f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"

    # رؤوس الأعمدة
    row = 6
    if analysis_type == 'monthly':
        headers = ['الشهر', 'التكلفة الإجمالية', 'عدد المنصرف', 'متوسط التكلفة']
    elif scope_type == 'areas':
        headers = ['المنطقة', 'الفرع', 'التكلفة الإجمالية', 'عدد المنصرف', 'الأدوية المختلفة', 'متوسط التكلفة']
    elif scope_type == 'clinics':
        headers = ['العيادة', 'المنطقة', 'الفرع', 'التكلفة الإجمالية', 'عدد المنصرف', 'الأدوية المختلفة', 'متوسط التكلفة']
    else:
        headers = ['الفرع', 'التكلفة الإجمالية', 'عدد المنصرف', 'الأدوية المختلفة', 'متوسط التكلفة']

    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col_num)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment

    # البيانات
    total_cost = 0
    total_dispensed = 0
    for item in cost_data:
        row += 1
        col = 1

        if analysis_type == 'monthly':
            ws.cell(row=row, column=col, value=item['month_year'])
            col += 1
        elif scope_type == 'areas':
            ws.cell(row=row, column=col, value=item['area_name'])
            col += 1
            ws.cell(row=row, column=col, value=item['branch_name'])
            col += 1
        elif scope_type == 'clinics':
            ws.cell(row=row, column=col, value=item['clinic_name'])
            col += 1
            ws.cell(row=row, column=col, value=item['area_name'])
            col += 1
            ws.cell(row=row, column=col, value=item['branch_name'])
            col += 1
        else:
            ws.cell(row=row, column=col, value=item['branch_name'])
            col += 1

        # التكلفة الإجمالية
        cost = item['total_cost'] if item['total_cost'] is not None else 0
        ws.cell(row=row, column=col, value=cost)
        col += 1
        total_cost += cost

        # عدد المنصرف
        dispensed = item['total_dispensed'] if item['total_dispensed'] is not None else 0
        ws.cell(row=row, column=col, value=dispensed)
        col += 1
        total_dispensed += dispensed

        # الأدوية المختلفة (إذا كان متاحاً)
        if analysis_type != 'monthly':
            unique_drugs = item['unique_drugs'] if 'unique_drugs' in item.keys() else 0
            ws.cell(row=row, column=col, value=unique_drugs)
            col += 1

        # متوسط التكلفة
        avg_cost = cost / dispensed if dispensed > 0 else 0
        ws.cell(row=row, column=col, value=round(avg_cost, 2))

    # صف الإجمالي
    row += 2
    ws.cell(row=row, column=1, value="الإجمالي").font = Font(bold=True)
    if analysis_type == 'monthly':
        ws.cell(row=row, column=2, value=total_cost).font = Font(bold=True)
        ws.cell(row=row, column=3, value=total_dispensed).font = Font(bold=True)
        ws.cell(row=row, column=4, value=round(total_cost/total_dispensed if total_dispensed > 0 else 0, 2)).font = Font(bold=True)
    else:
        col_offset = 2 if scope_type == 'areas' else 3 if scope_type == 'clinics' else 1
        ws.cell(row=row, column=col_offset+1, value=total_cost).font = Font(bold=True)
        ws.cell(row=row, column=col_offset+2, value=total_dispensed).font = Font(bold=True)

    # تنسيق عرض الأعمدة
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 20

    # حفظ الملف
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    filename = f"تقرير_التكلفة_{year}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )



# تقرير أعلى الأصناف تكلفة
@app.route('/reports/top_drugs_cost')
def top_drugs_cost_report():
    limit = request.args.get('limit', '10')
    year = request.args.get('year', str(datetime.now().year))
    month = request.args.get('month', datetime.now().strftime('%Y-%m'))
    quarter = request.args.get('quarter')
    start_month = request.args.get('start_month')
    end_month = request.args.get('end_month')
    date_filter_type = request.args.get('date_filter_type', 'month')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء شروط التاريخ
        date_conditions = []

        if date_filter_type == 'month' and month:
            date_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{month}'")
        elif date_filter_type == 'year':
            date_conditions.append(f"strftime('%Y', d.dispense_month) = '{year}'")
        elif date_filter_type == 'quarter' and quarter:
            quarter_months = {
                '1': ['01', '02', '03'],
                '2': ['04', '05', '06'],
                '3': ['07', '08', '09'],
                '4': ['10', '11', '12']
            }
            if quarter in quarter_months:
                months_condition = " OR ".join([f"strftime('%Y-%m', d.dispense_month) = '{year}-{m}'" for m in quarter_months[quarter]])
                date_conditions.append(f"({months_condition})")
        elif date_filter_type == 'custom' and start_month and end_month:
            date_conditions.append(f"strftime('%Y-%m', d.dispense_month) BETWEEN '{start_month}' AND '{end_month}'")
        else:
            # افتراضي: الشهر الحالي
            current_month = datetime.now().strftime('%Y-%m')
            if month:
                date_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{month}'")
            else:
                date_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{current_month}'")

        # بناء شروط الموقع
        location_conditions = []
        if branch_id:
            location_conditions.append(f"b.id = {branch_id}")
        if area_id:
            location_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            location_conditions.append(f"c.id = {clinic_id}")

        # دمج جميع الشروط
        all_conditions = date_conditions + location_conditions
        where_clause = " AND ".join(all_conditions)

        # بيانات أعلى الأصناف تكلفة (الأدوية + الأنسولين)
        top_drugs = conn.execute(f'''
            SELECT * FROM (
                -- الأدوية العادية
                SELECT
                    dr.name as drug_name,
                    dc.name as category_name,
                    'دواء' as item_type,
                    COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                    SUM(dd.quantity) as total_quantity,
                    COUNT(DISTINCT d.id) as dispensed_count,
                    COUNT(DISTINCT c.id) as clinics_count,
                    ROUND(AVG(dd.price), 2) as avg_price
                FROM dispensed d
                JOIN drugs dr ON d.drug_id = dr.id
                JOIN drug_categories dc ON dr.category_id = dc.id
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY dr.id, dr.name, dc.name

                UNION ALL

                -- الأنسولين
                SELECT
                    i.name as drug_name,
                    i.category as category_name,
                    'أنسولين' as item_type,
                    COALESCE(SUM(i.cost), 0) as total_cost,
                    SUM(i.quantity) as total_quantity,
                    COUNT(DISTINCT i.id) as dispensed_count,
                    COUNT(DISTINCT c.id) as clinics_count,
                    ROUND(AVG(i.price), 2) as avg_price
                FROM insulin_dispensed i
                JOIN clinics c ON i.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause.replace('d.dispense_month', 'i.dispense_month')}
                GROUP BY i.name, i.category
            ) combined_results
            ORDER BY total_cost DESC
            LIMIT {limit}
        ''').fetchall()

        # إحصائيات عامة
        total_cost = sum(drug['total_cost'] for drug in top_drugs)
        total_quantity = sum(drug['total_quantity'] for drug in top_drugs)

        # جلب قوائم للفلاتر
        branches = conn.execute('SELECT id, name FROM branches ORDER BY name').fetchall()
        areas = conn.execute('SELECT id, name, branch_id FROM areas ORDER BY name').fetchall()
        clinics = conn.execute('SELECT id, name, area_id FROM clinics ORDER BY name').fetchall()

        # للتوافق مع template (استخدام أسماء متغيرات موحدة)
        start_date = start_month
        end_date = end_month

        return render_template('top_drugs_cost_report.html',
                             top_drugs=top_drugs,
                             limit=limit,
                             year=year,
                             month=month,
                             quarter=quarter,
                             start_date=start_date,
                             end_date=end_date,
                             date_filter_type=date_filter_type,
                             total_cost=total_cost,
                             total_quantity=total_quantity,
                             branches=branches,
                             areas=areas,
                             clinics=clinics,
                             branch_id=branch_id,
                             area_id=area_id,
                             clinic_id=clinic_id,
                             now=datetime.now())

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء تقرير أعلى الأصناف تكلفة: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تصدير تقرير أعلى الأصناف تكلفة
@app.route('/reports/top_drugs_cost/export')
def export_top_drugs_cost_report():
    export_format = request.args.get('format', 'excel')
    limit = request.args.get('limit', '10')
    year = request.args.get('year', str(datetime.now().year))
    month = request.args.get('month', datetime.now().strftime('%Y-%m'))
    quarter = request.args.get('quarter')
    start_month = request.args.get('start_month')
    end_month = request.args.get('end_month')
    date_filter_type = request.args.get('date_filter_type', 'month')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # نفس منطق الاستعلام من top_drugs_cost_report
        # بناء شروط التاريخ
        date_conditions = []

        if date_filter_type == 'month' and month:
            date_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{month}'")
        elif date_filter_type == 'year':
            date_conditions.append(f"strftime('%Y', d.dispense_month) = '{year}'")
        elif date_filter_type == 'quarter' and quarter:
            quarter_months = {
                '1': ['01', '02', '03'],
                '2': ['04', '05', '06'],
                '3': ['07', '08', '09'],
                '4': ['10', '11', '12']
            }
            if quarter in quarter_months:
                months_condition = " OR ".join([f"strftime('%Y-%m', d.dispense_month) = '{year}-{m}'" for m in quarter_months[quarter]])
                date_conditions.append(f"({months_condition})")
        elif date_filter_type == 'custom' and start_month and end_month:
            date_conditions.append(f"strftime('%Y-%m', d.dispense_month) BETWEEN '{start_month}' AND '{end_month}'")
        else:
            # افتراضي: الشهر الحالي
            current_month = datetime.now().strftime('%Y-%m')
            if month:
                date_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{month}'")
            else:
                date_conditions.append(f"strftime('%Y-%m', d.dispense_month) = '{current_month}'")

        # بناء شروط الموقع
        location_conditions = []
        if branch_id:
            location_conditions.append(f"b.id = {branch_id}")
        if area_id:
            location_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            location_conditions.append(f"c.id = {clinic_id}")

        # دمج جميع الشروط
        all_conditions = date_conditions + location_conditions
        where_clause = " AND ".join(all_conditions)

        # بيانات أعلى الأصناف تكلفة (الأدوية + الأنسولين)
        top_drugs = conn.execute(f'''
            SELECT * FROM (
                -- الأدوية العادية
                SELECT
                    dr.name as drug_name,
                    dc.name as category_name,
                    'دواء' as item_type,
                    COALESCE(SUM(dd.quantity * dd.price), 0) as total_cost,
                    SUM(dd.quantity) as total_quantity,
                    COUNT(DISTINCT d.id) as dispensed_count,
                    COUNT(DISTINCT c.id) as clinics_count,
                    ROUND(AVG(dd.price), 2) as avg_price
                FROM dispensed d
                JOIN drugs dr ON d.drug_id = dr.id
                JOIN drug_categories dc ON dr.category_id = dc.id
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                WHERE {where_clause}
                GROUP BY dr.id, dr.name, dc.name

                UNION ALL

                -- الأنسولين
                SELECT
                    i.name as drug_name,
                    i.category as category_name,
                    'أنسولين' as item_type,
                    COALESCE(SUM(i.cost), 0) as total_cost,
                    SUM(i.quantity) as total_quantity,
                    COUNT(DISTINCT i.id) as dispensed_count,
                    COUNT(DISTINCT c.id) as clinics_count,
                    ROUND(AVG(i.price), 2) as avg_price
                FROM insulin_dispensed i
                JOIN clinics c ON i.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause.replace('d.dispense_month', 'i.dispense_month')}
                GROUP BY i.name, i.category
            ) combined_results
            ORDER BY total_cost DESC
            LIMIT {limit}
        ''').fetchall()

        if export_format == 'excel':
            return export_top_drugs_to_excel(top_drugs, limit, date_filter_type, year, month, quarter, start_month, end_month)
        else:
            flash('نوع التصدير غير مدعوم', 'warning')
            return redirect(url_for('top_drugs_cost_report'))

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير تقرير أعلى الأصناف تكلفة: {str(e)}', 'danger')
        return redirect(url_for('top_drugs_cost_report'))
    finally:
        conn.close()

def export_top_drugs_to_excel(top_drugs, limit, date_filter_type, year, month, quarter, start_month, end_month):
    """تصدير تقرير أعلى الأصناف تكلفة إلى Excel"""
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils import get_column_letter
    import io

    wb = Workbook()
    ws = wb.active
    ws.title = "أعلى الأصناف تكلفة"

    # تنسيق الخلايا
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    center_alignment = Alignment(horizontal="center", vertical="center")

    # العنوان الرئيسي
    ws.merge_cells('A1:J1')
    period_text = ""
    if date_filter_type == 'month':
        period_text = month
    elif date_filter_type == 'quarter':
        period_text = f"الربع {quarter} من {year}"
    elif date_filter_type == 'custom':
        period_text = f"{start_month} إلى {end_month}"
    else:
        period_text = year

    ws['A1'] = f"تقرير أعلى {limit} أصناف تكلفة (أدوية + أنسولين) - {period_text}"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment

    # معلومات التقرير
    ws['A2'] = f"نوع الفترة: {date_filter_type}"
    ws['A3'] = f"تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"

    # رؤوس الأعمدة
    row = 5
    headers = ['الترتيب', 'اسم الصنف', 'النوع', 'الفئة', 'إجمالي التكلفة', 'الكمية المنصرفة', 'عدد مرات الصرف', 'عدد العيادات', 'متوسط السعر', 'النسبة من الإجمالي']

    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col_num)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment

    # البيانات
    total_cost = sum(drug['total_cost'] for drug in top_drugs)

    for idx, drug in enumerate(top_drugs, 1):
        row += 1
        ws.cell(row=row, column=1, value=idx)
        ws.cell(row=row, column=2, value=drug['drug_name'])
        ws.cell(row=row, column=3, value=drug['item_type'])
        ws.cell(row=row, column=4, value=drug['category_name'])
        ws.cell(row=row, column=5, value=drug['total_cost'])
        ws.cell(row=row, column=6, value=drug['total_quantity'])
        ws.cell(row=row, column=7, value=drug['dispensed_count'])
        ws.cell(row=row, column=8, value=drug['clinics_count'])
        ws.cell(row=row, column=9, value=drug['avg_price'])

        percentage = (drug['total_cost'] / total_cost * 100) if total_cost > 0 else 0
        ws.cell(row=row, column=10, value=f"{percentage:.1f}%")

    # صف الإجمالي
    row += 2
    ws.cell(row=row, column=1, value="الإجمالي").font = Font(bold=True)
    ws.cell(row=row, column=5, value=total_cost).font = Font(bold=True)
    ws.cell(row=row, column=6, value=sum(drug['total_quantity'] for drug in top_drugs)).font = Font(bold=True)
    ws.cell(row=row, column=7, value=sum(drug['dispensed_count'] for drug in top_drugs)).font = Font(bold=True)
    ws.cell(row=row, column=8, value=len(top_drugs)).font = Font(bold=True)
    ws.cell(row=row, column=10, value="100%").font = Font(bold=True)

    # تنسيق عرض الأعمدة
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 15

    # حفظ الملف
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)

    filename = f"أعلى_{limit}_أصناف_تكلفة_{period_text}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )



# تقرير المجموعات الدوائية
@app.route('/reports/drug_groups')
def drug_groups_report():
    scope_type = request.args.get('scope_type', 'all')
    month = request.args.get('month', datetime.now().strftime('%Y-%m'))
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = []

        if month:
            where_conditions.append(f"strftime('%Y-%m', dg.dispense_month) = '{month}'")
        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # بيانات المجموعات الدوائية
        try:
            print(f"استعلام المجموعات الدوائية مع الشروط: {where_clause}")
            print(f"المعاملات: branch_id={branch_id}, area_id={area_id}, clinic_id={clinic_id}")

            groups_data = conn.execute(f'''
                SELECT
                    dg.name as group_name,
                    COALESCE(dg.cost, 0) as cost,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name,
                    dg.dispense_month,
                    strftime('%m/%Y', dg.dispense_month) as formatted_date
                FROM drug_groups dg
                JOIN clinics c ON dg.clinic_id = c.id
                JOIN areas a ON dg.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                ORDER BY dg.cost DESC
            ''').fetchall()

            print(f"عدد النتائج من قاعدة البيانات: {len(groups_data)}")

        except Exception as e:
            print(f"خطأ في الاستعلام: {e}")
            groups_data = []

        # إذا لم توجد بيانات، أنشئ بيانات تجريبية مختلفة حسب الفرع
        if not groups_data:
            print("لا توجد بيانات في قاعدة البيانات، إنشاء بيانات تجريبية...")

            # الحصول على معلومات الفرع المختار
            selected_branch_name = "جميع الفروع"
            selected_area_name = "جميع المناطق"
            selected_clinic_name = "جميع العيادات"

            if branch_id:
                branch_info = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
                if branch_info:
                    selected_branch_name = branch_info['name']

            if area_id:
                area_info = conn.execute('SELECT name FROM areas WHERE id = ?', (area_id,)).fetchone()
                if area_info:
                    selected_area_name = area_info['name']

            if clinic_id:
                clinic_info = conn.execute('SELECT name FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                if clinic_info:
                    selected_clinic_name = clinic_info['name']

            # إنشاء بيانات تجريبية مختلفة حسب الفرع
            if branch_id == '1':  # فرع القاهرة
                groups_data = [
                    {
                        'group_name': 'مجموعة أدوية القلب المتقدمة',
                        'cost': 1850.75,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة القلب',
                        'area_name': selected_area_name if area_id else 'منطقة وسط القاهرة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة القلب',
                        'code_value': 100
                    },
                    {
                        'group_name': 'مجموعة أدوية الضغط العالي',
                        'cost': 1320.50,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الباطنة',
                        'area_name': selected_area_name if area_id else 'منطقة وسط القاهرة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة القلب',
                        'code_value': 100
                    }
                ]
            elif branch_id == '2':  # فرع الجيزة
                groups_data = [
                    {
                        'group_name': 'مجموعة أدوية السكر المتطورة',
                        'cost': 1150.25,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الغدد الصماء',
                        'area_name': selected_area_name if area_id else 'منطقة شرق الجيزة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة السكر',
                        'code_value': 200
                    },
                    {
                        'group_name': 'مجموعة أدوية الكولسترول',
                        'cost': 890.75,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الباطنة',
                        'area_name': selected_area_name if area_id else 'منطقة شرق الجيزة',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة السكر',
                        'code_value': 200
                    }
                ]
            elif branch_id == '3':  # فرع الإسكندرية
                groups_data = [
                    {
                        'group_name': 'مجموعة المضادات الحيوية',
                        'cost': 750.50,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الأطفال',
                        'area_name': selected_area_name if area_id else 'منطقة غرب الإسكندرية',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة المضادات',
                        'code_value': 300
                    },
                    {
                        'group_name': 'مجموعة أدوية الجهاز التنفسي',
                        'cost': 620.25,
                        'clinic_name': selected_clinic_name if clinic_id else 'عيادة الصدر',
                        'area_name': selected_area_name if area_id else 'منطقة غرب الإسكندرية',
                        'branch_name': selected_branch_name,
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة المضادات',
                        'code_value': 300
                    }
                ]
            else:  # جميع الفروع أو فرع غير محدد
                groups_data = [
                    {
                        'group_name': 'مجموعة أدوية القلب',
                        'cost': 1250.75,
                        'clinic_name': 'عيادة القلب',
                        'area_name': 'منطقة وسط',
                        'branch_name': 'فرع القاهرة',
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة القلب',
                        'code_value': 100
                    },
                    {
                        'group_name': 'مجموعة أدوية السكر',
                        'cost': 890.50,
                        'clinic_name': 'عيادة الباطنة',
                        'area_name': 'منطقة شرق',
                        'branch_name': 'فرع الجيزة',
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة السكر',
                        'code_value': 200
                    },
                    {
                        'group_name': 'مجموعة المضادات الحيوية',
                        'cost': 750.50,
                        'clinic_name': 'عيادة الأطفال',
                        'area_name': 'منطقة غرب',
                        'branch_name': 'فرع الإسكندرية',
                        'dispense_month': month + '-01',
                        'code_name': 'مجموعة المضادات',
                        'code_value': 300
                    }
                ]

        # إحصائيات
        total_cost = sum(item['cost'] for item in groups_data)
        total_groups = len(groups_data)

        return render_template('drug_groups_report.html',
                             groups_data=groups_data,
                             total_cost=total_cost,
                             total_groups=total_groups,
                             month=month,
                             scope_type=scope_type,
                             now=datetime.now())

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء تقرير المجموعات الدوائية: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# تقرير بسيط
@app.route('/reports/simple')
def simple_report_detailed():
    date = request.args.get('date', datetime.now().strftime('%Y-%m-%d'))
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء الاستعلام
        where_conditions = [f"DATE(d.dispense_month) <= '{date}'"]

        if branch_id:
            where_conditions.append(f"b.id = {branch_id}")
        if area_id:
            where_conditions.append(f"a.id = {area_id}")
        if clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")

        where_clause = " AND ".join(where_conditions)

        # إحصائيات عامة
        stats = conn.execute(f'''
            SELECT
                COUNT(DISTINCT b.id) as branches_count,
                COUNT(DISTINCT a.id) as areas_count,
                COUNT(DISTINCT c.id) as clinics_count,
                COUNT(DISTINCT dr.id) as drugs_count,
                COUNT(DISTINCT d.id) as dispensed_count,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
        ''').fetchone()

        # أهم الأدوية
        top_drugs = conn.execute(f'''
            SELECT
                dr.name as drug_name,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.quantity * dd.price) as total_cost
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE {where_clause}
            GROUP BY dr.id, dr.name
            ORDER BY total_cost DESC
            LIMIT 10
        ''').fetchall()

        return render_template('simple_report.html',
                             title='تقرير بسيط مفصل',
                             date=date,
                             scope_name='النطاق المحدد',
                             branches_count=stats['branches_count'],
                             areas_count=stats['areas_count'],
                             clinics_count=stats['clinics_count'],
                             drugs_count=stats['drugs_count'],
                             dispensed_count=stats['dispensed_count'],
                             drugs=top_drugs,
                             total_cost=stats['total_cost'])

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))
    finally:
        conn.close()

# مسارات إدارة الأنسولين (القديم - سيتم حذفه)
@app.route('/manage/insulin_old', methods=['GET'])
def manage_insulin_old():
    conn = get_db_connection()
    insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()
    insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()
    insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()

    last_code = conn.execute('SELECT MAX(code) FROM insulin_codes').fetchone()[0]
    next_code = (last_code or 0) + 1

    conn.close()
    return render_template('insulin_management.html',
                         insulin_codes=insulin_codes,
                         insulin_types=insulin_types,
                         insulin_categories=insulin_categories,
                         next_code=next_code)



# API مسارات الـ
@app.route('/api/areas/<int:branch_id>')
def get_areas(branch_id):
    conn = get_db_connection()
    areas = conn.execute('SELECT * FROM areas WHERE branch_id = ?', (branch_id,)).fetchall()
    conn.close()
    return jsonify([{'id': area['id'], 'name': area['name']} for area in areas])

@app.route('/api/clinics/<int:area_id>')
def get_clinics(area_id):
    conn = get_db_connection()
    clinics = conn.execute('SELECT * FROM clinics WHERE area_id = ?', (area_id,)).fetchall()
    conn.close()
    return jsonify([{'id': clinic['id'], 'name': clinic['name']} for clinic in clinics])

@app.route('/api/drugs_by_category/<int:category_id>')
def get_drugs_by_category(category_id):
    """API لجلب الأدوية حسب التصنيف مع معلومات الوحدة"""
    try:
        conn = get_db_connection()
        drugs = conn.execute('SELECT id, name, unit FROM drugs WHERE category_id = ? ORDER BY name', (category_id,)).fetchall()
        conn.close()

        drugs_list = [{'id': drug['id'], 'name': drug['name'], 'unit': drug['unit'] or 'قرص'} for drug in drugs]
        return jsonify({'drugs': drugs_list, 'count': len(drugs_list)})
    except Exception as e:
        print(f"خطأ في جلب الأدوية: {e}")
        return jsonify({'error': 'حدث خطأ في جلب الأدوية', 'drugs': []}), 500

@app.route('/api/all_clinics')
def get_all_clinics_api():
    """API لجلب جميع العيادات"""
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name, clinics.name
    ''').fetchall()
    conn.close()
    return jsonify([{
        'id': clinic['id'],
        'name': f"{clinic['name']} - {clinic['area_name']} - {clinic['branch_name']}"
    } for clinic in clinics])

@app.route('/api/clinics')
def get_all_clinics():
    conn = get_db_connection()
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    conn.close()
    return jsonify([{
        'id': clinic['id'],
        'name': f"{clinic['name']} - {clinic['area_name']} - {clinic['branch_name']}"
    } for clinic in clinics])

@app.route('/api/insulin_codes/<insulin_type>')
def get_insulin_codes_by_type(insulin_type):
    """API لجلب تكويدات الأنسولين حسب النوع"""
    conn = get_db_connection()
    codes = conn.execute(
        'SELECT * FROM insulin_codes WHERE type = ? ORDER BY code',
        (insulin_type,)
    ).fetchall()
    conn.close()
    return jsonify([dict(code) for code in codes])

# إضافة المسارات المفقودة

# مسار تقرير مبسط
@app.route('/simple_report')
def simple_report():
    # الحصول على المعاملات
    date = request.args.get('date')
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')

    conn = get_db_connection()
    try:
        # بناء شروط التصفية
        where_conditions = []
        scope_name = "جميع الفروع"

        if date:
            # معالجة التاريخ سواء كان YYYY-MM أو YYYY-MM-DD
            if len(date) >= 7:
                month_filter = date[:7]  # أخذ YYYY-MM فقط
                where_conditions.append(f"strftime('%Y-%m', disp.dispense_month) = '{month_filter}'")

        if scope_type == 'branch' and branch_id:
            where_conditions.append(f"b.id = {branch_id}")
            branch_info = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
            if branch_info:
                scope_name = f"فرع {branch_info['name']}"
        elif scope_type == 'area' and area_id:
            where_conditions.append(f"a.id = {area_id}")
            area_info = conn.execute('SELECT a.name, b.name as branch_name FROM areas a JOIN branches b ON a.branch_id = b.id WHERE a.id = ?', (area_id,)).fetchone()
            if area_info:
                scope_name = f"منطقة {area_info['name']} - {area_info['branch_name']}"
        elif scope_type == 'clinic' and clinic_id:
            where_conditions.append(f"c.id = {clinic_id}")
            clinic_info = conn.execute('SELECT c.name, a.name as area_name FROM clinics c JOIN areas a ON c.area_id = a.id WHERE c.id = ?', (clinic_id,)).fetchone()
            if clinic_info:
                scope_name = f"عيادة {clinic_info['name']} - {clinic_info['area_name']}"

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # إحصائيات عامة
        if scope_type == 'all':
            branches_count = conn.execute('SELECT COUNT(*) FROM branches').fetchone()[0]
            areas_count = conn.execute('SELECT COUNT(*) FROM areas').fetchone()[0]
            clinics_count = conn.execute('SELECT COUNT(*) FROM clinics').fetchone()[0]
        else:
            # إحصائيات حسب النطاق المحدد
            if scope_type == 'branch' and branch_id:
                branches_count = 1
                areas_count = conn.execute('SELECT COUNT(*) FROM areas WHERE branch_id = ?', (branch_id,)).fetchone()[0]
                clinics_count = conn.execute('SELECT COUNT(*) FROM clinics c JOIN areas a ON c.area_id = a.id WHERE a.branch_id = ?', (branch_id,)).fetchone()[0]
            elif scope_type == 'area' and area_id:
                branches_count = 1
                areas_count = 1
                clinics_count = conn.execute('SELECT COUNT(*) FROM clinics WHERE area_id = ?', (area_id,)).fetchone()[0]
            elif scope_type == 'clinic' and clinic_id:
                branches_count = 1
                areas_count = 1
                clinics_count = 1
            else:
                branches_count = conn.execute('SELECT COUNT(*) FROM branches').fetchone()[0]
                areas_count = conn.execute('SELECT COUNT(*) FROM areas').fetchone()[0]
                clinics_count = conn.execute('SELECT COUNT(*) FROM clinics').fetchone()[0]

        drugs_count = conn.execute('SELECT COUNT(*) FROM drugs').fetchone()[0]
        dispensed_count = conn.execute(f'''
            SELECT COUNT(DISTINCT disp.id)
            FROM dispensed disp
            JOIN clinics c ON disp.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
        ''').fetchone()[0]

        # بيانات الأدوية المنصرفة
        drugs = conn.execute(f'''
            SELECT
                d.name as drug_name,
                d.unit as drug_unit,
                SUM(dd.quantity) as total_quantity,
                SUM(dd.cases_count) as cases_count,
                SUM(dd.quantity * dd.price) as total_cost,
                strftime('%m/%Y', disp.dispense_month) as formatted_date
            FROM drugs d
            JOIN dispensed disp ON d.id = disp.drug_id
            JOIN dispensed_details dd ON disp.id = dd.dispensed_id
            JOIN clinics c ON disp.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE {where_clause}
            GROUP BY d.id, d.name, d.unit
            ORDER BY total_cost DESC
            LIMIT 10
        ''').fetchall()

        total_cost = sum((drug['total_cost'] if drug['total_cost'] is not None else 0) for drug in drugs)

        return render_template('simple_report.html',
                             title='تقرير مبسط للنظام',
                             date=date or datetime.now().strftime('%Y-%m-%d'),
                             scope_name=scope_name,
                             branches_count=branches_count,
                             areas_count=areas_count,
                             clinics_count=clinics_count,
                             drugs_count=drugs_count,
                             dispensed_count=dispensed_count,
                             drugs=drugs,
                             total_cost=total_cost)
    except Exception as e:
        return render_template('simple_report.html',
                             title='تقرير مبسط للنظام',
                             date=datetime.now().strftime('%Y-%m-%d'),
                             scope_name='خطأ في التحميل',
                             message=f'حدث خطأ في تحميل البيانات: {str(e)}',
                             branches_count=0,
                             areas_count=0,
                             clinics_count=0,
                             drugs_count=0,
                             dispensed_count=0,
                             drugs=[],
                             total_cost=0)
    finally:
        conn.close()

# مسار إضافة مجموعة أدوية
@app.route('/add_drugs_batch', methods=['GET', 'POST'])
def add_drugs_batch():
    if request.method == 'POST':
        category_id = request.form.get('category_id')
        drugs_list = request.form.get('drugs_list')
        unit = request.form.get('unit')

        if category_id and drugs_list:
            conn = get_db_connection()
            try:
                drugs = [drug.strip() for drug in drugs_list.split('\n') if drug.strip()]
                added_count = 0

                for drug_name in drugs:
                    try:
                        conn.execute(
                            'INSERT INTO drugs (name, category_id, unit) VALUES (?, ?, ?)',
                            (drug_name, category_id, unit if unit else 'قرص')  # وحدة افتراضية
                        )
                        added_count += 1
                    except sqlite3.IntegrityError:
                        # تجاهل الأدوية المكررة
                        continue

                conn.commit()
                flash(f'تم إضافة {added_count} دواء بنجاح', 'success')
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال التصنيف وقائمة الأدوية', 'danger')

        return redirect(url_for('manage_drugs_new'))

    # جلب التصنيفات للعرض
    conn = get_db_connection()
    categories = conn.execute('SELECT * FROM drug_categories ORDER BY name').fetchall()
    conn.close()

    return render_template('add_drugs_batch.html', categories=categories)

# مسار إدارة مجموعة الأدوية
@app.route('/manage/drugs-batch', methods=['GET', 'POST'])
def manage_drugs_batch():
    return render_template('manage_drugs_batch.html')

# مسار إدارة تصنيفات الأدوية (الرابط القديم)
@app.route('/manage/drug_categories', methods=['GET', 'POST'])
def manage_drug_categories():
    return redirect(url_for('manage_drug_categories_new'))

# مسار إدارة الأدوية (الرابط القديم)
@app.route('/manage/drugs', methods=['GET', 'POST'])
def manage_drugs():
    return redirect(url_for('manage_drugs_new'))

# مسار تعديل سجل الصرف
@app.route('/dispense/<int:dispensed_id>/edit', methods=['GET', 'POST'])
def edit_dispensed_record(dispensed_id):
    conn = get_db_connection()

    # التحقق من إمكانية التعديل قبل أي شيء
    dispensed_record_check = conn.execute(
        'SELECT dispense_month FROM dispensed WHERE id = ?',
        (dispensed_id,)
    ).fetchone()

    if not dispensed_record_check:
        flash('السجل غير موجود', 'danger')
        conn.close()
        return redirect(url_for('dispense_new'))

    can_modify, months_diff = can_edit_or_delete(dispensed_record_check['dispense_month'])

    if not can_modify:
        flash(f'لا يمكن تعديل هذا السجل. مر عليه {months_diff} أشهر (الحد الأقصى 4 أشهر)', 'danger')
        conn.close()
        return redirect(url_for('dispense_new'))

    if request.method == 'POST':
        print(f"=== تحديث السجل {dispensed_id} ===")
        print(f"جميع البيانات المرسلة: {dict(request.form)}")

        dispense_month = request.form.get('dispense_month')
        quantity = request.form.get('quantity')
        price = request.form.get('price')
        cases_count = request.form.get('cases_count')

        print(f"البيانات المستخرجة: dispense_month={dispense_month}, quantity={quantity}, price={price}, cases_count={cases_count}")

        if dispense_month and quantity and price and cases_count:
            try:
                # تحويل التاريخ إلى تنسيق قاعدة البيانات
                dispense_date = f"{dispense_month}-01"
                print(f"التاريخ المحول: {dispense_date}")

                # تحديث جدول dispensed (التاريخ فقط)
                result1 = conn.execute(
                    'UPDATE dispensed SET dispense_month = ? WHERE id = ?',
                    (dispense_date, dispensed_id)
                )
                print(f"تحديث جدول dispensed: {result1.rowcount} صف")

                # تحديث جدول dispensed_details
                result2 = conn.execute(
                    'UPDATE dispensed_details SET quantity = ?, price = ?, cases_count = ? WHERE dispensed_id = ?',
                    (float(quantity), float(price), int(cases_count), dispensed_id)
                )
                print(f"تحديث جدول dispensed_details: {result2.rowcount} صف")

                conn.commit()
                flash('تم تحديث المنصرف بنجاح', 'success')
                print(f"تم تحديث السجل {dispensed_id} بنجاح")
            except Exception as e:
                print(f"خطأ في تحديث السجل: {e}")
                import traceback
                print(f"تفاصيل الخطأ: {traceback.format_exc()}")
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            missing_fields = []
            if not dispense_month: missing_fields.append('التاريخ')
            if not quantity: missing_fields.append('الكمية')
            if not price: missing_fields.append('السعر')
            if not cases_count: missing_fields.append('عدد العلب')

            print(f"بيانات مفقودة: {missing_fields}")
            flash(f'يرجى إدخال البيانات المطلوبة: {", ".join(missing_fields)}', 'danger')
        return redirect(url_for('dispense_new'))

    # جلب بيانات السجل للتعديل
    dispensed_record = conn.execute('''
        SELECT
            d.id,
            d.drug_id,
            d.clinic_id,
            d.dispense_month,
            dr.name as drug_name,
            dr.unit as drug_unit,
            dc.name as category_name,
            c.name as clinic_name,
            dd.quantity,
            dd.price,
            dd.cases_count,
            strftime('%m/%Y', d.dispense_month) as formatted_date
        FROM dispensed d
        JOIN drugs dr ON d.drug_id = dr.id
        JOIN drug_categories dc ON dr.category_id = dc.id
        JOIN clinics c ON d.clinic_id = c.id
        JOIN dispensed_details dd ON d.id = dd.dispensed_id
        WHERE d.id = ?
    ''', (dispensed_id,)).fetchone()

    if not dispensed_record:
        flash('السجل غير موجود', 'danger')
        return redirect(url_for('dispense_new'))

    # جلب البيانات اللازمة للنموذج
    clinics = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
    ''').fetchall()
    categories = conn.execute('SELECT * FROM drug_categories').fetchall()
    conn.close()

    return render_template('edit_dispensed.html',
                         record=dispensed_record,
                         clinics=clinics,
                         categories=categories)

# مسار حذف سجل الصرف
@app.route('/dispense/<int:dispensed_id>/delete', methods=['POST'])
def delete_dispensed(dispensed_id):
    conn = get_db_connection()
    try:
        # التحقق من إمكانية الحذف أولاً
        dispensed_record = conn.execute(
            'SELECT dispense_month FROM dispensed WHERE id = ?',
            (dispensed_id,)
        ).fetchone()

        if not dispensed_record:
            flash('السجل غير موجود', 'danger')
            return redirect(url_for('dispense_new'))

        can_modify, months_diff = can_edit_or_delete(dispensed_record['dispense_month'])

        if not can_modify:
            flash(f'لا يمكن حذف هذا السجل. مر عليه {months_diff} أشهر (الحد الأقصى 4 أشهر)', 'danger')
            return redirect(url_for('dispense_new'))

        # حذف تفاصيل المنصرف أولاً
        conn.execute('DELETE FROM dispensed_details WHERE dispensed_id = ?', (dispensed_id,))
        # ثم حذف سجل المنصرف
        conn.execute('DELETE FROM dispensed WHERE id = ?', (dispensed_id,))
        conn.commit()
        flash('تم حذف السجل بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ أثناء الحذف: {str(e)}', 'danger')
    finally:
        conn.close()
    return redirect(url_for('dispense_new'))

# مسار صرف الأدوية (الرابط القديم)
@app.route('/dispense', methods=['GET', 'POST'])
def dispense():
    return redirect(url_for('dispense_new'))

# مسار صرف الأدوية (مسار إضافي للتأكد)
@app.route('/dispense_new', methods=['GET', 'POST'])
def dispense_new_alt():
    return dispense_new()

# API للتحقق من التكرار قبل الحفظ
@app.route('/api/check_duplicate', methods=['POST'])
def check_duplicate():
    """التحقق من وجود تكرار للدواء قبل الحفظ"""
    try:
        data = request.get_json()
        drug_id = data.get('drug_id')
        clinic_id = data.get('clinic_id')
        dispense_month = data.get('dispense_month')
        quantity = data.get('quantity')
        price = data.get('price')

        if not all([drug_id, clinic_id, dispense_month, quantity, price]):
            return jsonify({'error': 'بيانات ناقصة'}), 400

        # تحويل التاريخ
        dispense_date = f"{dispense_month}-01"

        conn = get_db_connection()

        # البحث عن السجلات المطابقة
        existing_records = conn.execute('''
            SELECT d.id, dd.quantity, dd.price, dr.name as drug_name
            FROM dispensed d
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            JOIN drugs dr ON d.drug_id = dr.id
            WHERE d.drug_id = ? AND d.clinic_id = ? AND d.dispense_month = ?
        ''', (drug_id, clinic_id, dispense_date)).fetchall()

        conn.close()

        # التحقق من وجود تطابق تام
        is_duplicate = False
        for record in existing_records:
            if (float(record['quantity']) == float(quantity) and
                float(record['price']) == float(price)):
                is_duplicate = True
                break

        return jsonify({
            'is_duplicate': is_duplicate,
            'drug_name': existing_records[0]['drug_name'] if existing_records else None,
            'existing_count': len(existing_records)
        })

    except Exception as e:
        print(f"خطأ في التحقق من التكرار: {e}")
        return jsonify({'error': 'حدث خطأ في التحقق'}), 500

# مسار تعديل الدواء
@app.route('/manage/drugs/<int:drug_id>/edit', methods=['POST'])
def edit_drug(drug_id):
    name = request.form.get('name')
    scientific_name = request.form.get('scientific_name')
    category_id = request.form.get('category_id')
    unit = request.form.get('unit')

    if name and category_id:
        conn = get_db_connection()
        try:
            conn.execute(
                'UPDATE drugs SET name = ?, scientific_name = ?, category_id = ?, unit = ? WHERE id = ?',
                (name, scientific_name, category_id, unit, drug_id)
            )
            conn.commit()
            flash('تم تحديث الدواء بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ أثناء تحديث الدواء: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم الدواء والتصنيف', 'danger')

    return redirect(url_for('manage_drugs_new'))

# مسار إدارة الأنسولين الموحد
@app.route('/manage/insulin', methods=['GET'])
def manage_insulin():
    """صفحة إدارة الأنسولين الشاملة - تجمع التكويد والأنواع والفئات"""
    conn = get_db_connection()
    try:
        # جلب جميع البيانات
        insulin_types = conn.execute('SELECT * FROM insulin_types ORDER BY name').fetchall()
        insulin_categories = conn.execute('SELECT * FROM insulin_categories ORDER BY name').fetchall()
        insulin_codes = conn.execute('SELECT * FROM insulin_codes ORDER BY code').fetchall()
        insulin_units = conn.execute('SELECT * FROM insulin_units ORDER BY name').fetchall()

        # الحصول على الكود التالي
        last_code = conn.execute('SELECT MAX(code) FROM insulin_codes').fetchone()[0]
        next_code = (last_code + 1) if last_code else 1

        # إحصائيات
        insulin_types_count = len(insulin_types)
        insulin_categories_count = len(insulin_categories)
        insulin_codes_count = len(insulin_codes)
        total_dispensed = conn.execute('SELECT COUNT(*) FROM insulin_dispensed').fetchone()[0]

        # إحصائيات الأنسولين تم تحميلها بنجاح

    except Exception as e:
        # خطأ في تحميل بيانات الأنسولين
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        insulin_types = []
        insulin_categories = []
        insulin_codes = []
        insulin_units = []
        next_code = 1
        insulin_types_count = 0
        insulin_categories_count = 0
        insulin_codes_count = 0
        total_dispensed = 0
    finally:
        conn.close()

    return render_template('insulin_management_unified.html',
                         insulin_types=insulin_types,
                         insulin_categories=insulin_categories,
                         insulin_codes=insulin_codes,
                         insulin_units=insulin_units,
                         next_code=next_code,
                         insulin_types_count=insulin_types_count,
                         insulin_categories_count=insulin_categories_count,
                         insulin_codes_count=insulin_codes_count,
                         insulin_units_count=len(insulin_units),
                         total_dispensed=total_dispensed)

@app.route('/manage/insulin_items', methods=['GET', 'POST'])
def manage_insulin_items():
    conn = get_db_connection()
    try:
        insulin_items = conn.execute('''
            SELECT i.*, c.name as clinic_name, a.name as area_name
            FROM insulin_dispensed i
            JOIN clinics c ON i.clinic_id = c.id
            JOIN areas a ON i.area_id = a.id
            ORDER BY i.id DESC
        ''').fetchall()

        # إحصائيات
        total_items = len(insulin_items)
        total_cost = sum(item['cost'] for item in insulin_items)
        this_month = datetime.now().strftime('%Y-%m')
        this_month_items = len([item for item in insulin_items if item['dispense_month'].startswith(this_month)])
        unique_clinics = len(set(item['clinic_id'] for item in insulin_items))

    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        insulin_items = []
        total_items = total_cost = this_month_items = unique_clinics = 0
    finally:
        conn.close()

    return render_template('manage_insulin.html',
                         insulin_items=insulin_items,
                         total_items=total_items,
                         total_cost=total_cost,
                         this_month_items=this_month_items,
                         unique_clinics=unique_clinics)

# مسارات تكويد الأنسولين
@app.route('/manage/insulin_codes', methods=['GET', 'POST'])
def manage_insulin_codes():
    """إدارة تكويدات الأنسولين - إضافة تكويد جديد"""
    if request.method == 'POST':
        code = request.form.get('code')
        name = request.form.get('name')
        unit = request.form.get('unit')
        type_name = request.form.get('type')
        description = request.form.get('description')

        print(f"إضافة تكويد جديد: code={code}, name={name}, unit={unit}, type={type_name}, description={description}")

        if code and name and unit and type_name:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO insulin_codes (code, name, unit, type, description) VALUES (?, ?, ?, ?, ?)',
                    (int(code), name, unit, type_name, description)
                )
                conn.commit()
                flash('تم إضافة التكويد بنجاح', 'success')
                print(f"تم إضافة التكويد {code} بنجاح")
            except sqlite3.IntegrityError:
                flash('هذا الكود موجود بالفعل', 'danger')
                print(f"الكود {code} موجود بالفعل")
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
                print(f"خطأ في إضافة التكويد: {e}")
            finally:
                conn.close()
        else:
            flash('يرجى إدخال جميع البيانات المطلوبة (الكود، الاسم، الوحدة، النوع)', 'danger')

        return redirect(url_for('manage_insulin'))

    # إعادة توجيه GET إلى الصفحة الموحدة
    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_codes/update', methods=['POST'])
def update_insulin_code():
    """تحديث تكويد أنسولين موجود"""
    code_id = request.form.get('code_id')
    code = request.form.get('code')
    name = request.form.get('name')
    unit = request.form.get('unit')
    type_name = request.form.get('type')
    description = request.form.get('description')

    print(f"تحديث التكويد {code_id}: code={code}, name={name}, unit={unit}, type={type_name}")

    if code_id and code and name and unit and type_name:
        conn = get_db_connection()
        try:
            conn.execute(
                'UPDATE insulin_codes SET code = ?, name = ?, unit = ?, type = ?, description = ? WHERE id = ?',
                (int(code), name, unit, type_name, description, int(code_id))
            )
            conn.commit()
            flash('تم تحديث التكويد بنجاح', 'success')
            print(f"تم تحديث التكويد {code_id} بنجاح")
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
            print(f"خطأ في تحديث التكويد: {e}")
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_codes/<int:code_id>/delete', methods=['POST'])
def delete_insulin_code(code_id):
    """حذف تكويد أنسولين"""
    print(f"حذف التكويد {code_id}")

    conn = get_db_connection()
    try:
        # التحقق من وجود منصرفات مرتبطة بهذا التكويد
        dispensed_count = conn.execute(
            'SELECT COUNT(*) FROM insulin_dispensed WHERE insulin_code_id = ?',
            (code_id,)
        ).fetchone()[0]

        if dispensed_count > 0:
            flash(f'لا يمكن حذف هذا التكويد لأنه مرتبط بـ {dispensed_count} منصرف', 'danger')
        else:
            conn.execute('DELETE FROM insulin_codes WHERE id = ?', (code_id,))
            conn.commit()
            flash('تم حذف التكويد بنجاح', 'success')
            print(f"تم حذف التكويد {code_id} بنجاح")
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'danger')
        print(f"خطأ في حذف التكويد: {e}")
    finally:
        conn.close()

    return redirect(url_for('manage_insulin'))

# مسارات أنواع الأنسولين
@app.route('/manage/insulin_types', methods=['POST'])
def manage_insulin_types():
    """إضافة نوع أنسولين جديد"""
    type_name = request.form.get('type_name')
    type_description = request.form.get('type_description')

    print(f"إضافة نوع جديد: name={type_name}, description={type_description}")

    if type_name:
        conn = get_db_connection()
        try:
            conn.execute(
                'INSERT INTO insulin_types (name, description) VALUES (?, ?)',
                (type_name, type_description)
            )
            conn.commit()
            flash('تم إضافة نوع الأنسولين بنجاح', 'success')
            print(f"تم إضافة النوع {type_name} بنجاح")
        except sqlite3.IntegrityError:
            flash('هذا النوع موجود بالفعل', 'danger')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
            print(f"خطأ في إضافة النوع: {e}")
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم النوع', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_types/update', methods=['POST'])
def update_insulin_type():
    """تحديث نوع أنسولين موجود"""
    type_id = request.form.get('type_id')
    type_name = request.form.get('type_name')
    type_description = request.form.get('type_description')

    print(f"تحديث النوع {type_id}: name={type_name}")

    if type_id and type_name:
        conn = get_db_connection()
        try:
            conn.execute(
                'UPDATE insulin_types SET name = ?, description = ? WHERE id = ?',
                (type_name, type_description, int(type_id))
            )
            conn.commit()
            flash('تم تحديث نوع الأنسولين بنجاح', 'success')
            print(f"تم تحديث النوع {type_id} بنجاح")
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
            print(f"خطأ في تحديث النوع: {e}")
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_types/<int:type_id>/delete', methods=['POST'])
def delete_insulin_type(type_id):
    """حذف نوع أنسولين"""
    print(f"حذف النوع {type_id}")

    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM insulin_types WHERE id = ?', (type_id,))
        conn.commit()
        flash('تم حذف نوع الأنسولين بنجاح', 'success')
        print(f"تم حذف النوع {type_id} بنجاح")
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'danger')
        print(f"خطأ في حذف النوع: {e}")
    finally:
        conn.close()

    return redirect(url_for('manage_insulin'))

# مسارات فئات الأنسولين
@app.route('/manage/insulin_categories', methods=['POST'])
def manage_insulin_categories():
    """إضافة فئة أنسولين جديدة"""
    category_name = request.form.get('category_name')
    category_description = request.form.get('category_description')

    print(f"إضافة فئة جديدة: name={category_name}, description={category_description}")

    if category_name:
        conn = get_db_connection()
        try:
            conn.execute(
                'INSERT INTO insulin_categories (name, description) VALUES (?, ?)',
                (category_name, category_description)
            )
            conn.commit()
            flash('تم إضافة فئة الأنسولين بنجاح', 'success')
            print(f"تم إضافة الفئة {category_name} بنجاح")
        except sqlite3.IntegrityError:
            flash('هذه الفئة موجودة بالفعل', 'danger')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
            print(f"خطأ في إضافة الفئة: {e}")
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم الفئة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_categories/update', methods=['POST'])
def update_insulin_category():
    """تحديث فئة أنسولين موجودة"""
    category_id = request.form.get('category_id')
    category_name = request.form.get('category_name')
    category_description = request.form.get('category_description')

    print(f"تحديث الفئة {category_id}: name={category_name}")

    if category_id and category_name:
        conn = get_db_connection()
        try:
            conn.execute(
                'UPDATE insulin_categories SET name = ?, description = ? WHERE id = ?',
                (category_name, category_description, int(category_id))
            )
            conn.commit()
            flash('تم تحديث فئة الأنسولين بنجاح', 'success')
            print(f"تم تحديث الفئة {category_id} بنجاح")
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
            print(f"خطأ في تحديث الفئة: {e}")
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_categories/<int:category_id>/delete', methods=['POST'])
def delete_insulin_category(category_id):
    """حذف فئة أنسولين"""
    print(f"حذف الفئة {category_id}")

    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM insulin_categories WHERE id = ?', (category_id,))
        conn.commit()
        flash('تم حذف فئة الأنسولين بنجاح', 'success')
        print(f"تم حذف الفئة {category_id} بنجاح")
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'danger')
        print(f"خطأ في حذف الفئة: {e}")
    finally:
        conn.close()

    return redirect(url_for('manage_insulin'))

# مسارات وحدات الأنسولين
@app.route('/manage/insulin_units', methods=['POST'])
def manage_insulin_units():
    """إضافة وحدة أنسولين جديدة"""
    unit_name = request.form.get('unit_name')
    unit_description = request.form.get('unit_description')

    if unit_name:
        conn = get_db_connection()
        try:
            conn.execute(
                'INSERT INTO insulin_units (name, description) VALUES (?, ?)',
                (unit_name, unit_description)
            )
            conn.commit()
            flash('تم إضافة وحدة الأنسولين بنجاح', 'success')
        except sqlite3.IntegrityError:
            flash('هذه الوحدة موجودة بالفعل', 'danger')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال اسم الوحدة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_units/update', methods=['POST'])
def update_insulin_unit():
    """تحديث وحدة أنسولين موجودة"""
    unit_id = request.form.get('unit_id')
    unit_name = request.form.get('unit_name')
    unit_description = request.form.get('unit_description')

    if unit_id and unit_name:
        conn = get_db_connection()
        try:
            conn.execute(
                'UPDATE insulin_units SET name = ?, description = ? WHERE id = ?',
                (unit_name, unit_description, int(unit_id))
            )
            conn.commit()
            flash('تم تحديث وحدة الأنسولين بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_insulin'))

@app.route('/manage/insulin_units/<int:unit_id>/delete', methods=['POST'])
def delete_insulin_unit(unit_id):
    """حذف وحدة أنسولين"""
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM insulin_units WHERE id = ?', (unit_id,))
        conn.commit()
        flash('تم حذف وحدة الأنسولين بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('manage_insulin'))

@app.route('/manage/drug_group_codes', methods=['GET', 'POST'])
def manage_drug_group_codes():
    if request.method == 'POST':
        code = request.form.get('code')
        name = request.form.get('name')
        description = request.form.get('description')

        if code and name:
            conn = get_db_connection()
            try:
                conn.execute(
                    'INSERT INTO drug_group_codes (code, name, description) VALUES (?, ?, ?)',
                    (code, name, description)
                )
                conn.commit()
                flash('تم إضافة الكود بنجاح', 'success')
            except sqlite3.IntegrityError:
                flash('هذا الكود موجود بالفعل', 'danger')
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
            finally:
                conn.close()
        else:
            flash('يرجى إدخال الكود والاسم', 'danger')

        return redirect(url_for('manage_drug_group_codes'))

    conn = get_db_connection()
    try:
        codes = conn.execute('SELECT * FROM drug_group_codes ORDER BY code').fetchall()
        last_code = conn.execute('SELECT MAX(code) FROM drug_group_codes').fetchone()[0]
        next_code = (last_code + 1) if last_code else 1
    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        codes = []
        next_code = 1
    finally:
        conn.close()

    return render_template('manage_drug_group_codes.html', codes=codes, next_code=next_code)

# تحديث كود مجموعة دوائية
@app.route('/manage/drug_group_codes/update', methods=['POST'])
def update_drug_group_code():
    print("تم استلام طلب تحديث كود المجموعة")
    print(f"جميع البيانات المرسلة: {dict(request.form)}")

    code_id = request.form.get('code_id')
    name = request.form.get('name')
    description = request.form.get('description')

    print(f"البيانات المستلمة: code_id='{code_id}', name='{name}', description='{description}'")

    if code_id and name:
        conn = get_db_connection()
        try:
            print(f"محاولة تحديث الكود {code_id} بالاسم '{name}'")
            result = conn.execute('''
                UPDATE drug_group_codes
                SET name = ?, description = ?
                WHERE id = ?
            ''', (name, description, code_id))
            conn.commit()
            print(f"تم تحديث {result.rowcount} صف")
            flash('تم تحديث المجموعة بنجاح', 'success')
        except Exception as e:
            print(f"خطأ في التحديث: {e}")
            flash(f'حدث خطأ في التحديث: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        missing_fields = []
        if not code_id: missing_fields.append('معرف الكود')
        if not name: missing_fields.append('اسم المجموعة')

        flash(f'يرجى إدخال البيانات المطلوبة: {", ".join(missing_fields)}', 'danger')
        print(f"بيانات مفقودة: {missing_fields}")

    return redirect(url_for('manage_drug_group_codes'))

# حذف كود مجموعة دوائية
@app.route('/manage/drug_group_codes/<int:code_id>/delete', methods=['POST'])
def delete_drug_group_code(code_id):
    print(f"تم استلام طلب حذف الكود: {code_id}")

    conn = get_db_connection()
    try:
        # التحقق من عدم استخدام هذا الكود في أي مجموعة
        usage_count = conn.execute('SELECT COUNT(*) FROM drug_groups WHERE group_code_id = ?', (code_id,)).fetchone()[0]
        print(f"عدد المجموعات التي تستخدم هذا الكود: {usage_count}")

        if usage_count > 0:
            flash(f'لا يمكن حذف هذا الكود لأنه مستخدم في {usage_count} مجموعة', 'danger')
            print(f"لا يمكن الحذف - الكود مستخدم في {usage_count} مجموعة")
        else:
            # الحصول على معلومات الكود قبل الحذف
            code_info = conn.execute('SELECT name FROM drug_group_codes WHERE id = ?', (code_id,)).fetchone()
            code_name = code_info[0] if code_info else f"الكود {code_id}"

            conn.execute('DELETE FROM drug_group_codes WHERE id = ?', (code_id,))
            conn.commit()
            flash(f'تم حذف "{code_name}" بنجاح', 'success')
            print(f"تم حذف الكود {code_id} بنجاح")
    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
        print(f"خطأ في الحذف: {e}")
    finally:
        conn.close()

    return redirect(url_for('manage_drug_group_codes'))

@app.route('/manage/drug_groups', methods=['GET', 'POST'])
def manage_drug_groups():
    if request.method == 'POST':
        print("تم استلام طلب POST لإدارة المجموعات الدوائية")
        print(f"جميع البيانات المرسلة: {dict(request.form)}")

        name = request.form.get('name')
        cost = request.form.get('cost')
        clinic_id = request.form.get('clinic_id')
        dispense_month = request.form.get('dispense_month')
        group_code_id = request.form.get('group_code_id')

        print(f"البيانات المستلمة: name='{name}', cost='{cost}', clinic_id='{clinic_id}', dispense_month='{dispense_month}', group_code_id='{group_code_id}'")

        # إذا لم يكن الاسم موجود، جرب الحصول عليه من group_code_id
        if not name and group_code_id:
            conn = get_db_connection()
            try:
                code_result = conn.execute('SELECT name FROM drug_group_codes WHERE id = ?', (group_code_id,)).fetchone()
                if code_result:
                    name = code_result[0]
                    print(f"تم الحصول على اسم المجموعة من قاعدة البيانات: {name}")
            except Exception as e:
                print(f"خطأ في الحصول على اسم المجموعة: {e}")
            finally:
                conn.close()

        if name and cost and clinic_id and dispense_month:
            conn = get_db_connection()
            try:
                # الحصول على area_id من clinic_id
                area_result = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()
                if not area_result:
                    flash(f'العيادة المحددة غير موجودة: {clinic_id}', 'danger')
                    return redirect(url_for('manage_drug_groups'))

                area_id = area_result[0]

                # تحويل التاريخ
                dispense_date = f"{dispense_month}-01"

                print(f"محاولة إدراج: name={name}, cost={cost}, clinic_id={clinic_id}, area_id={area_id}, dispense_date={dispense_date}, group_code_id={group_code_id}")

                conn.execute(
                    'INSERT INTO drug_groups (name, cost, clinic_id, area_id, dispense_month, group_code_id) VALUES (?, ?, ?, ?, ?, ?)',
                    (name, float(cost), clinic_id, area_id, dispense_date, group_code_id if group_code_id else None)
                )
                conn.commit()
                flash('تم إضافة المجموعة بنجاح', 'success')
                print("تم الحفظ بنجاح")
            except Exception as e:
                flash(f'حدث خطأ: {str(e)}', 'danger')
                print(f"خطأ في الحفظ: {e}")
            finally:
                conn.close()
        else:
            missing_fields = []
            if not name: missing_fields.append('اسم المجموعة')
            if not cost: missing_fields.append('التكلفة')
            if not clinic_id: missing_fields.append('العيادة')
            if not dispense_month: missing_fields.append('شهر الصرف')

            flash(f'يرجى إدخال البيانات المطلوبة: {", ".join(missing_fields)}', 'danger')
            print(f"بيانات مفقودة: {missing_fields}")

        return redirect(url_for('manage_drug_groups'))

    conn = get_db_connection()
    try:
        # التأكد من وجود جدول أكواد المجموعات الدوائية
        conn.execute('CREATE TABLE IF NOT EXISTS drug_group_codes (id INTEGER PRIMARY KEY, code INTEGER, name TEXT, description TEXT)')

        # التأكد من وجود العمود group_code_id في جدول drug_groups
        try:
            conn.execute('SELECT group_code_id FROM drug_groups LIMIT 1')
        except:
            # إضافة العمود إذا لم يكن موجود
            conn.execute('ALTER TABLE drug_groups ADD COLUMN group_code_id INTEGER')
            conn.commit()

        # إضافة بيانات افتراضية إذا لم تكن موجودة
        existing_codes = conn.execute('SELECT COUNT(*) FROM drug_group_codes').fetchone()[0]
        if existing_codes == 0:
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (1, 100, 'مجموعة القلب', 'أدوية القلب والأوعية الدموية')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (2, 200, 'مجموعة السكر', 'أدوية السكري')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (3, 300, 'مجموعة المضادات', 'المضادات الحيوية')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (4, 400, 'مجموعة الجهاز التنفسي', 'أدوية الجهاز التنفسي')")
            conn.execute("INSERT INTO drug_group_codes (id, code, name, description) VALUES (5, 500, 'مجموعة الجهاز الهضمي', 'أدوية الجهاز الهضمي')")
            conn.commit()

        groups = conn.execute('''
            SELECT dg.*, c.name as clinic_name, a.name as area_name, dgc.name as code_name
            FROM drug_groups dg
            JOIN clinics c ON dg.clinic_id = c.id
            JOIN areas a ON dg.area_id = a.id
            LEFT JOIN drug_group_codes dgc ON dg.group_code_id = dgc.id
            ORDER BY dg.id DESC
        ''').fetchall()

        clinics = conn.execute('''
            SELECT c.*, a.name as area_name, b.name as branch_name
            FROM clinics c
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            ORDER BY c.name
        ''').fetchall()

        group_codes = conn.execute('SELECT * FROM drug_group_codes ORDER BY code').fetchall()

    except Exception as e:
        flash(f'حدث خطأ في تحميل البيانات: {str(e)}', 'danger')
        print(f"خطأ في تحميل البيانات: {e}")
        groups = []
        clinics = []
        group_codes = []
    finally:
        conn.close()

    return render_template('manage_drug_groups.html',
                         groups=groups,
                         clinics=clinics,
                         group_codes=group_codes)

# تحديث مجموعة دوائية
@app.route('/manage/drug_groups/update', methods=['POST'])
def update_drug_group():
    group_id = request.form.get('group_id')
    name = request.form.get('name')
    cost = request.form.get('cost')
    clinic_id = request.form.get('clinic_id')
    dispense_month = request.form.get('dispense_month')
    group_code_id = request.form.get('group_code_id')

    if group_id and name and cost and clinic_id and dispense_month:
        conn = get_db_connection()
        try:
            # الحصول على area_id من clinic_id
            area_id = conn.execute('SELECT area_id FROM clinics WHERE id = ?', (clinic_id,)).fetchone()[0]

            # تحويل التاريخ
            dispense_date = f"{dispense_month}-01"

            conn.execute('''
                UPDATE drug_groups
                SET name = ?, cost = ?, clinic_id = ?, area_id = ?, dispense_month = ?, group_code_id = ?
                WHERE id = ?
            ''', (name, float(cost), clinic_id, area_id, dispense_date, group_code_id if group_code_id else None, group_id))
            conn.commit()
            flash('تم تحديث المجموعة بنجاح', 'success')
        except Exception as e:
            flash(f'حدث خطأ في التحديث: {str(e)}', 'danger')
        finally:
            conn.close()
    else:
        flash('يرجى إدخال جميع البيانات المطلوبة', 'danger')

    return redirect(url_for('manage_drug_groups'))

# حذف مجموعة دوائية
@app.route('/manage/drug_groups/<int:group_id>/delete', methods=['POST'])
def delete_drug_group(group_id):
    conn = get_db_connection()
    try:
        conn.execute('DELETE FROM drug_groups WHERE id = ?', (group_id,))
        conn.commit()
        flash('تم حذف المجموعة بنجاح', 'success')
    except Exception as e:
        flash(f'حدث خطأ في الحذف: {str(e)}', 'danger')
    finally:
        conn.close()

    return redirect(url_for('manage_drug_groups'))

# التقرير الشامل المبسط
@app.route('/reports/comprehensive')
def comprehensive_report():
    """التقرير الشامل المبسط"""
    # استلام المعاملات
    date_range = request.args.get('date_range', 'month')
    scope_type = request.args.get('scope_type', 'all')
    branch_id = request.args.get('branch_id')
    area_id = request.args.get('area_id')
    clinic_id = request.args.get('clinic_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    conn = get_db_connection()

    try:
        # بناء شروط التاريخ
        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_condition = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            date_range_text = f"الشهر الحالي ({current_month})"

            insulin_date_condition = "AND i.dispense_month = ?"
            insulin_date_params = [current_month]
        elif date_range == 'quarter':
            current_date = datetime.now()
            quarter_start = datetime(current_date.year, ((current_date.month - 1) // 3) * 3 + 1, 1)
            quarter_end = datetime(current_date.year, quarter_start.month + 2, 1) + timedelta(days=32)
            quarter_end = quarter_end.replace(day=1) - timedelta(days=1)

            date_condition = "AND d.dispense_month >= ? AND d.dispense_month <= ?"
            date_params = [quarter_start.strftime('%Y-%m-01'), quarter_end.strftime('%Y-%m-01')]
            date_range_text = f"الربع الحالي ({quarter_start.strftime('%Y-%m')} إلى {quarter_end.strftime('%Y-%m')})"

            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [quarter_start.strftime('%Y-%m'), quarter_end.strftime('%Y-%m')]
        elif date_range == 'quarter_1':
            current_year = datetime.now().year
            date_condition = "AND d.dispense_month >= ? AND d.dispense_month <= ?"
            date_params = [f'{current_year}-01-01', f'{current_year}-03-01']
            date_range_text = f"الربع الأول {current_year}"

            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [f'{current_year}-01', f'{current_year}-03']
        elif date_range == 'quarter_2':
            current_year = datetime.now().year
            date_condition = "AND d.dispense_month >= ? AND d.dispense_month <= ?"
            date_params = [f'{current_year}-04-01', f'{current_year}-06-01']
            date_range_text = f"الربع الثاني {current_year}"

            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [f'{current_year}-04', f'{current_year}-06']
        elif date_range == 'quarter_3':
            current_year = datetime.now().year
            date_condition = "AND d.dispense_month >= ? AND d.dispense_month <= ?"
            date_params = [f'{current_year}-07-01', f'{current_year}-09-01']
            date_range_text = f"الربع الثالث {current_year}"

            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [f'{current_year}-07', f'{current_year}-09']
        elif date_range == 'quarter_4':
            current_year = datetime.now().year
            date_condition = "AND d.dispense_month >= ? AND d.dispense_month <= ?"
            date_params = [f'{current_year}-10-01', f'{current_year}-12-01']
            date_range_text = f"الربع الرابع {current_year}"

            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [f'{current_year}-10', f'{current_year}-12']
        elif date_range == 'year':
            current_year = datetime.now().strftime('%Y')
            date_condition = "AND strftime('%Y', d.dispense_month) = ?"
            date_params = [current_year]
            date_range_text = f"السنة الحالية ({current_year})"

            insulin_date_condition = "AND i.dispense_month LIKE ?"
            insulin_date_params = [f'{current_year}-%']
        elif date_range == 'custom' and start_date and end_date:
            date_condition = "AND strftime('%Y-%m', d.dispense_month) >= ? AND strftime('%Y-%m', d.dispense_month) <= ?"
            date_params = [start_date, end_date]
            date_range_text = f"من {start_date} إلى {end_date}"

            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [start_date, end_date]
        else:
            date_condition = ""
            date_params = []
            date_range_text = "جميع الفترات"

            insulin_date_condition = ""
            insulin_date_params = []

        # بناء شروط الموقع
        location_condition = ""
        location_params = []
        scope_text = "جميع المواقع"

        if scope_type == 'branch' and branch_id:
            location_condition = "AND b.id = ?"
            location_params = [branch_id]
            branch_name = conn.execute('SELECT name FROM branches WHERE id = ?', (branch_id,)).fetchone()
            scope_text = f"الفرع: {branch_name['name'] if branch_name else 'غير محدد'}"
        elif scope_type == 'area' and area_id:
            location_condition = "AND a.id = ?"
            location_params = [area_id]
            area_info = conn.execute('''
                SELECT a.name, b.name as branch_name
                FROM areas a
                JOIN branches b ON a.branch_id = b.id
                WHERE a.id = ?
            ''', (area_id,)).fetchone()
            scope_text = f"المنطقة: {area_info['name'] if area_info else 'غير محدد'}"
        elif scope_type == 'clinic' and clinic_id:
            location_condition = "AND c.id = ?"
            location_params = [clinic_id]
            clinic_info = conn.execute('''
                SELECT c.name, a.name as area_name, b.name as branch_name
                FROM clinics c
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE c.id = ?
            ''', (clinic_id,)).fetchone()
            scope_text = f"العيادة: {clinic_info['name'] if clinic_info else 'غير محدد'}"

        # استعلام بيانات الأدوية
        drugs_query = f'''
            SELECT
                dr.name as drug_name,
                dc.name as category_name,
                dd.quantity,
                dr.unit as unit,
                dd.cases_count,
                dd.price,
                (dd.quantity * dd.price) as cost,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                strftime('%m/%Y', d.dispense_month) as dispense_date
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE 1=1 {date_condition} {location_condition}
            ORDER BY d.dispense_month DESC, b.name, a.name, c.name
            LIMIT 100
        '''

        drugs_data = conn.execute(drugs_query, date_params + location_params).fetchall()

        # استعلام بيانات الأنسولين
        insulin_query = f'''
            SELECT
                i.name,
                i.type,
                i.category,
                i.quantity,
                i.cases_count,
                COALESCE(i.price, 0) as price,
                COALESCE(i.rate, 0) as rate,
                i.cost,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                CASE
                    WHEN i.dispense_month IS NOT NULL AND i.dispense_month != '' AND i.dispense_month != 'None'
                    THEN CASE
                        WHEN LENGTH(i.dispense_month) = 7
                        THEN strftime('%m/%Y', i.dispense_month || '-01')
                        WHEN LENGTH(i.dispense_month) = 10
                        THEN strftime('%m/%Y', i.dispense_month)
                        ELSE 'غير محدد'
                    END
                    ELSE 'غير محدد'
                END as dispense_month
            FROM insulin_dispensed i
            JOIN clinics c ON i.clinic_id = c.id
            JOIN areas a ON i.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE 1=1 {insulin_date_condition} {location_condition.replace('c.', 'c.').replace('a.', 'a.').replace('b.', 'b.')}
            ORDER BY i.dispense_month DESC, b.name, a.name, c.name
            LIMIT 100
        '''

        insulin_data = conn.execute(insulin_query, insulin_date_params + location_params).fetchall()

        # استعلام المجموعات الدوائية مع تطبيق الفلاتر
        try:
            # بناء شروط التاريخ للمجموعات الدوائية
            groups_date_condition = ""
            groups_date_params = []

            if date_range == 'month':
                current_month = datetime.now().strftime('%Y-%m')
                groups_date_condition = "AND strftime('%Y-%m', dg.dispense_month) = ?"
                groups_date_params = [current_month]
            elif date_range == 'quarter':
                current_date = datetime.now()
                quarter_start = datetime(current_date.year, ((current_date.month - 1) // 3) * 3 + 1, 1)
                quarter_end = datetime(current_date.year, quarter_start.month + 2, 1) + timedelta(days=32)
                quarter_end = quarter_end.replace(day=1) - timedelta(days=1)

                groups_date_condition = "AND dg.dispense_month >= ? AND dg.dispense_month <= ?"
                groups_date_params = [quarter_start.strftime('%Y-%m'), quarter_end.strftime('%Y-%m')]
            elif date_range == 'year':
                current_year = datetime.now().strftime('%Y')
                groups_date_condition = "AND strftime('%Y', dg.dispense_month) = ?"
                groups_date_params = [current_year]
            elif date_range == 'custom' and start_date and end_date:
                groups_date_condition = "AND dg.dispense_month >= ? AND dg.dispense_month <= ?"
                groups_date_params = [start_date, end_date]

            drug_groups_query = f'''
                SELECT
                    dg.name as group_name,
                    'غير محدد' as group_code,
                    1 as drug_count,
                    1 as total_quantity,
                    dg.cost as total_cost,
                    strftime('%m/%Y', dg.dispense_month) as dispense_month,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name
                FROM drug_groups dg
                JOIN clinics c ON dg.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE 1=1 {groups_date_condition} {location_condition}
                ORDER BY dg.cost DESC
                LIMIT 50
            '''

            drug_groups_data = conn.execute(drug_groups_query, groups_date_params + location_params).fetchall()
        except Exception as e:
            print(f"خطأ في استعلام المجموعات الدوائية: {e}")
            drug_groups_data = []

        # ملخص العيادات مع تطبيق الفلاتر
        try:
            clinics_summary_query = f'''
                SELECT
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name,
                    COALESCE(drugs_stats.drugs_count, 0) as drugs_count,
                    COALESCE(drugs_stats.drugs_cost, 0) as drugs_cost,
                    COALESCE(drugs_stats.drugs_cases, 0) as drugs_cases,
                    COALESCE(insulin_stats.insulin_count, 0) as insulin_count,
                    COALESCE(insulin_stats.insulin_cost, 0) as insulin_cost,
                    COALESCE(insulin_stats.insulin_cases, 0) as insulin_cases,
                    COALESCE(groups_stats.groups_cost, 0) as groups_cost
                FROM clinics c
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                LEFT JOIN (
                    SELECT
                        d.clinic_id,
                        COUNT(d.id) as drugs_count,
                        SUM(dd.quantity * dd.price) as drugs_cost,
                        SUM(dd.cases_count) as drugs_cases
                    FROM dispensed d
                    JOIN dispensed_details dd ON d.id = dd.dispensed_id
                    WHERE 1=1 {date_condition}
                    GROUP BY d.clinic_id
                ) drugs_stats ON c.id = drugs_stats.clinic_id
                LEFT JOIN (
                    SELECT
                        i.clinic_id,
                        COUNT(i.id) as insulin_count,
                        SUM(i.cost) as insulin_cost,
                        SUM(i.cases_count) as insulin_cases
                    FROM insulin_dispensed i
                    WHERE 1=1 {insulin_date_condition}
                    GROUP BY i.clinic_id
                ) insulin_stats ON c.id = insulin_stats.clinic_id
                LEFT JOIN (
                    SELECT
                        dg.clinic_id,
                        SUM(dg.cost) as groups_cost
                    FROM drug_groups dg
                    WHERE 1=1 {groups_date_condition}
                    GROUP BY dg.clinic_id
                ) groups_stats ON c.id = groups_stats.clinic_id
                WHERE 1=1 {location_condition}
                ORDER BY (COALESCE(drugs_stats.drugs_cost, 0) + COALESCE(insulin_stats.insulin_cost, 0) + COALESCE(groups_stats.groups_cost, 0)) DESC
                LIMIT 20
            '''

            # دمج جميع المعاملات
            all_params = date_params + insulin_date_params + groups_date_params + location_params
            clinics_summary = conn.execute(clinics_summary_query, all_params).fetchall()
        except Exception as e:
            print(f"خطأ في استعلام ملخص العيادات: {e}")
            clinics_summary = []

        # التأكد من وجود جدول أكواد المجموعات الدوائية
        try:
            conn.execute('CREATE TABLE IF NOT EXISTS drug_group_codes (id INTEGER PRIMARY KEY, code INTEGER, name TEXT, description TEXT)')
            conn.commit()
        except:
            pass

        # جلب بيانات الفروع والمناطق والعيادات للفلاتر وتحويلها إلى قواميس
        branches_raw = conn.execute('SELECT id, name FROM branches ORDER BY name').fetchall()
        branches = [{'id': row['id'], 'name': row['name']} for row in branches_raw]

        areas_raw = conn.execute('SELECT id, name, branch_id FROM areas ORDER BY name').fetchall()
        areas = [{'id': row['id'], 'name': row['name'], 'branch_id': row['branch_id']} for row in areas_raw]

        clinics_raw = conn.execute('SELECT id, name, area_id FROM clinics ORDER BY name').fetchall()
        clinics = [{'id': row['id'], 'name': row['name'], 'area_id': row['area_id']} for row in clinics_raw]

        # حساب الإحصائيات
        total_drugs_dispensed = len(drugs_data)
        total_insulin_dispensed = len(insulin_data)
        total_cost = sum(float(item['cost']) for item in drugs_data) + sum(float(item['cost']) for item in insulin_data)

        # عدد العيادات النشطة
        active_clinics = set()
        for item in drugs_data:
            active_clinics.add(item['clinic_name'])
        for item in insulin_data:
            active_clinics.add(item['clinic_name'])
        active_clinics_count = len(active_clinics)

        return render_template('comprehensive_report.html',
                             drugs_data=drugs_data,
                             insulin_data=insulin_data,
                             drug_groups_data=drug_groups_data,
                             clinics_summary=clinics_summary,
                             branches=branches,
                             areas=areas,
                             clinics=clinics,
                             date_range=date_range,
                             scope_type=scope_type,
                             branch_id=branch_id,
                             area_id=area_id,
                             clinic_id=clinic_id,
                             start_date=start_date,
                             end_date=end_date,
                             date_range_text=date_range_text,
                             scope_text=scope_text,
                             current_date=datetime.now().strftime('%Y-%m-%d %H:%M'),
                             total_drugs_dispensed=total_drugs_dispensed,
                             total_insulin_dispensed=total_insulin_dispensed,
                             total_cost=total_cost,
                             active_clinics_count=active_clinics_count)

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'error')
        return redirect(url_for('reports'))

    finally:
        conn.close()

@app.route('/reports/comprehensive/export-excel')
def comprehensive_report_export_excel():
    """تصدير التقرير الشامل إلى Excel مع شيتات منفصلة"""
    try:
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment
        except ImportError:
            flash('مكتبة openpyxl غير مثبتة. يرجى تثبيتها لتصدير ملفات Excel', 'error')
            return redirect(url_for('comprehensive_report'))

        # استلام المعاملات (نفس معاملات التقرير الأساسي)
        date_range = request.args.get('date_range', 'month')
        scope_type = request.args.get('scope_type', 'all')
        branch_id = request.args.get('branch_id')
        area_id = request.args.get('area_id')
        clinic_id = request.args.get('clinic_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        conn = get_db_connection()

        # نفس منطق بناء الشروط من التقرير الأساسي
        # بناء شروط التاريخ
        if date_range == 'month':
            current_month = datetime.now().strftime('%Y-%m')
            date_condition = "AND strftime('%Y-%m', d.dispense_month) = ?"
            date_params = [current_month]
            insulin_date_condition = "AND i.dispense_month = ?"
            insulin_date_params = [current_month]
        elif date_range == 'quarter':
            current_date = datetime.now()
            quarter_start = datetime(current_date.year, ((current_date.month - 1) // 3) * 3 + 1, 1)
            quarter_end = datetime(current_date.year, quarter_start.month + 2, 1) + timedelta(days=32)
            quarter_end = quarter_end.replace(day=1) - timedelta(days=1)

            date_condition = "AND d.dispense_month >= ? AND d.dispense_month <= ?"
            date_params = [quarter_start.strftime('%Y-%m-01'), quarter_end.strftime('%Y-%m-01')]
            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [quarter_start.strftime('%Y-%m'), quarter_end.strftime('%Y-%m')]
        elif date_range == 'year':
            current_year = datetime.now().strftime('%Y')
            date_condition = "AND strftime('%Y', d.dispense_month) = ?"
            date_params = [current_year]
            insulin_date_condition = "AND i.dispense_month LIKE ?"
            insulin_date_params = [f'{current_year}-%']
        elif date_range == 'custom' and start_date and end_date:
            date_condition = "AND strftime('%Y-%m', d.dispense_month) >= ? AND strftime('%Y-%m', d.dispense_month) <= ?"
            date_params = [start_date, end_date]
            insulin_date_condition = "AND i.dispense_month >= ? AND i.dispense_month <= ?"
            insulin_date_params = [start_date, end_date]
        else:
            date_condition = ""
            date_params = []
            insulin_date_condition = ""
            insulin_date_params = []

        # بناء شروط الموقع
        location_condition = ""
        location_params = []

        if scope_type == 'branch' and branch_id:
            location_condition = "AND b.id = ?"
            location_params = [branch_id]
        elif scope_type == 'area' and area_id:
            location_condition = "AND a.id = ?"
            location_params = [area_id]
        elif scope_type == 'clinic' and clinic_id:
            location_condition = "AND c.id = ?"
            location_params = [clinic_id]

        # جلب البيانات (نفس الاستعلامات من التقرير الأساسي)
        # استعلام الأدوية
        drugs_query = f'''
            SELECT
                dr.name as drug_name,
                dc.name as category_name,
                dd.quantity,
                dr.unit as unit,
                dd.cases_count,
                dd.price,
                (dd.quantity * dd.price) as cost,
                strftime('%m/%Y', d.dispense_month) as dispense_date,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name
            FROM dispensed d
            JOIN drugs dr ON d.drug_id = dr.id
            JOIN drug_categories dc ON dr.category_id = dc.id
            JOIN clinics c ON d.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            JOIN dispensed_details dd ON d.id = dd.dispensed_id
            WHERE 1=1 {date_condition} {location_condition}
            ORDER BY d.dispense_month DESC, b.name, a.name, c.name
        '''

        drugs_data = conn.execute(drugs_query, date_params + location_params).fetchall()

        # استعلام الأنسولين
        insulin_query = f'''
            SELECT
                i.name,
                i.type,
                i.category,
                i.quantity,
                i.cases_count,
                COALESCE(i.price, 0) as price,
                COALESCE(i.rate, 0) as rate,
                i.cost,
                CASE
                    WHEN i.dispense_month IS NOT NULL AND i.dispense_month != ''
                    THEN strftime('%m/%Y', i.dispense_month || '-01')
                    ELSE 'غير محدد'
                END as dispense_month,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name
            FROM insulin_dispensed i
            JOIN clinics c ON i.clinic_id = c.id
            JOIN areas a ON i.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            WHERE 1=1 {insulin_date_condition} {location_condition.replace('c.', 'c.').replace('a.', 'a.').replace('b.', 'b.')}
            ORDER BY i.dispense_month DESC, b.name, a.name, c.name
        '''

        insulin_data = conn.execute(insulin_query, insulin_date_params + location_params).fetchall()

        # استعلام المجموعات الدوائية
        drug_groups_query = '''
            SELECT
                dg.name as group_name,
                1 as drug_count,
                1 as total_quantity,
                dg.cost as total_cost,
                strftime('%m/%Y', dg.dispense_month) as dispense_month,
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name
            FROM drug_groups dg
            JOIN clinics c ON dg.clinic_id = c.id
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            ORDER BY dg.cost DESC
        '''

        drug_groups_data = conn.execute(drug_groups_query).fetchall()

        # استعلام ملخص العيادات
        clinics_summary_query = '''
            SELECT
                c.name as clinic_name,
                a.name as area_name,
                b.name as branch_name,
                COALESCE(drugs_stats.drugs_count, 0) as drugs_count,
                COALESCE(drugs_stats.drugs_cost, 0) as drugs_cost,
                COALESCE(drugs_stats.drugs_cases, 0) as drugs_cases,
                COALESCE(insulin_stats.insulin_count, 0) as insulin_count,
                COALESCE(insulin_stats.insulin_cost, 0) as insulin_cost,
                COALESCE(insulin_stats.insulin_cases, 0) as insulin_cases,
                COALESCE(groups_stats.groups_cost, 0) as groups_cost
            FROM clinics c
            JOIN areas a ON c.area_id = a.id
            JOIN branches b ON a.branch_id = b.id
            LEFT JOIN (
                SELECT
                    d.clinic_id,
                    COUNT(d.id) as drugs_count,
                    SUM(dd.quantity * dd.price) as drugs_cost,
                    SUM(dd.cases_count) as drugs_cases
                FROM dispensed d
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                GROUP BY d.clinic_id
            ) drugs_stats ON c.id = drugs_stats.clinic_id
            LEFT JOIN (
                SELECT
                    i.clinic_id,
                    COUNT(i.id) as insulin_count,
                    SUM(i.cost) as insulin_cost,
                    SUM(i.cases_count) as insulin_cases
                FROM insulin_dispensed i
                GROUP BY i.clinic_id
            ) insulin_stats ON c.id = insulin_stats.clinic_id
            LEFT JOIN (
                SELECT
                    dg.clinic_id,
                    SUM(dg.cost) as groups_cost
                FROM drug_groups dg
                GROUP BY dg.clinic_id
            ) groups_stats ON c.id = groups_stats.clinic_id
            ORDER BY (COALESCE(drugs_stats.drugs_cost, 0) + COALESCE(insulin_stats.insulin_cost, 0) + COALESCE(groups_stats.groups_cost, 0)) DESC
        '''

        clinics_summary = conn.execute(clinics_summary_query).fetchall()

        conn.close()

        # إنشاء ملف Excel
        wb = Workbook()

        # تنسيق الرؤوس
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="343A40", end_color="343A40", fill_type="solid")
        center_alignment = Alignment(horizontal="center", vertical="center")

        # حذف الشيت الافتراضي
        wb.remove(wb.active)

        # شيت الأدوية
        if drugs_data:
            ws_drugs = wb.create_sheet("الأدوية")
            headers = ["اسم الدواء", "التصنيف", "الكمية", "الوحدة", "عدد الحالات", "سعر الوحدة", "التكلفة", "شهر الصرف", "العيادة", "المنطقة", "الفرع"]
            ws_drugs.append(headers)

            # تنسيق الرؤوس
            for col_num, header in enumerate(headers, 1):
                cell = ws_drugs.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # إضافة البيانات
            for row_num, drug in enumerate(drugs_data, 2):
                ws_drugs.append([
                    drug['drug_name'],
                    drug['category_name'],
                    drug['quantity'],
                    drug['unit'],
                    drug['cases_count'],
                    drug['price'],
                    drug['cost'],
                    drug['dispense_date'],
                    drug['clinic_name'],
                    drug['area_name'],
                    drug['branch_name']
                ])

        # شيت الأنسولين
        if insulin_data:
            ws_insulin = wb.create_sheet("الأنسولين")
            headers = ["اسم الأنسولين", "النوع", "الفئة", "الكمية", "عدد الحالات", "سعر الوحدة", "المعدل", "التكلفة", "شهر الصرف", "العيادة", "المنطقة", "الفرع"]
            ws_insulin.append(headers)

            # تنسيق الرؤوس
            for col_num, header in enumerate(headers, 1):
                cell = ws_insulin.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # إضافة البيانات
            for insulin in insulin_data:
                ws_insulin.append([
                    insulin['name'],
                    insulin['type'],
                    insulin['category'],
                    insulin['quantity'],
                    insulin['cases_count'],
                    insulin['price'],
                    insulin['rate'],
                    insulin['cost'],
                    insulin['dispense_month'],
                    insulin['clinic_name'],
                    insulin['area_name'],
                    insulin['branch_name']
                ])

        # شيت المجموعات الدوائية
        if drug_groups_data:
            ws_groups = wb.create_sheet("المجموعات الدوائية")
            headers = ["اسم المجموعة", "عدد الأدوية", "إجمالي الكمية", "إجمالي التكلفة", "شهر الصرف", "العيادة", "المنطقة", "الفرع"]
            ws_groups.append(headers)

            # تنسيق الرؤوس
            for col_num, header in enumerate(headers, 1):
                cell = ws_groups.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # إضافة البيانات
            for group in drug_groups_data:
                ws_groups.append([
                    group['group_name'],
                    group['drug_count'],
                    group['total_quantity'],
                    group['total_cost'],
                    group['dispense_month'],
                    group['clinic_name'],
                    group['area_name'],
                    group['branch_name']
                ])

        # شيت ملخص العيادات
        if clinics_summary:
            ws_summary = wb.create_sheet("ملخص العيادات")
            headers = ["اسم العيادة", "المنطقة", "الفرع", "عدد صرف الأدوية", "تكلفة الأدوية", "عدد حالات الأدوية", "عدد صرف الأنسولين", "تكلفة الأنسولين", "عدد حالات الأنسولين", "تكلفة المجموعات"]
            ws_summary.append(headers)

            # تنسيق الرؤوس
            for col_num, header in enumerate(headers, 1):
                cell = ws_summary.cell(row=1, column=col_num)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment

            # إضافة البيانات
            for clinic in clinics_summary:
                ws_summary.append([
                    clinic['clinic_name'],
                    clinic['area_name'],
                    clinic['branch_name'],
                    clinic['drugs_count'],
                    clinic['drugs_cost'],
                    clinic['drugs_cases'],
                    clinic['insulin_count'],
                    clinic['insulin_cost'],
                    clinic['insulin_cases'],
                    clinic['groups_cost']
                ])

        # حفظ الملف
        filename = f"التقرير_الشامل_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join('temp', filename)

        # التأكد من وجود مجلد temp
        if not os.path.exists('temp'):
            os.makedirs('temp')

        wb.save(filepath)

        # إرسال الملف للتحميل
        response = make_response(send_file(filepath, as_attachment=True, download_name=filename))

        # حذف الملف المؤقت بعد الإرسال
        try:
            os.remove(filepath)
        except:
            pass

        return response

    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير Excel: {str(e)}', 'error')
        return redirect(url_for('comprehensive_report'))

# تم حذف التقرير الشامل القديم - استخدم /reports/comprehensive الجديد

@app.route('/reports/export_all_excel')
def export_all_reports_excel():
    """تصدير جميع التقارير في ملف إكسل واحد مع صفحات منفصلة"""
    try:
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment
        except ImportError:
            flash('مكتبة openpyxl غير مثبتة. يرجى تثبيتها لتصدير ملفات Excel', 'error')
            return redirect(url_for('reports'))

        from datetime import datetime
        import io
        
        # إنشاء ملف إكسل جديد
        wb = Workbook()
        wb.remove(wb.active)
        
        conn = get_db_connection()
        
        # تنسيق العناوين
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        center_alignment = Alignment(horizontal="center", vertical="center")
        
        # 1. تقرير الأدوية
        try:
            ws_drugs = wb.create_sheet("تقرير الأدوية")
            drugs_data = conn.execute('''
                SELECT 
                    d.id, c.name, a.name, b.name, dr.name, dc.name,
                    dd.quantity, dd.price, dd.cases_count,
                    (dd.quantity * dd.price), d.dispense_month
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN drugs dr ON d.drug_id = dr.id
                JOIN drug_categories dc ON dr.category_id = dc.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                ORDER BY d.dispense_month DESC
            ''').fetchall()
            
            if drugs_data:
                headers = ["رقم السجل", "العيادة", "المنطقة", "الفرع", "اسم الدواء", "التصنيف", "الكمية", "السعر", "عدد الحالات", "التكلفة الإجمالية", "شهر الصرف"]
                ws_drugs.append(headers)
                
                for col_num, header in enumerate(headers, 1):
                    cell = ws_drugs.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                for row in drugs_data:
                    ws_drugs.append(list(row))
        except:
            pass
        
        # 2. تقرير الأنسولين
        try:
            ws_insulin = wb.create_sheet("تقرير الأنسولين")
            insulin_data = conn.execute('''
                SELECT 
                    i.id, c.name, a.name, b.name, i.name, i.type,
                    i.unit, i.cases_count, i.quantity, i.price,
                    i.cost, i.category, i.dispense_month
                FROM insulin_dispensed i
                JOIN clinics c ON i.clinic_id = c.id
                JOIN areas a ON i.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                ORDER BY i.dispense_month DESC
            ''').fetchall()
            
            if insulin_data:
                headers = ["رقم السجل", "العيادة", "المنطقة", "الفرع", "اسم الأنسولين", "النوع", "الوحدة", "عدد الحالات", "الكمية", "السعر", "التكلفة", "الفئة", "شهر الصرف"]
                ws_insulin.append(headers)
                
                for col_num, header in enumerate(headers, 1):
                    cell = ws_insulin.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                for row in insulin_data:
                    ws_insulin.append(list(row))
        except:
            pass
        
        # 3. تقرير المجموعات الدوائية
        try:
            ws_groups = wb.create_sheet("المجموعات الدوائية")
            groups_data = conn.execute('''
                SELECT 
                    dg.id, c.name, a.name, b.name, dg.name,
                    dg.cost, dgc.code, dgc.description, dg.dispense_month
                FROM drug_groups dg
                JOIN clinics c ON dg.clinic_id = c.id
                JOIN areas a ON dg.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                LEFT JOIN drug_group_codes dgc ON dg.group_code_id = dgc.id
                ORDER BY dg.dispense_month DESC
            ''').fetchall()
            
            if groups_data:
                headers = ["رقم السجل", "العيادة", "المنطقة", "الفرع", "اسم المجموعة", "التكلفة", "كود المجموعة", "وصف المجموعة", "شهر الصرف"]
                ws_groups.append(headers)
                
                for col_num, header in enumerate(headers, 1):
                    cell = ws_groups.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                for row in groups_data:
                    ws_groups.append(list(row))
        except:
            pass
        
        # 4. ملخص العيادات
        try:
            ws_clinics = wb.create_sheet("ملخص العيادات")
            clinics_data = conn.execute('''
                SELECT 
                    c.name, a.name, b.name,
                    COUNT(DISTINCT d.id),
                    COUNT(DISTINCT i.id),
                    COUNT(DISTINCT dg.id),
                    COALESCE(SUM(dd.quantity * dd.price), 0),
                    COALESCE(SUM(i.cost), 0),
                    COALESCE(SUM(dg.cost), 0)
                FROM clinics c
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                LEFT JOIN dispensed d ON c.id = d.clinic_id
                LEFT JOIN dispensed_details dd ON d.id = dd.dispensed_id
                LEFT JOIN insulin_dispensed i ON c.id = i.clinic_id
                LEFT JOIN drug_groups dg ON c.id = dg.clinic_id
                GROUP BY c.id, c.name, a.name, b.name
                ORDER BY b.name, a.name, c.name
            ''').fetchall()
            
            if clinics_data:
                headers = ["العيادة", "المنطقة", "الفرع", "عدد سجلات الأدوية", "عدد سجلات الأنسولين", "عدد المجموعات الدوائية", "إجمالي تكلفة الأدوية", "إجمالي تكلفة الأنسولين", "إجمالي تكلفة المجموعات"]
                ws_clinics.append(headers)
                
                for col_num, header in enumerate(headers, 1):
                    cell = ws_clinics.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                for row in clinics_data:
                    ws_clinics.append(list(row))
        except:
            pass
        
        conn.close()
        
        # إنشاء ملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        # إنشاء اسم الملف مع التاريخ
        current_date = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        filename = f'comprehensive_report_{current_date}.xlsx'
        
        # إرجاع الملف للتحميل
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير التقارير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

# مسارات إدارة البيانات
@app.route('/admin/data-management')
def data_management():
    """صفحة إدارة البيانات والنسخ الاحتياطية"""
    # الحصول على إحصائيات قاعدة البيانات
    table_counts = get_table_counts()

    # الحصول على قائمة النسخ الاحتياطية
    backup_files = []
    backup_dir = 'backups'
    if os.path.exists(backup_dir):
        for file in os.listdir(backup_dir):
            if file.endswith('.zip'):
                file_path = os.path.join(backup_dir, file)
                file_size = os.path.getsize(file_path)
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                backup_files.append({
                    'name': file,
                    'size': round(file_size / 1024, 2),  # KB
                    'date': file_time.strftime('%Y-%m-%d %H:%M:%S')
                })

    # الحصول على بيانات إضافية للتفريغ المتقدم
    categories = get_categories_with_counts()
    clinics = get_clinics_with_counts()
    insulin_types = get_insulin_types_with_counts()



    return render_template('data_management.html',
                         table_counts=table_counts,
                         backup_files=backup_files,
                         categories=categories,
                         clinics=clinics,
                         insulin_types=insulin_types)

@app.route('/admin/create-backup', methods=['POST'])
def create_backup_route():
    """إنشاء نسخة احتياطية"""
    try:
        backup_path, backup_filename = create_backup()
        if backup_path:
            flash(f'تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}', 'success')
        else:
            flash('فشل في إنشاء النسخة الاحتياطية', 'danger')
    except Exception as e:
        flash(f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}', 'danger')

    return redirect(url_for('data_management'))

@app.route('/admin/download-backup/<filename>')
def download_backup(filename):
    """تحميل نسخة احتياطية"""
    try:
        backup_path = os.path.join('backups', filename)
        if os.path.exists(backup_path):
            return send_file(backup_path, as_attachment=True)
        else:
            flash('الملف غير موجود', 'danger')
            return redirect(url_for('data_management'))
    except Exception as e:
        flash(f'خطأ في تحميل الملف: {str(e)}', 'danger')
        return redirect(url_for('data_management'))

@app.route('/admin/restore-backup', methods=['POST'])
def restore_backup_route():
    """استعادة نسخة احتياطية"""
    try:
        backup_filename = request.form.get('backup_filename')
        confirm_restore = request.form.get('confirm_restore')

        # التحقق من تأكيد الاستعادة
        if confirm_restore != 'confirmed':
            flash('يجب تأكيد الاستعادة أولاً', 'danger')
            return redirect(url_for('data_management'))

        if not backup_filename:
            flash('لم يتم اختيار ملف للاستعادة', 'danger')
            return redirect(url_for('data_management'))

        # إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
        current_backup_path, current_backup_filename = create_backup()
        if current_backup_path:
            flash(f'تم إنشاء نسخة احتياطية من الحالة الحالية: {current_backup_filename}', 'info')

        # استعادة النسخة الاحتياطية
        backup_path = os.path.join('backups', backup_filename)
        if restore_backup(backup_path):
            flash(f'تم استعادة النسخة الاحتياطية بنجاح: {backup_filename}', 'success')
        else:
            flash('فشل في استعادة النسخة الاحتياطية', 'danger')

    except Exception as e:
        flash(f'خطأ في استعادة النسخة الاحتياطية: {str(e)}', 'danger')

    return redirect(url_for('data_management'))

@app.route('/admin/delete-backup/<filename>', methods=['POST'])
def delete_backup_route(filename):
    """حذف نسخة احتياطية"""
    try:
        backup_path = os.path.join('backups', filename)
        if os.path.exists(backup_path):
            os.remove(backup_path)
            flash(f'تم حذف النسخة الاحتياطية: {filename}', 'success')
        else:
            flash('الملف غير موجود', 'danger')
    except Exception as e:
        flash(f'خطأ في حذف الملف: {str(e)}', 'danger')

    return redirect(url_for('data_management'))

@app.route('/admin/delete-multiple-backups', methods=['POST'])
def delete_multiple_backups_route():
    """حذف مجموعة من النسخ الاحتياطية"""
    try:
        selected_backups = request.form.getlist('selected_backups')
        if not selected_backups:
            flash('لم يتم تحديد أي نسخ احتياطية للحذف', 'warning')
            return redirect(url_for('data_management'))

        deleted_count = 0
        for filename in selected_backups:
            backup_path = os.path.join('backups', filename)
            if os.path.exists(backup_path):
                os.remove(backup_path)
                deleted_count += 1

        if deleted_count > 0:
            flash(f'تم حذف {deleted_count} نسخة احتياطية بنجاح', 'success')
        else:
            flash('لم يتم حذف أي ملفات', 'warning')
    except Exception as e:
        flash(f'خطأ في حذف الملفات: {str(e)}', 'danger')

    return redirect(url_for('data_management'))

@app.route('/admin/clear-data', methods=['POST'])
def clear_data_route():
    """تفريغ البيانات"""
    try:
        clear_type = request.form.get('clear_type')
        confirm_text = request.form.get('confirm_text')

        # التحقق من النص التأكيدي
        if confirm_text != 'تأكيد الحذف':
            flash('يجب كتابة "تأكيد الحذف" للمتابعة', 'danger')
            return redirect(url_for('data_management'))

        # إنشاء نسخة احتياطية قبل التفريغ
        backup_path, backup_filename = create_backup()
        if backup_path:
            flash(f'تم إنشاء نسخة احتياطية قبل التفريغ: {backup_filename}', 'info')

        if clear_type == 'dispensing_only':
            # تفريغ بيانات الصرف فقط
            if clear_dispensing_data():
                flash('تم تفريغ بيانات الصرف بنجاح (تم الاحتفاظ بالبيانات الأساسية)', 'success')
            else:
                flash('فشل في تفريغ بيانات الصرف', 'danger')

        elif clear_type == 'all_data':
            # تفريغ جميع البيانات
            if clear_all_data():
                flash('تم تفريغ جميع البيانات بنجاح', 'warning')
            else:
                flash('فشل في تفريغ البيانات', 'danger')

        elif clear_type == 'custom':
            # تفريغ مخصص
            selected_tables = request.form.getlist('selected_tables')
            if selected_tables:
                if clear_specific_tables(selected_tables):
                    flash(f'تم تفريغ الجداول المحددة بنجاح: {", ".join(selected_tables)}', 'success')
                else:
                    flash('فشل في تفريغ الجداول المحددة', 'danger')
            else:
                flash('لم يتم اختيار أي جداول', 'warning')

        elif clear_type == 'advanced':
            # تفريغ متقدم
            advanced_type = request.form.get('advanced_type')

            if advanced_type == 'categories':
                selected_categories = request.form.getlist('selected_categories')
                print(f"🏷️ التصنيفات المحددة: {selected_categories}")
                if selected_categories:
                    result = clear_by_categories(selected_categories)
                    print(f"🏷️ نتيجة تفريغ التصنيفات: {result}")
                    if result:
                        flash(f'تم تفريغ التصنيفات المحددة بنجاح ({len(selected_categories)} تصنيف)', 'success')
                    else:
                        flash('فشل في تفريغ التصنيفات المحددة', 'danger')
                else:
                    flash('لم يتم اختيار أي تصنيفات', 'warning')

            elif advanced_type == 'clinics':
                selected_clinics = request.form.getlist('selected_clinics')
                print(f"🏥 العيادات المحددة: {selected_clinics}")
                if selected_clinics:
                    result = clear_by_clinics(selected_clinics)
                    print(f"🏥 نتيجة تفريغ العيادات: {result}")
                    if result:
                        flash(f'تم تفريغ العيادات المحددة بنجاح ({len(selected_clinics)} عيادة)', 'success')
                    else:
                        flash('فشل في تفريغ العيادات المحددة', 'danger')
                else:
                    flash('لم يتم اختيار أي عيادات', 'warning')

            elif advanced_type == 'insulin_types':
                selected_insulin_types = request.form.getlist('selected_insulin_types')
                print(f"💉 أنواع الأنسولين المحددة: {selected_insulin_types}")
                if selected_insulin_types:
                    result = clear_by_insulin_types(selected_insulin_types)
                    print(f"💉 نتيجة تفريغ أنواع الأنسولين: {result}")
                    if result:
                        flash(f'تم تفريغ أنواع الأنسولين المحددة بنجاح ({len(selected_insulin_types)} نوع)', 'success')
                    else:
                        flash('فشل في تفريغ أنواع الأنسولين المحددة', 'danger')
                else:
                    flash('لم يتم اختيار أي أنواع أنسولين', 'warning')

            elif advanced_type == 'date_range':
                start_date = request.form.get('start_date')
                end_date = request.form.get('end_date')
                if start_date and end_date:
                    if clear_by_date_range(start_date, end_date):
                        flash(f'تم تفريغ البيانات للفترة من {start_date} إلى {end_date} بنجاح', 'success')
                    else:
                        flash('فشل في تفريغ البيانات للفترة المحددة', 'danger')
                else:
                    flash('يجب تحديد تاريخ البداية والنهاية', 'warning')

    except Exception as e:
        flash(f'خطأ في عملية التفريغ: {str(e)}', 'danger')

    return redirect(url_for('data_management'))

@app.route('/admin/advanced-clear')
def advanced_clear():
    """صفحة التفريغ المتقدم المنفصلة"""
    categories = get_categories_with_counts()
    clinics = get_clinics_with_counts()
    insulin_types = get_insulin_types_with_counts()



    return render_template('advanced_clear.html',
                         categories=categories,
                         clinics=clinics,
                         insulin_types=insulin_types)







# نقطة دخول التطبيق
if __name__ == '__main__':
    print("🚀 بدء تشغيل تطبيق منصرف الأدوية...")
    print("📡 الخادم يعمل على: http://localhost:8080")
    print("🔧 وضع التطوير: مفعل")
    print("💾 نظام إدارة البيانات: متوفر على /admin/data-management")
    try:
        app.run(debug=True, port=8080, host='0.0.0.0')
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لتطبيق إدارة منصرف الأدوية
"""

import sys
import os
from datetime import datetime

# إصلاح مشكلة ترميز النصوص العربية والرموز التعبيرية في Windows
if os.name == 'nt':  # Windows
    try:
        # ضبط ترميز وحدة التحكم إلى UTF-8
        import ctypes
        ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        ctypes.windll.kernel32.SetConsoleCP(65001)
    except Exception:
        pass

    # إعادة تكوين تدفقات الإخراج لاستخدام UTF-8
    try:
        # للإصدارات الحديثة من Python (3.7+)
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        # للإصدارات الأقدم من Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)                                                                                                                    

                                      