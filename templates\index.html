{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    .hero-section {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0.5rem;
    }

    .feature-card {
        transition: all 0.3s ease;
        border: none;
        border-radius: 0.75rem;
        overflow: hidden;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
        font-size: 3.5rem;
        margin-bottom: 1rem;
    }

    .card-header-custom {
        background: linear-gradient(45deg, #36b9cc 0%, #1cc88a 100%);
        color: white;
        border-radius: 0.5rem 0.5rem 0 0 !important;
        padding: 1.5rem;
    }

    .quick-access-card {
        background-color: #f8f9fc;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .btn-custom {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s;
    }

    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .stats-card {
        border-left: 4px solid;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        background-color: white;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
    }

    .stats-card.primary {
        border-left-color: #4e73df;
    }

    .stats-card.success {
        border-left-color: #1cc88a;
    }

    .stats-card.warning {
        border-left-color: #f6c23e;
    }

    .stats-card.danger {
        border-left-color: #e74a3b;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section shadow">
    <div class="container py-3">
        <div class="row align-items-center">
            <div class="col-lg-9 text-center text-lg-end">
                <h2 class="fw-bold mb-2">نظام إدارة منصرف الأدوية</h2>
                <p class="mb-3">منصة متكاملة لإدارة صرف الأدوية للعيادات والإدارات وفقاً للمناطق داخل الفروع</p>
                <div class="d-flex justify-content-center justify-content-lg-end gap-2">
                    <button onclick="goToDispense()" class="btn btn-light btn-custom">
                        <i class="mdi mdi-pill me-1"></i>صرف الأدوية
                    </button>
                    <a href="{{ url_for('simple_report') }}" class="btn btn-outline-light btn-custom">
                        <i class="mdi mdi-chart-bar me-1"></i>التقرير المبسط
                    </a>
                </div>
            </div>
            <div class="col-lg-3 d-none d-lg-block text-center">
                <i class="mdi mdi-medical-bag text-white" style="font-size: 5rem; opacity: 0.8;"></i>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Quick Access Section -->
    <div class="quick-access-card mb-5">
        <h4 class="mb-4 text-primary"><i class="mdi mdi-flash me-2"></i>الوصول السريع</h4>
        <div class="row">
            <div class="col-md-4 mb-3">
                <div class="form-group">
                    <label for="branch" class="form-label fw-bold">اختر الفرع</label>
                    <select id="branch" class="form-select form-select-lg">
                        <option value="">-- اختر الفرع --</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}">{{ branch.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-group">
                    <label for="area" class="form-label fw-bold">اختر المنطقة</label>
                    <select id="area" class="form-select form-select-lg" disabled>
                        <option value="">-- اختر المنطقة --</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="form-group">
                    <label for="clinic" class="form-label fw-bold">اختر العيادة</label>
                    <select id="clinic" class="form-select form-select-lg" disabled>
                        <option value="">-- اختر العيادة --</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="text-center mt-3">
            <a href="{{ url_for('dispense_new') }}" id="goToDispense" class="btn btn-primary btn-lg btn-custom" disabled>
                <i class="mdi mdi-pill me-1"></i>الانتقال إلى شاشة إدخال المنصرف
            </a>
        </div>
    </div>



    <!-- Copyright Button -->
    <div class="text-center mb-5">
        <a href="{{ url_for('copyright') }}" class="btn btn-danger btn-lg btn-custom">
            <i class="mdi mdi-copyright me-1"></i>حقوق الملكية: ك/أحمد علي أحمد (أحمد كوكب)
        </a>
    </div>

    <!-- Features Section -->
    <div class="mt-5">
        <h3 class="mb-4 text-center fw-bold">إدارة النظام</h3>
        <div class="row">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card feature-card shadow h-100">
                <div class="card-header-custom text-center">
                    <i class="mdi mdi-hospital-building feature-icon"></i>
                    <h5 class="fw-bold">إدارة العيادات</h5>
                </div>
                <div class="card-body text-center">
                    <p class="card-text">إضافة وتعديل وحذف الفروع والمناطق والعيادات</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('manage_branches') }}" class="btn btn-primary btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>الفروع
                        </a>
                        <a href="{{ url_for('manage_areas') }}" class="btn btn-outline-primary btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>المناطق
                        </a>
                        <a href="{{ url_for('manage_clinics') }}" class="btn btn-outline-primary btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>العيادات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card feature-card shadow h-100">
                <div class="card-header-custom text-center" style="background: linear-gradient(45deg, #1cc88a 0%, #20c997 100%);">
                    <i class="mdi mdi-pill feature-icon"></i>
                    <h5 class="fw-bold">إدارة الأدوية</h5>
                </div>
                <div class="card-body text-center">
                    <p class="card-text">إضافة وتعديل وحذف الأدوية وتصنيفاتها</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('manage_drugs_new') }}" class="btn btn-success btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>إدارة الأدوية
                        </a>
                        <a href="{{ url_for('manage_drug_categories_new') }}" class="btn btn-outline-success btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>إدارة التصنيفات
                        </a>
                        <a href="{{ url_for('dispense_new') }}" class="btn btn-outline-success btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>صرف الأدوية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card feature-card shadow h-100">
                <div class="card-header-custom text-center" style="background: linear-gradient(45deg, #f6c23e 0%, #fd7e14 100%);">
                    <i class="mdi mdi-package-variant feature-icon"></i>
                    <h5 class="fw-bold">المجموعات الدوائية</h5>
                </div>
                <div class="card-body text-center">
                    <p class="card-text">إدارة المجموعات الدوائية والأنسولين</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('manage_drug_groups') }}" class="btn btn-warning btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>المجموعات الدوائية
                        </a>
                        <a href="{{ url_for('manage_insulin') }}" class="btn btn-outline-warning btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>إدارة الأنسولين
                        </a>
                        <a href="{{ url_for('insulin_dispense') }}" class="btn btn-outline-warning btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>منصرف الأنسولين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card feature-card shadow h-100">
                <div class="card-header-custom text-center" style="background: linear-gradient(45deg, #36b9cc 0%, #0dcaf0 100%);">
                    <i class="mdi mdi-chart-bar feature-icon"></i>
                    <h5 class="fw-bold">التقارير</h5>
                </div>
                <div class="card-body text-center">
                    <p class="card-text">عرض تقارير مفصلة عن المنصرف</p>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('reports') }}" class="btn btn-info btn-custom">
                            <i class="mdi mdi-arrow-left me-1"></i>التقارير
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function goToDispense() {
    const selectedClinic = document.getElementById('clinic_selector')?.value;
    if (selectedClinic) {
        window.location.href = "{{ url_for('dispense_new') }}?clinic_id=" + selectedClinic;
    } else {
        window.location.href = "{{ url_for('dispense_new') }}";
    }
}

$(document).ready(function() {
        // عند تغيير الفرع
        $('#branch').change(function() {
            var branchId = $(this).val();
            if (branchId) {
                // تفعيل قائمة المناطق
                $('#area').prop('disabled', false);

                // جلب المناطق من الخادم
                $.getJSON('/api/areas/' + branchId, function(data) {
                    var options = '<option value="">-- اختر المنطقة --</option>';
                    $.each(data, function(index, area) {
                        options += '<option value="' + area.id + '">' + area.name + '</option>';
                    });
                    $('#area').html(options);
                });
            } else {
                // تعطيل وتفريغ قائمة المناطق
                $('#area').prop('disabled', true).html('<option value="">-- اختر المنطقة --</option>');
                $('#clinic').prop('disabled', true).html('<option value="">-- اختر العيادة --</option>');
                $('#goToDispense').prop('disabled', true);
            }
        });

        // عند تغيير المنطقة
        $('#area').change(function() {
            var areaId = $(this).val();
            if (areaId) {
                // تفعيل قائمة العيادات
                $('#clinic').prop('disabled', false);

                // جلب العيادات من الخادم
                $.getJSON('/api/clinics/' + areaId, function(data) {
                    var options = '<option value="">-- اختر العيادة --</option>';
                    $.each(data, function(index, clinic) {
                        options += '<option value="' + clinic.id + '">' + clinic.name + '</option>';
                    });
                    $('#clinic').html(options);
                });
            } else {
                // تعطيل وتفريغ قائمة العيادات
                $('#clinic').prop('disabled', true).html('<option value="">-- اختر العيادة --</option>');
                $('#goToDispense').prop('disabled', true);
            }
        });

        // عند تغيير العيادة
        $('#clinic').change(function() {
            var clinicId = $(this).val();
            if (clinicId) {
                // تفعيل زر الانتقال إلى شاشة الصرف
                $('#goToDispense').prop('disabled', false);

                // تحديث رابط الزر ليتضمن معرف العيادة
                var href = "{{ url_for('dispense_new') }}";
                $('#goToDispense').attr('href', href + '?clinic_id=' + clinicId);
            } else {
                // تعطيل زر الانتقال إلى شاشة الصرف
                $('#goToDispense').prop('disabled', true);
            }
        });
    });
</script>
{% endblock %}
