{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block head %}
<style>
@media print {
    /* إخفاء الأزرار وخيارات التصفية عند الطباعة */
    .btn, .card-header .d-flex .d-flex, .no-print {
        display: none !important;
    }

    /* إخفاء بطاقة خيارات التصفية */
    .card.border-info {
        display: none !important;
    }

    /* إخفاء التنبيهات */
    .alert {
        display: none !important;
    }

    /* تحسين عرض الطباعة */
    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background: #343a40 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    /* تحسين عرض الجداول */
    table {
        font-size: 12px !important;
    }

    thead {
        background: #343a40 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    /* تحسين عرض بطاقات الإحصائيات للطباعة */
    .bg-primary, .bg-success, .bg-warning, .bg-info {
        background: white !important;
        color: black !important;
        border: 1px solid #333 !important;
    }

    /* إضافة عنوان للطباعة */
    @page {
        size: A4;
        margin: 1cm;
    }

    /* تحسين عرض الجدول للطباعة */
    .table-responsive {
        overflow: visible !important;
    }

    /* إضافة فواصل صفحات */
    .card {
        page-break-inside: avoid;
    }

    /* تحسين عرض الصفحة */
    body {
        font-size: 12px !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .report-logo {
        max-width: 60px !important;
        height: auto !important;
        margin: 0 auto 8px !important;
        display: block !important;
    }
}

.report-logo {
    max-width: 80px;
    height: auto;
    margin: 0 auto 8px;
    display: block;
}

.table-dark-header {
    background-color: #343a40 !important;
    color: white !important;
}

.table-dark-footer {
    background-color: #6c757d !important;
    color: white !important;
}

.hidden-element {
    display: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- عنوان التقرير -->
            <div class="card border-primary mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="mdi mdi-needle me-2"></i>{{ title }}
                        </h4>
                        <div class="d-flex gap-2 no-print">
                            <button type="button" onclick="window.print()" class="btn btn-light btn-sm">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('export_insulin_enhanced_excel', **request.args) }}" class="btn btn-success btn-sm">
                                <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                            </a>
                            <button type="button" onclick="exportToPDF()" class="btn btn-danger btn-sm">
                                <i class="mdi mdi-file-pdf me-1"></i>تصدير PDF
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-light btn-sm">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- الشعار -->
                    <div class="text-center">
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                    </div>

                    <!-- معلومات التقرير -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <strong>الفترة الزمنية:</strong> {{ date_range_text }}
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ التقرير:</strong> {{ current_date }}
                        </div>
                        <div class="col-md-4">
                            <strong>إجمالي السجلات:</strong> {{ insulin_items|length }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- خيارات التصفية -->
            <div class="card border-info mb-4 no-print">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="mdi mdi-filter me-2"></i>خيارات التصفية
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('insulin_report_enhanced') }}">
                        <div class="row">
                            <!-- نوع التصفية -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label" for="filter_type">نوع التصفية:</label>
                                <select name="filter_type" id="filter_type" class="form-select" onchange="toggleFilterType()" title="اختر نوع التصفية" aria-label="نوع التصفية">
                                    <option value="category" {% if request.args.get('filter_type') == 'category' %}selected{% endif %}>حسب الفئة</option>
                                    <option value="type" {% if request.args.get('filter_type') == 'type' %}selected{% endif %}>حسب النوع</option>
                                </select>
                            </div>

                            <!-- فئة المريض -->
                            <div class="col-md-3 mb-3" id="categoryFilter">
                                <label class="form-label" for="category">فئة المريض:</label>
                                <select name="category" id="category" class="form-select" title="اختر تصنيف الأنسولين" aria-label="تصنيف الأنسولين">
                                    <option value="">جميع الفئات</option>
                                    <option value="هيئة" {% if request.args.get('category') == 'هيئة' %}selected{% endif %}>هيئة</option>
                                    <option value="طلاب" {% if request.args.get('category') == 'طلاب' %}selected{% endif %}>طلاب</option>
                                    <option value="رضع" {% if request.args.get('category') == 'رضع' %}selected{% endif %}>رضع</option>
                                    <option value="مرأة معيلة" {% if request.args.get('category') == 'مرأة معيلة' %}selected{% endif %}>مرأة معيلة</option>
                                </select>
                            </div>

                            <!-- نوع الأنسولين -->
                            <div class="col-md-3 mb-3 hidden-element" id="typeFilter">
                                <label class="form-label" for="type">نوع الأنسولين:</label>
                                <select name="type" id="type" class="form-select" title="اختر نوع الأنسولين" aria-label="نوع الأنسولين">
                                    <option value="">جميع الأنواع</option>
                                    <option value="مدعم" {% if request.args.get('type') == 'مدعم' %}selected{% endif %}>مدعم</option>
                                    <option value="هيئة" {% if request.args.get('type') == 'هيئة' %}selected{% endif %}>هيئة</option>
                                </select>
                            </div>

                            <!-- الفترة الزمنية -->
                            <div class="col-md-3 mb-3">
                                <label class="form-label" for="enhanced_date_range">الفترة الزمنية:</label>
                                <select name="date_range" class="form-select" id="enhanced_date_range" title="اختر الفترة الزمنية" aria-label="الفترة الزمنية" onchange="toggleDateFields()">
                                    <optgroup label="فترات قصيرة">
                                        <option value="month" {% if request.args.get('date_range') == 'month' %}selected{% endif %}>💉 الشهر الحالي</option>
                                        <option value="current_and_previous_month" {% if request.args.get('date_range') == 'current_and_previous_month' %}selected{% endif %}>💉 الشهر الحالي والسابق</option>
                                        <option value="last_3_months" {% if request.args.get('date_range') == 'last_3_months' %}selected{% endif %}>💉 آخر 3 أشهر</option>
                                    </optgroup>
                                    <optgroup label="فترات متوسطة">
                                        <option value="last_6_months" {% if request.args.get('date_range') == 'last_6_months' %}selected{% endif %}>📊 آخر 6 أشهر</option>
                                        <option value="quarter" {% if request.args.get('date_range') == 'quarter' %}selected{% endif %}>📊 الربع الحالي</option>
                                        <option value="q1" {% if request.args.get('date_range') == 'q1' %}selected{% endif %}>📊 الربع الأول (يناير - مارس)</option>
                                        <option value="q2" {% if request.args.get('date_range') == 'q2' %}selected{% endif %}>📊 الربع الثاني (أبريل - يونيو)</option>
                                        <option value="q3" {% if request.args.get('date_range') == 'q3' %}selected{% endif %}>📊 الربع الثالث (يوليو - سبتمبر)</option>
                                        <option value="q4" {% if request.args.get('date_range') == 'q4' %}selected{% endif %}>📊 الربع الرابع (أكتوبر - ديسمبر)</option>
                                    </optgroup>
                                    <optgroup label="فترات طويلة">
                                        <option value="first_half_year" {% if request.args.get('date_range') == 'first_half_year' %}selected{% endif %}>📈 النصف الأول من السنة</option>
                                        <option value="second_half_year" {% if request.args.get('date_range') == 'second_half_year' %}selected{% endif %}>📈 النصف الثاني من السنة</option>
                                        <option value="last_12_months" {% if request.args.get('date_range') == 'last_12_months' %}selected{% endif %}>📈 آخر 12 شهر</option>
                                        <option value="year" {% if request.args.get('date_range') == 'year' %}selected{% endif %}>📈 السنة الحالية</option>
                                    </optgroup>
                                    <optgroup label="فترة مخصصة">
                                        <option value="custom" {% if request.args.get('date_range') == 'custom' %}selected{% endif %}>🗓️ فترة مخصصة</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>

                        <!-- نطاق التقرير -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label" for="enhanced_scope_type">نطاق التقرير:</label>
                                <select name="scope_type" class="form-select" id="enhanced_scope_type" onchange="toggleLocationSelectors()" title="اختر نطاق التقرير" aria-label="نطاق التقرير">
                                    <option value="all" {% if request.args.get('scope_type') == 'all' %}selected{% endif %}>جميع الفروع</option>
                                    <option value="branch" {% if request.args.get('scope_type') == 'branch' %}selected{% endif %}>فرع محدد</option>
                                    <option value="area" {% if request.args.get('scope_type') == 'area' %}selected{% endif %}>منطقة محددة</option>
                                    <option value="clinic" {% if request.args.get('scope_type') == 'clinic' %}selected{% endif %}>عيادة محددة</option>
                                </select>
                            </div>

                            <!-- حقول اختيار الموقع -->
                            <div class="col-md-9">
                                <div class="row location-selectors hidden-element">
                                    <div class="col-md-4 mb-3" id="branch_container">
                                        <label class="form-label" for="branch_id">الفرع:</label>
                                        <select name="branch_id" class="form-select" id="branch_id" onchange="updateAreas()" title="اختر الفرع" aria-label="الفرع">
                                            <option value="">-- اختر الفرع --</option>
                                            {% for branch in branches %}
                                            <option value="{{ branch.id }}" {% if request.args.get('branch_id') == branch.id|string %}selected{% endif %}>{{ branch.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <div class="col-md-4 mb-3 hidden-element" id="area_container">
                                        <label class="form-label" for="area_id">المنطقة:</label>
                                        <select name="area_id" class="form-select" id="area_id" onchange="updateClinics()" title="اختر المنطقة" aria-label="المنطقة">
                                            <option value="">-- اختر المنطقة --</option>
                                            {% if areas %}
                                                {% for area in areas %}
                                                <option value="{{ area.id }}" {% if request.args.get('area_id') == area.id|string %}selected{% endif %}>{{ area.name }}</option>
                                                {% endfor %}
                                            {% endif %}
                                        </select>
                                    </div>

                                    <div class="col-md-4 mb-3 hidden-element" id="clinic_container">
                                        <label class="form-label" for="clinic_id">العيادة:</label>
                                        <select name="clinic_id" class="form-select" id="clinic_id" title="اختر العيادة" aria-label="العيادة">
                                            <option value="">-- اختر العيادة --</option>
                                            {% if clinics %}
                                                {% for clinic in clinics %}
                                                <option value="{{ clinic.id }}" {% if request.args.get('clinic_id') == clinic.id|string %}selected{% endif %}>{{ clinic.name }}</option>
                                                {% endfor %}
                                            {% endif %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- حقول التاريخ المخصصة -->
                        <div class="row enhanced-custom-date-range hidden-element">
                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="start_date">من شهر:</label>
                                <input type="month" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', '') }}" title="شهر البداية" aria-label="شهر البداية" placeholder="YYYY-MM">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label" for="end_date">إلى شهر:</label>
                                <input type="month" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', '') }}" title="شهر النهاية" aria-label="شهر النهاية" placeholder="YYYY-MM">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="mdi mdi-magnify me-1"></i>تطبيق التصفية
                                </button>
                                <a href="{{ url_for('insulin_report_enhanced') }}" class="btn btn-secondary">
                                    <i class="mdi mdi-refresh me-1"></i>إعادة تعيين
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- نوع الاختيار المحدد -->
            {% if filter_text %}
            <div class="alert alert-info no-print">
                <div class="d-flex align-items-center">
                    <i class="mdi mdi-information me-2"></i>
                    <strong>نوع الاختيار المحدد:</strong> {{ filter_text }}
                </div>
            </div>
            {% endif %}

            <!-- الإحصائيات -->
            <div class="row mb-4 no-print">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>{{ total_cases }}</h3>
                            <p class="mb-0">إجمالي الحالات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>{{ total_quantity }}</h3>
                            <p class="mb-0">إجمالي الكمية</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>{{ "%.2f"|format(total_cost) }}</h3>
                            <p class="mb-0">إجمالي التكلفة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>{{ insulin_items|length }}</h3>
                            <p class="mb-0">عدد السجلات</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول البيانات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="mdi mdi-table me-2"></i>تفاصيل الأنسولين
                    </h5>
                </div>
                <div class="card-body">
                    {% if insulin_items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark-header">
                                <tr>
                                    <th>#</th>
                                    <th>الصنف</th>
                                    <th>النوع</th>
                                    <th>الفئة</th>
                                    <th>الوحدة</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المعدل</th>
                                    <th>الرصيد</th>
                                    <th>عدد الحالات</th>
                                    <th>التكلفة</th>
                                    {% if show_location %}
                                    <th>{{ location_type }}</th>
                                    {% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in insulin_items %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>
                                        <span class="badge {% if item.type == 'مدعم' %}bg-success{% else %}bg-primary{% endif %}">
                                            {{ item.type }}
                                        </span>
                                    </td>
                                    <td>{{ item.category }}</td>
                                    <td>{{ item.unit }}</td>
                                    <td>{{ "%.2f"|format(item.price) }}</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.rate or '-' }}</td>
                                    <td>{{ item.id_number or '-' }}</td>
                                    <td>{{ item.cases_count }}</td>
                                    <td>{{ "%.2f"|format(item.cost) }}</td>
                                    {% if show_location %}
                                    <td>
                                        {% if location_type == 'العيادة' %}
                                            {{ item.clinic_name }}
                                        {% elif location_type == 'المنطقة' %}
                                            {{ item.area_name }}
                                        {% elif location_type == 'الفرع' %}
                                            {{ item.branch_name }}
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="mdi mdi-information-outline me-2"></i>
                        لا توجد بيانات أنسولين متاحة للعرض في الفترة والتصفية المحددة
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleFilterOptions() {
    const filterType = document.querySelector('select[name="filter_type"]').value;
    const categoryFilter = document.getElementById('categoryFilter');
    const typeFilter = document.getElementById('typeFilter');

    if (filterType === 'category') {
        categoryFilter.classList.remove('hidden-element');
        typeFilter.classList.add('hidden-element');
    } else if (filterType === 'type') {
        categoryFilter.classList.add('hidden-element');
        typeFilter.classList.remove('hidden-element');
    }
}

function toggleDateFields() {
    const dateRange = document.getElementById('enhanced_date_range').value;
    const customDateFields = document.querySelector('.enhanced-custom-date-range');

    if (dateRange === 'custom') {
        customDateFields.classList.remove('hidden-element');
        customDateFields.style.display = 'flex';
    } else {
        customDateFields.classList.add('hidden-element');
        customDateFields.style.display = 'none';
    }
}

// استدعاء الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleDateFields();
    toggleLocationSelectors();
});

function toggleLocationSelectors() {
    const scopeType = document.getElementById('enhanced_scope_type').value;
    const locationSelectors = document.querySelector('.location-selectors');
    const branchContainer = document.getElementById('branch_container');
    const areaContainer = document.getElementById('area_container');
    const clinicContainer = document.getElementById('clinic_container');

    if (scopeType === 'all') {
        locationSelectors.classList.add('hidden-element');
        branchContainer.classList.add('hidden-element');
        areaContainer.classList.add('hidden-element');
        clinicContainer.classList.add('hidden-element');
    } else {
        locationSelectors.classList.remove('hidden-element');
        branchContainer.classList.remove('hidden-element');

        if (scopeType === 'area') {
            areaContainer.classList.remove('hidden-element');
            clinicContainer.classList.add('hidden-element');
        } else if (scopeType === 'clinic') {
            areaContainer.classList.remove('hidden-element');
            clinicContainer.classList.remove('hidden-element');
        } else {
            areaContainer.classList.add('hidden-element');
            clinicContainer.classList.add('hidden-element');
        }
    }
}

function updateAreas() {
    const branchId = document.getElementById('branch_id').value;
    const areaSelect = document.getElementById('area_id');
    const clinicSelect = document.getElementById('clinic_id');

    // مسح المناطق والعيادات
    areaSelect.innerHTML = '<option value="">-- اختر المنطقة --</option>';
    clinicSelect.innerHTML = '<option value="">-- اختر العيادة --</option>';

    if (branchId) {
        // جلب المناطق للفرع المحدد
        fetch(`/api/areas/${branchId}`)
            .then(response => response.json())
            .then(areas => {
                areas.forEach(area => {
                    const option = document.createElement('option');
                    option.value = area.id;
                    option.textContent = area.name;
                    areaSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error fetching areas:', error));
    }
}

function updateClinics() {
    const areaId = document.getElementById('area_id').value;
    const clinicSelect = document.getElementById('clinic_id');

    // مسح العيادات
    clinicSelect.innerHTML = '<option value="">-- اختر العيادة --</option>';

    if (areaId) {
        // جلب العيادات للمنطقة المحددة
        fetch(`/api/clinics/${areaId}`)
            .then(response => response.json())
            .then(clinics => {
                clinics.forEach(clinic => {
                    const option = document.createElement('option');
                    option.value = clinic.id;
                    option.textContent = clinic.name;
                    clinicSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error fetching clinics:', error));
    }
}

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleFilterOptions();
    toggleDateFields();
    toggleLocationSelectors();

    // إضافة مستمعي الأحداث
    document.getElementById('enhanced_date_range').addEventListener('change', toggleDateFields);
    document.getElementById('enhanced_scope_type').addEventListener('change', toggleLocationSelectors);
    document.getElementById('branch_id').addEventListener('change', updateAreas);
    document.getElementById('area_id').addEventListener('change', updateClinics);
});

// وظيفة تصدير إلى PDF
async function exportToPDF() {
    try {
        // إخفاء العناصر غير المطلوبة
        var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
        noprint.forEach(function(element) {
            element.style.display = 'none';
        });

        // الحصول على المحتوى المراد تحويله
        var element = document.querySelector('.container-fluid');

        // تحويل إلى صورة
        const canvas = await html2canvas(element, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: element.scrollWidth,
            height: element.scrollHeight,
            scrollX: 0,
            scrollY: 0
        });

        // إنشاء PDF بالوضع الأفقي للجداول العريضة
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('l', 'mm', 'a4'); // 'l' للوضع الأفقي (landscape)

        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 295; // عرض A4 أفقي بالمليمتر
        const pageHeight = 210; // ارتفاع A4 أفقي بالمليمتر
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        // إضافة الصفحة الأولى
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        const filename = `تقرير_الأنسولين_المحسن_${new Date().toISOString().slice(0,10)}.pdf`;
        pdf.save(filename);

        // إظهار العناصر مرة أخرى
        noprint.forEach(function(element) {
            element.style.display = '';
        });

        console.log('تم تصدير PDF بنجاح');
    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير PDF. يرجى المحاولة مرة أخرى.');

        // إظهار العناصر مرة أخرى في حالة الخطأ
        var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
        noprint.forEach(function(element) {
            element.style.display = '';
        });
    }
}
</script>

<!-- Scripts للتصدير -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}
