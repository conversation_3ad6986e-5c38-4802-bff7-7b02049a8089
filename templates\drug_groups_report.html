{% extends "base.html" %}

{% block title %}تقرير المجموعات الدوائية - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); min-height: 100vh;">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-light">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="exportToPDF()" title="تصدير إلى PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>تصدير PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-12">
            <!-- الشعار -->
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo" style="max-width: 150px; height: auto;">
            </div>

            <!-- Header Card -->
            <div class="card shadow-lg mb-4" style="border: none; border-radius: 20px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
                <div class="card-header text-white text-center py-4" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); border-radius: 20px 20px 0 0; border: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <i class="mdi mdi-package-variant" style="font-size: 3rem; margin-bottom: 10px;"></i>
                            <h2 class="mb-0 fw-bold">تقرير المجموعات الدوائية</h2>
                            <p class="mb-0 opacity-75">تحليل شامل للمجموعات الدوائية والتكاليف</p>
                        </div>
                        <div class="no-print">
                            <span class="badge bg-light text-dark">
                                <i class="mdi mdi-calendar-range me-1"></i>{{ date_range_text }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="card border-0 bg-light rounded-3 p-3">
                                <h5 class="text-primary mb-3">
                                    <i class="mdi mdi-information-outline me-2"></i>معلومات التقرير
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-2"><i class="mdi mdi-calendar text-success me-2"></i><strong>تاريخ التقرير:</strong> {{ now.strftime('%Y-%m-%d %H:%M') }}</p>
                                        <p class="mb-2"><i class="mdi mdi-calendar-range text-info me-2"></i><strong>الشهر:</strong> {{ month }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-2"><i class="mdi mdi-map-marker text-warning me-2"></i><strong>النطاق:</strong> {{ scope_name if scope_name else scope_type }}</p>
                                        <p class="mb-0"><i class="mdi mdi-package-variant text-danger me-2"></i><strong>عدد المجموعات:</strong> {{ total_groups }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card border-0 bg-gradient text-white rounded-3 p-3" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;">
                                <h6 class="text-white mb-2" style="opacity: 0.9;">الملخص المالي</h6>
                                <h4 class="mb-1 text-white fw-bold" style="color: white !important;">{{ "{:,.2f}".format(total_cost) }} ج.م</h4>
                                <small class="text-white" style="opacity: 0.8; color: white !important;">إجمالي التكلفة</small>
                                <hr class="my-2" style="border-color: rgba(255,255,255,0.3);">
                                <div class="d-flex justify-content-between text-white" style="color: white !important;">
                                    <span>المجموعات:</span>
                                    <span class="fw-bold">{{ total_groups }}</span>
                                </div>
                                <div class="d-flex justify-content-between text-white" style="color: white !important;">
                                    <span>متوسط التكلفة:</span>
                                    <span class="fw-bold">{{ "{:,.2f}".format(total_cost / total_groups if total_groups > 0 else 0) }} ج.م</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="card-body text-white text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-package-variant" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0">{{ total_groups }}</h3>
                                            <p class="mb-0">مجموعة دوائية</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                <div class="card-body text-white text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-cash-multiple" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0">{{ "{:,.2f}".format(total_cost) }}</h3>
                                            <p class="mb-0">إجمالي التكلفة</p>
                                            <small class="text-white-75">جنيه</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="card-body text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-calculator text-primary" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h3 class="mb-0 text-dark">{{ "{:,.2f}".format(total_cost / total_groups if total_groups > 0 else 0) }}</h3>
                                            <p class="mb-0 text-muted">متوسط التكلفة</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                                <div class="card-body text-white text-center">
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div class="me-3">
                                            <i class="mdi mdi-calendar-range" style="font-size: 2.5rem;"></i>
                                        </div>
                                        <div>
                                            <h4 class="mb-0">{{ month }}</h4>
                                            <p class="mb-0">الشهر</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المجموعات الدوائية -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="mdi mdi-format-list-bulleted me-2"></i>تفاصيل المجموعات الدوائية
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if groups_data %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white;">
                                        <tr>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                                <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                                <span>#</span>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                                <i class="mdi mdi-package-variant me-2" style="color: #f39c12;"></i>
                                                <span>اسم المجموعة الدوائية</span>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px;">
                                                <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                                <span>التكلفة الإجمالية</span><br>
                                                <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 150px;">
                                                <i class="mdi mdi-hospital-building me-2" style="color: #2ecc71;"></i>
                                                <span>العيادة</span>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 120px;">
                                                <i class="mdi mdi-map-marker me-2" style="color: #e74c3c;"></i>
                                                <span>المنطقة</span>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #17a2b8; min-width: 120px;">
                                                <i class="mdi mdi-office-building me-2" style="color: #fd7e14;"></i>
                                                <span>الفرع</span>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #ffc107; min-width: 120px;">
                                                <i class="mdi mdi-calendar me-2" style="color: #28a745;"></i>
                                                <span>شهر الصرف</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for group in groups_data %}
                                        <tr>
                                            <td class="text-center">{{ loop.index }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="mdi mdi-package-variant text-primary me-2"></i>
                                                    <strong>{{ group.group_name }}</strong>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-success" style="font-size: 0.9rem; padding: 8px 12px;">
                                                    {{ "{:,.2f}".format(group.cost) }} ج.م
                                                </span>
                                            </td>
                                            <td>
                                                <i class="mdi mdi-hospital-building text-info me-1"></i>
                                                {{ group.clinic_name }}
                                            </td>
                                            <td>
                                                <i class="mdi mdi-map-marker text-warning me-1"></i>
                                                {{ group.area_name }}
                                            </td>
                                            <td>
                                                <i class="mdi mdi-office-building text-secondary me-1"></i>
                                                {{ group.branch_name }}
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-info">{{ group.formatted_date or format_month_year(group.dispense_month) }}</span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                        <tr>
                                            <th colspan="2" class="text-start" style="border: none; font-weight: bold; padding: 15px;">
                                                <i class="mdi mdi-sigma me-1"></i>الإجمالي العام
                                            </th>
                                            <th class="text-center" style="border: none; font-weight: bold; color: #28a745; padding: 15px;">
                                                {{ "{:,.2f}".format(total_cost) }} ج.م
                                            </th>
                                            <th colspan="4" class="text-center" style="border: none; font-weight: bold; color: #6c757d; padding: 15px;">
                                                {{ total_groups }} مجموعة دوائية
                                            </th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>لا توجد مجموعات دوائية في الفترة المحددة.</strong>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    {% if groups_data %}
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">متوسط التكلفة</h6>
                                    <h4 class="text-primary">{{ "{:,.2f}".format(total_cost / total_groups) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">أعلى تكلفة</h6>
                                    <h4 class="text-success">{{ "{:,.2f}".format(groups_data[0].cost) }} ج.م</h4>
                                    <small class="text-muted">{{ groups_data[0].group_name }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مربع حوار خيارات الطباعة -->
<div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">اتجاه الصفحة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                        <label class="form-check-label" for="orientationLandscape">أفقي (Landscape)</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                        <label class="form-check-label" for="orientationPortrait">رأسي (Portrait)</label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">حجم الخط:</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
            </div>
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    var printAction = 'print';
    var printModal;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل صفحة تقرير المجموعات الدوائية بنجاح');
    });

    function showPrintOptions(action) {
        printAction = action;
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }
        printModal.show();
    }

    function performPrint() {
        try {
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; }';

            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            document.head.appendChild(styleElement);
            window.print();

            setTimeout(function() {
                document.head.removeChild(styleElement);
            }, 1000);
        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
        }
    }

    // وظيفة تصدير البيانات إلى Excel مع تنسيق متقدم
    async function exportToExcel() {
        try {
            // إنشاء مصنف جديد
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('تقرير المجموعات الدوائية');

            let currentRow = 1;

            // إضافة شعار الشركة (نص بدلاً من صورة)
            const logoCell = worksheet.getCell('A' + currentRow);
            logoCell.value = 'الهيئة العامة للتأمين الصحي';
            logoCell.font = { bold: true, size: 16, color: { argb: 'FF1F4E79' } };
            logoCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
            currentRow += 2;

            // إضافة عنوان التقرير
            const titleCell = worksheet.getCell('A' + currentRow);
            titleCell.value = 'تقرير المجموعات الدوائية';
            titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
            titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF667EEA' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
            currentRow += 2;

            // إضافة معلومات التقرير (مبسطة)
            const reportDate = '{{ month }}' || new Date().toISOString().slice(0, 7);
            const formattedDate = reportDate.split('-').reverse().join('/'); // تحويل من YYYY-MM إلى MM/YYYY

            const reportInfo = [
                'الشهر: ' + formattedDate,
                'تاريخ التقرير: ' + new Date().toLocaleDateString('ar-EG')
            ];

            reportInfo.forEach(info => {
                const infoCell = worksheet.getCell('A' + currentRow);
                infoCell.value = info;
                infoCell.font = { bold: false, size: 12 };
                infoCell.alignment = { horizontal: 'right' };
                worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
                currentRow++;
            });
            currentRow++;

            // إضافة الإحصائيات
            const statsCell = worksheet.getCell('A' + currentRow);
            statsCell.value = 'الإحصائيات العامة';
            statsCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
            statsCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
            statsCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
            currentRow++;

            // الحصول على الإحصائيات من البيانات المرسلة من الخادم
            const totalGroups = '{{ total_groups }}';
            const totalCost = '{{ "{:,.2f}".format(total_cost) }}' + ' ج.م';
            const avgCost = '{{ "{:,.2f}".format(total_cost / total_groups if total_groups > 0 else 0) }}' + ' ج.م';

            const stats = [
                ['عدد المجموعات:', totalGroups],
                ['إجمالي التكلفة:', totalCost],
                ['متوسط التكلفة:', avgCost]
            ];

            stats.forEach(stat => {
                const labelCell = worksheet.getCell('A' + currentRow);
                const valueCell = worksheet.getCell('B' + currentRow);
                labelCell.value = stat[0];
                valueCell.value = stat[1];
                labelCell.font = { bold: true };
                valueCell.font = { bold: false };
                currentRow++;
            });
            currentRow++;

            // إضافة عناوين الجدول
            const table = document.querySelector('.table');
            if (table) {
                const headers = [];
                table.querySelectorAll('thead th').forEach(function(th) {
                    headers.push(th.innerText.replace(/\n/g, ' ').trim());
                });

                // إضافة عناوين الأعمدة
                headers.forEach((header, index) => {
                    const headerCell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    headerCell.value = header;
                    headerCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    headerCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4472C4' } };
                    headerCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    headerCell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
                currentRow++;

                // إضافة بيانات الجدول
                table.querySelectorAll('tbody tr').forEach(function(tr) {
                    const row = [];
                    tr.querySelectorAll('td').forEach(function(td) {
                        row.push(td.innerText.replace(/\n/g, ' ').trim());
                    });

                    row.forEach((cellValue, index) => {
                        const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                        cell.value = cellValue;
                        cell.alignment = { horizontal: 'center', vertical: 'middle' };
                        cell.border = {
                            top: { style: 'thin' },
                            left: { style: 'thin' },
                            bottom: { style: 'thin' },
                            right: { style: 'thin' }
                        };
                    });
                    currentRow++;
                });

                // إضافة صف الإجمالي
                const footerRow = [];
                table.querySelectorAll('tfoot tr').forEach(function(tr) {
                    tr.querySelectorAll('th, td').forEach(function(cell) {
                        footerRow.push(cell.innerText.replace(/\n/g, ' ').trim());
                    });
                });

                if (footerRow.length > 0) {
                    footerRow.forEach((cellValue, index) => {
                        const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                        cell.value = cellValue;
                        cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                        cell.alignment = { horizontal: 'center', vertical: 'middle' };
                        cell.border = {
                            top: { style: 'thin' },
                            left: { style: 'thin' },
                            bottom: { style: 'thin' },
                            right: { style: 'thin' }
                        };
                    });
                }
            }

            // تحديد عرض الأعمدة
            worksheet.columns = [
                { width: 8 },   // #
                { width: 30 },  // اسم المجموعة
                { width: 15 },  // عدد الأدوية
                { width: 18 },  // إجمالي التكلفة
                { width: 15 },  // متوسط التكلفة
                { width: 12 }   // النسبة المئوية
            ];

            // تصدير الملف
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const fileName = "تقرير_المجموعات_الدوائية_منسق_" + new Date().toISOString().slice(0, 10) + ".xlsx";
            saveAs(blob, fileName);

            console.log('تم تصدير Excel بنجاح');
        } catch (error) {
            console.error("Error exporting to Excel:", error);
            alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
        }
    }

    // وظيفة تصدير إلى PDF
    async function exportToPDF() {
        try {
            // إخفاء العناصر غير المطلوبة
            var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
            noprint.forEach(function(element) {
                element.style.display = 'none';
            });

            // الحصول على المحتوى المراد تحويله
            var element = document.querySelector('.container-fluid');

            // تحويل إلى صورة
            const canvas = await html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: element.scrollWidth,
                height: element.scrollHeight,
                scrollX: 0,
                scrollY: 0
            });

            // إنشاء PDF بالوضع الأفقي للجداول العريضة
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('l', 'mm', 'a4'); // 'l' للوضع الأفقي (landscape)

            const imgData = canvas.toDataURL('image/png');
            const imgWidth = 295; // عرض A4 أفقي بالمليمتر
            const pageHeight = 210; // ارتفاع A4 أفقي بالمليمتر
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            // إضافة الصفحة الأولى
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // إضافة صفحات إضافية إذا لزم الأمر
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // حفظ الملف
            const filename = `تقرير_المجموعات_الدوائية_${new Date().toISOString().slice(0,10)}.pdf`;
            pdf.save(filename);

            // إظهار العناصر مرة أخرى
            noprint.forEach(function(element) {
                element.style.display = '';
            });

            console.log('تم تصدير PDF بنجاح');
        } catch (error) {
            console.error('خطأ في تصدير PDF:', error);
            alert('حدث خطأ أثناء تصدير PDF. يرجى المحاولة مرة أخرى.');

            // إظهار العناصر مرة أخرى في حالة الخطأ
            var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
            noprint.forEach(function(element) {
                element.style.display = '';
            });
        }
    }
</script>

<style>
    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    @media print {
        /* إخفاء العناصر غير المطلوبة */
        .btn, .no-print, button, .action-buttons {
            display: none !important;
        }

        /* تنسيق الصفحة */
        body {
            margin: 0;
            padding: 0;
            background: white !important;
            font-family: 'Tajawal', sans-serif;
        }

        .container-fluid {
            padding: 15px !important;
            background: white !important;
            max-width: 100% !important;
        }

        /* تنسيق الشعار */
        .report-logo {
            display: block !important;
            max-width: 120px !important;
            height: auto !important;
            margin: 0 auto 20px !important;
        }

        .text-center.mb-4 {
            page-break-after: avoid !important;
            margin-bottom: 20px !important;
        }

        /* تنسيق الكارتات */
        .card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
            break-inside: avoid;
            margin-bottom: 15px !important;
            background: white !important;
        }

        .card-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
            color: white !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            padding: 15px !important;
        }

        .card-body {
            padding: 15px !important;
        }

        /* تنسيق الكارتات الملونة */
        .card[style*="background"] {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        /* تنسيق الجدول */
        .table {
            font-size: 11px !important;
            border-collapse: collapse !important;
            width: 100% !important;
        }

        .table th,
        .table td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
            text-align: center !important;
        }

        .table thead th {
            background-color: #4472C4 !important;
            color: white !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        .table tfoot th,
        .table tfoot td {
            background-color: #28a745 !important;
            color: white !important;
            font-weight: bold !important;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        /* تنسيق الألوان للطباعة */
        * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }

        /* تنسيق النصوص */
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50 !important;
            margin-bottom: 10px !important;
        }

        /* تنسيق الإحصائيات */
        .row .col-md-3 .card,
        .row .col-md-4 .card {
            margin-bottom: 10px !important;
            min-height: auto !important;
        }

        /* منع كسر الصفحة داخل العناصر المهمة */
        .card,
        .table,
        .row {
            page-break-inside: avoid !important;
        }

        /* تحسين المساحات */
        .mb-4 {
            margin-bottom: 15px !important;
        }

        .py-4 {
            padding-top: 15px !important;
            padding-bottom: 15px !important;
        }
    }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        body::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
    }
</style>
{% endblock %}
