{% extends 'base.html' %}

{% block title %}تقرير المقارنة التفصيلي{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-danger" onclick="exportToPDF()" title="تصدير إلى PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>تصدير PDF
                    </button>
                    <button class="btn btn-primary" onclick="window.print()" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-compare-horizontal me-2"></i>تقرير المقارنة التفصيلي
                </h4>
                <div class="no-print">
                    <span class="badge bg-light text-dark">
                        <i class="mdi mdi-calendar-range me-1"></i>مقارنة تفصيلية
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="report-header">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                <h2><i class="mdi mdi-file-document-outline me-2"></i>تقرير المقارنة التفصيلي</h2>
                <div class="report-info">
                    <div class="info-row period-highlight">
                        <span class="info-label"><i class="mdi mdi-calendar-range me-2"></i>الفترة الزمنية:</span>
                        <span class="info-value period-value">{{ period_desc }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label"><i class="mdi mdi-map-marker me-2"></i>نوع المقارنة:</span>
                        <span class="info-value">{{ comparison_type_text }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label"><i class="mdi mdi-office-building me-2"></i>المواقع المختارة:</span>
                        <span class="info-value">{{ selected_locations_names|join(', ') }}</span>
                    </div>
                    {% if category_name != 'جميع التصنيفات' %}
                    <div class="info-row">
                        <span class="info-label"><i class="mdi mdi-tag me-2"></i>التصنيف:</span>
                        <span class="info-value">{{ category_name }}</span>
                    </div>
                    {% endif %}
                    {% if drugs_names %}
                    <div class="info-row">
                        <span class="info-label"><i class="mdi mdi-pill me-2"></i>الأدوية المختارة:</span>
                        <span class="info-value">{{ drugs_names|join(', ') }}</span>
                    </div>
                    {% endif %}
                    <div class="info-row">
                        <span class="info-label"><i class="mdi mdi-clock me-2"></i>تاريخ إنشاء التقرير:</span>
                        <span class="info-value">{{ now.strftime('%d/%m/%Y - %H:%M') }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label"><i class="mdi mdi-chart-line me-2"></i>عدد المواقع المقارنة:</span>
                        <span class="info-value">{{ selected_locations_names|length }} موقع</span>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات السريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-calculator" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">متوسط التكلفة</h5>
                            <h3>{{ "{:,.2f}".format(avg_cost) }} ج.م</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-account-multiple" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">إجمالي عدد الحالات</h5>
                            <h3>{{ "{:,}".format(total_cases) }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-package-variant" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">إجمالي الكمية</h5>
                            <h3>{{ "{:,}".format(total_quantity) }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="mdi mdi-cash-multiple" style="font-size: 2rem;"></i>
                            <h5 class="mt-2">إجمالي التكلفة</h5>
                            <h3>{{ "{:,.2f}".format(total_cost) }} ج.م</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسم البياني -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="mdi mdi-chart-bar me-2"></i>الرسم البياني للمقارنة
                        </h5>
                        <div class="btn-group no-print" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" onclick="changeChartType('bar')" id="barChartBtn">
                                <i class="mdi mdi-chart-bar me-1"></i>أعمدة
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="changeChartType('pie')" id="pieChartBtn">
                                <i class="mdi mdi-chart-pie me-1"></i>دائري
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="changeChartType('line')" id="lineChartBtn">
                                <i class="mdi mdi-chart-line me-1"></i>خطي
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height: 400px;">
                        <canvas id="comparisonChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- جدول المقارنة التفصيلي -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="mdi mdi-table me-2"></i>تفاصيل المقارنة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
                                <tr>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 200px;">
                                        <i class="mdi mdi-map-marker me-2" style="color: #f1c40f;"></i>
                                        <span>{{ comparison_type_text }}</span>
                                    </th>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                        <i class="mdi mdi-pill me-2" style="color: #f39c12;"></i>
                                        <span>اسم الدواء</span>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                        <i class="mdi mdi-account-multiple me-2" style="color: #2ecc71;"></i>
                                        <span>عدد الحالات</span>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #9b59b6; min-width: 120px;">
                                        <i class="mdi mdi-package-variant me-2" style="color: #e74c3c;"></i>
                                        <span>الكمية</span>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 150px;">
                                        <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                        <span>التكلفة الإجمالية</span>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 120px;">
                                        <i class="mdi mdi-calculator me-2" style="color: #3498db;"></i>
                                        <span>متوسط السعر</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for location in locations_data %}
                                    {% for drug in location.drugs %}
                                    <tr>
                                        {% if loop.first %}
                                        <td rowspan="{{ location.drugs|length }}" class="align-middle" style="background-color: #f8f9fa; font-weight: bold;">
                                            <div class="d-flex align-items-center">
                                                <i class="mdi mdi-office-building text-primary me-2"></i>
                                                <span>{{ location.name }}</span>
                                            </div>
                                        </td>
                                        {% endif %}
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if drug.type == 'أنسولين' %}
                                                <i class="mdi mdi-needle text-info me-2"></i>
                                                {% else %}
                                                <i class="mdi mdi-pill text-success me-2"></i>
                                                {% endif %}
                                                <span>{{ drug.name }}</span>
                                                <span class="badge bg-secondary ms-2">{{ drug.type }}</span>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info">{{ drug.cases_count }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary">{{ "{:,}".format(drug.total_quantity) }}</span>
                                        </td>
                                        <td class="text-center">
                                            <strong>{{ "{:,.2f}".format(drug.total_cost) }} ج.م</strong>
                                        </td>
                                        <td class="text-center">
                                            <span class="text-muted">{{ "{:,.2f}".format(drug.avg_price) }} ج.م</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                    <!-- صف إجمالي لكل موقع -->
                                    <tr class="table-warning">
                                        <td class="text-center"><strong>إجمالي {{ location.name }}</strong></td>
                                        <td class="text-center"><strong>{{ location.drugs|length }} دواء</strong></td>
                                        <td class="text-center"><strong>{{ location.total_cases }}</strong></td>
                                        <td class="text-center"><strong>{{ "{:,}".format(location.total_quantity) }}</strong></td>
                                        <td class="text-center"><strong>{{ "{:,.2f}".format(location.total_cost) }} ج.م</strong></td>
                                        <td class="text-center"><strong>{{ "{:,.2f}".format(location.total_cost / location.total_cases if location.total_cases > 0 else 0) }} ج.م</strong></td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-success">
                                    <th colspan="2" class="text-start">
                                        <i class="mdi mdi-calculator me-2"></i>الإجمالي العام
                                    </th>
                                    <th class="text-center">{{ total_cases }}</th>
                                    <th class="text-center">{{ "{:,}".format(total_quantity) }}</th>
                                    <th class="text-center">{{ "{:,.2f}".format(total_cost) }} ج.م</th>
                                    <th class="text-center">{{ "{:,.2f}".format(avg_cost) }} ج.م</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary no-print">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .report-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 20px;
        border-bottom: 3px solid #28a745;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px 10px 0 0;
    }

    .report-header img {
        max-width: 120px;
        margin-bottom: 15px;
    }

    .report-header h2 {
        color: #28a745;
        font-weight: bold;
        margin-bottom: 20px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .report-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        max-width: 800px;
        margin: 0 auto;
        text-align: right;
    }

    .info-row {
        display: flex;
        align-items: center;
        background: white;
        padding: 10px 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-right: 4px solid #28a745;
    }

    .info-label {
        font-weight: bold;
        color: #495057;
        min-width: 120px;
        margin-left: 10px;
    }

    .info-value {
        color: #28a745;
        font-weight: 600;
        flex: 1;
    }

    .period-highlight {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
        border-right: 4px solid #2196f3 !important;
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2) !important;
    }

    .period-value {
        color: #1976d2 !important;
        font-weight: 700 !important;
        font-size: 1.1em;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    @media (max-width: 768px) {
        .report-info {
            grid-template-columns: 1fr;
        }

        .info-row {
            flex-direction: column;
            text-align: center;
        }

        .info-label {
            margin-left: 0;
            margin-bottom: 5px;
        }
    }

    .table th {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        font-weight: bold;
        text-align: center;
        vertical-align: middle;
    }

    .table td {
        vertical-align: middle;
    }

    .chart-container {
        position: relative;
        height: 400px;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        .chart-container {
            page-break-inside: avoid;
            margin: 20px 0;
        }

        #comparisonChart {
            max-width: 100% !important;
            height: auto !important;
        }

        .btn-group {
            display: none !important;
        }

        .table {
            font-size: 10pt;
        }

        .table th, .table td {
            padding: 8px !important;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- ExcelJS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>

<script>
let comparisonChart;

document.addEventListener('DOMContentLoaded', function() {
    // إنشاء الرسم البياني الأولي
    createChart('bar');
});

// دالة إنشاء الرسم البياني
function createChart(type) {
    const ctx = document.getElementById('comparisonChart').getContext('2d');

    // تدمير الرسم البياني السابق إذا كان موجوداً
    if (comparisonChart) {
        comparisonChart.destroy();
    }

    // بيانات المواقع والتكلفة
    const chartLabels = [
        {% for location in locations_data %}
            '{{ location.name }}'{% if not loop.last %},{% endif %}
        {% endfor %}
    ];

    const chartValues = [
        {% for location in locations_data %}
            {{ location.total_cost }}{% if not loop.last %},{% endif %}
        {% endfor %}
    ];

    const backgroundColors = [
        'rgba(54, 162, 235, 0.8)',
        'rgba(255, 99, 132, 0.8)',
        'rgba(255, 205, 86, 0.8)',
        'rgba(75, 192, 192, 0.8)',
        'rgba(153, 102, 255, 0.8)',
        'rgba(255, 159, 64, 0.8)',
        'rgba(199, 199, 199, 0.8)',
        'rgba(83, 102, 255, 0.8)',
        'rgba(255, 99, 255, 0.8)',
        'rgba(99, 255, 132, 0.8)'
    ];

    const borderColors = [
        'rgba(54, 162, 235, 1)',
        'rgba(255, 99, 132, 1)',
        'rgba(255, 205, 86, 1)',
        'rgba(75, 192, 192, 1)',
        'rgba(153, 102, 255, 1)',
        'rgba(255, 159, 64, 1)',
        'rgba(199, 199, 199, 1)',
        'rgba(83, 102, 255, 1)',
        'rgba(255, 99, 255, 1)',
        'rgba(99, 255, 132, 1)'
    ];

    const chartData = {
        labels: chartLabels,
        datasets: [{
            label: 'إجمالي التكلفة (ج.م)',
            data: chartValues,
            backgroundColor: type === 'pie' ? backgroundColors : 'rgba(54, 162, 235, 0.8)',
            borderColor: type === 'pie' ? borderColors : 'rgba(54, 162, 235, 1)',
            borderWidth: 2,
            fill: type === 'line' ? false : true
        }]
    };

    // إعدادات مختلفة حسب نوع الرسم البياني
    let options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: 'مقارنة التكلفة بين {{ comparison_type_text }}',
                font: {
                    size: 16,
                    weight: 'bold'
                }
            },
            legend: {
                display: type === 'pie',
                position: 'right'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const value = context.parsed.y || context.parsed;
                        const total = {{ total_cost }};
                        const percentage = ((value / total) * 100).toFixed(1);
                        return `التكلفة: ${value.toLocaleString()} ج.م (${percentage}%)`;
                    }
                }
            }
        }
    };

    // إضافة إعدادات المحاور للرسوم البيانية غير الدائرية
    if (type !== 'pie') {
        options.scales = {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'التكلفة (ج.م)'
                },
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ج.م';
                    }
                }
            },
            x: {
                title: {
                    display: true,
                    text: '{{ comparison_type_text }}'
                },
                ticks: {
                    maxRotation: 45,
                    minRotation: 0
                }
            }
        };
    }

    const config = {
        type: type,
        data: chartData,
        options: options
    };

    comparisonChart = new Chart(ctx, config);
}

// دالة تغيير نوع الرسم البياني
function changeChartType(type) {
    // تحديث الأزرار
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(type + 'ChartBtn').classList.add('active');

    // إنشاء رسم بياني جديد
    createChart(type);
}

// دالة تصدير PDF باستخدام jsPDF + html2canvas
function exportToPDF() {
    // إخفاء العناصر غير المطلوبة
    var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
    noprint.forEach(function(element) {
        element.style.display = 'none';
    });

    // الحصول على المحتوى المراد تحويله
    var element = document.querySelector('.container-fluid');

    // تحويل إلى صورة
    html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
    }).then(function(canvas) {
        // إنشاء PDF
        const { jsPDF } = window.jspdf;
        var pdf = new jsPDF('p', 'mm', 'a4');

        var imgData = canvas.toDataURL('image/png');
        var imgWidth = 210;
        var pageHeight = 295;
        var imgHeight = (canvas.height * imgWidth) / canvas.width;
        var heightLeft = imgHeight;
        var position = 0;

        // إضافة الصفحة الأولى
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        var now = new Date();
        var dateStr = now.getFullYear() + '-' +
                     String(now.getMonth() + 1).padStart(2, '0') + '-' +
                     String(now.getDate()).padStart(2, '0');
        var filename = 'تقرير_المقارنة_التفصيلي_' + dateStr + '.pdf';

        pdf.save(filename);

        // إظهار العناصر مرة أخرى
        noprint.forEach(function(element) {
            element.style.display = '';
        });

        console.log('تم تصدير PDF بنجاح');
    }).catch(function(error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير PDF. يرجى المحاولة مرة أخرى.');

        // إظهار العناصر مرة أخرى في حالة الخطأ
        var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
        noprint.forEach(function(element) {
            element.style.display = '';
        });
    });
}

// دالة تصدير Excel
async function exportToExcel() {
    try {
        // إنشاء مصنف جديد
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('تقرير المقارنة التفصيلي');

        let currentRow = 1;

        // إضافة العنوان
        const titleCell = worksheet.getCell('A' + currentRow);
        titleCell.value = 'تقرير المقارنة التفصيلي';
        titleCell.font = { bold: true, size: 18, color: { argb: 'FFFFFFFF' } };
        titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28a745' } };
        titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
        currentRow += 2;

        // إضافة معلومات التقرير مع تنسيق محسن
        const infoData = [
            ['الفترة الزمنية:', '{{ period_desc }}'],
            ['نوع المقارنة:', '{{ comparison_type_text }}'],
            ['المواقع المختارة:', '{{ selected_locations_names|join(", ") }}'],
            {% if category_name != 'جميع التصنيفات' %}
            ['التصنيف المختار:', '{{ category_name }}'],
            {% endif %}
            {% if drugs_names %}
            ['الأدوية المختارة:', '{{ drugs_names|join(", ") }}'],
            {% endif %}
            ['تاريخ إنشاء التقرير:', new Date().toLocaleDateString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            })]
        ];

        infoData.forEach(info => {
            worksheet.getCell('A' + currentRow).value = info[0];
            worksheet.getCell('A' + currentRow).font = { bold: true };
            worksheet.getCell('B' + currentRow).value = info[1];
            currentRow++;
        });
        currentRow++;

        // إضافة الإحصائيات السريعة
        const statsCell = worksheet.getCell('A' + currentRow);
        statsCell.value = 'الإحصائيات السريعة';
        statsCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
        statsCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF007BFF' } };
        statsCell.alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
        currentRow++;

        const statsData = [
            ['متوسط التكلفة', '{{ "{:,.2f}".format(avg_cost) }} ج.م'],
            ['إجمالي عدد الحالات', '{{ "{:,}".format(total_cases) }}'],
            ['إجمالي الكمية', '{{ "{:,}".format(total_quantity) }}'],
            ['إجمالي التكلفة', '{{ "{:,.2f}".format(total_cost) }} ج.م']
        ];

        statsData.forEach(stat => {
            worksheet.getCell('A' + currentRow).value = stat[0];
            worksheet.getCell('A' + currentRow).font = { bold: true };
            worksheet.getCell('B' + currentRow).value = stat[1];
            currentRow++;
        });
        currentRow += 2;

        // إضافة الرسم البياني كصورة
        try {
            const chartCanvas = document.getElementById('comparisonChart');
            if (chartCanvas) {
                const chartImageData = chartCanvas.toDataURL('image/png');
                const chartImageId = workbook.addImage({
                    base64: chartImageData,
                    extension: 'png',
                });

                const chartTitleCell = worksheet.getCell('A' + currentRow);
                chartTitleCell.value = 'الرسم البياني';
                chartTitleCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
                chartTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF007BFF' } };
                chartTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
                worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
                currentRow++;

                worksheet.addImage(chartImageId, {
                    tl: { col: 0, row: currentRow - 1 },
                    ext: { width: 600, height: 400 }
                });
                currentRow += 20;
            }
        } catch (chartError) {
            console.warn('لم يتم إضافة الرسم البياني:', chartError);
        }

        // إضافة عنوان الجدول
        const tableHeaderCell = worksheet.getCell('A' + currentRow);
        tableHeaderCell.value = 'تفاصيل المقارنة';
        tableHeaderCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
        tableHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28a745' } };
        tableHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
        worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
        currentRow++;

        // إضافة رؤوس الأعمدة
        const headers = ['{{ comparison_type_text }}', 'اسم الدواء', 'عدد الحالات', 'الكمية', 'التكلفة الإجمالية', 'متوسط السعر'];
        headers.forEach((header, index) => {
            const cell = worksheet.getCell(currentRow, index + 1);
            cell.value = header;
            cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF20c997' } };
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });
        currentRow++;

        // إضافة البيانات
        {% for location in locations_data %}
            {% for drug in location.drugs %}
            const row{{ loop.index0 }} = [
                '{{ location.name }}',
                '{{ drug.name }}',
                {{ drug.cases_count }},
                {{ drug.total_quantity }},
                {{ drug.total_cost }},
                {{ drug.avg_price }}
            ];

            row{{ loop.index0 }}.forEach((value, index) => {
                worksheet.getCell(currentRow, index + 1).value = value;
            });
            currentRow++;
            {% endfor %}

            // إضافة صف الإجمالي لكل موقع
            const totalRow{{ loop.index0 }} = [
                'إجمالي {{ location.name }}',
                '{{ location.drugs|length }} دواء',
                {{ location.total_cases }},
                {{ location.total_quantity }},
                {{ location.total_cost }},
                {{ location.total_cost / location.total_cases if location.total_cases > 0 else 0 }}
            ];

            totalRow{{ loop.index0 }}.forEach((value, index) => {
                const cell = worksheet.getCell(currentRow, index + 1);
                cell.value = value;
                cell.font = { bold: true };
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFF2CC' } };
            });
            currentRow++;
        {% endfor %}

        // إضافة الإجمالي العام
        const grandTotalRow = [
            'الإجمالي العام',
            '',
            {{ total_cases }},
            {{ total_quantity }},
            {{ total_cost }},
            {{ avg_cost }}
        ];

        grandTotalRow.forEach((value, index) => {
            const cell = worksheet.getCell(currentRow, index + 1);
            cell.value = value;
            cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28a745' } };
        });
        currentRow += 2;

        // إضافة تاريخ التقرير
        const dateCell = worksheet.getCell('A' + currentRow);
        dateCell.value = 'تاريخ إنشاء التقرير: ' + new Date().toLocaleDateString('ar-EG');
        dateCell.font = { italic: true, size: 10 };
        dateCell.alignment = { horizontal: 'right' };

        // تنسيق عرض الأعمدة
        worksheet.columns.forEach(column => {
            column.width = 20;
        });

        // حفظ الملف
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        const now = new Date();
        const dateStr = now.getFullYear() + '-' +
                       String(now.getMonth() + 1).padStart(2, '0') + '-' +
                       String(now.getDate()).padStart(2, '0');
        const filename = 'تقرير_المقارنة_التفصيلي_' + dateStr + '.xlsx';

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();

        console.log('تم تصدير Excel بنجاح');
    } catch (error) {
        console.error("Error exporting to Excel:", error);
        alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
    }
}
</script>
{% endblock %}
