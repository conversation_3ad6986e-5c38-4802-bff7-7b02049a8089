// Utility function for formatting dates in English format (DD/MM/YYYY)
function formatDateEnglish(date) {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
}

// Utility function for formatting month and year (MM/YYYY)
function formatMonthYearEnglish(date) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${year}`;
}

// Utility function for formatting dates for input[type="month"] (YYYY-MM)
function formatMonthInput(date) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${year}-${month}`;
}

// Utility function for converting YYYY-MM-DD to MM/YYYY format
function formatMonthYear(dateStr) {
    if (!dateStr) return '';
    try {
        if (typeof dateStr === 'string' && dateStr.length >= 7) {
            const year = dateStr.substring(0, 4);
            const month = dateStr.substring(5, 7);
            return `${month}/${year}`;
        }
        return dateStr;
    } catch (e) {
        return dateStr;
    }
}
