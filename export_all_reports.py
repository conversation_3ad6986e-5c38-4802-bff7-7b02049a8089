@app.route('/reports/export_all_excel')
def export_all_reports_excel():
    """تصدير جميع التقارير في ملف إكسل واحد مع صفحات منفصلة"""
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        from datetime import datetime
        import io
        
        # إنشاء ملف إكسل جديد
        wb = Workbook()
        
        # حذف الصفحة الافتراضية
        wb.remove(wb.active)
        
        conn = get_db_connection()
        
        # تنسيق العناوين
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        center_alignment = Alignment(horizontal="center", vertical="center")
        
        # 1. تقرير الأدوية
        try:
            ws_drugs = wb.create_sheet("تقرير الأدوية")
            drugs_data = conn.execute('''
                SELECT 
                    d.id as "رقم السجل",
                    c.name as "العيادة",
                    a.name as "المنطقة", 
                    b.name as "الفرع",
                    dr.name as "اسم الدواء",
                    dc.name as "التصنيف",
                    dd.quantity as "الكمية",
                    dd.price as "السعر",
                    dd.cases_count as "عدد الحالات",
                    (dd.quantity * dd.price) as "التكلفة الإجمالية",
                    d.dispense_month as "شهر الصرف"
                FROM dispensed d
                JOIN clinics c ON d.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                JOIN drugs dr ON d.drug_id = dr.id
                JOIN drug_categories dc ON dr.category_id = dc.id
                JOIN dispensed_details dd ON d.id = dd.dispensed_id
                ORDER BY d.dispense_month DESC, b.name, a.name, c.name
            ''').fetchall()
            
            if drugs_data:
                # إضافة العناوين
                headers = ["رقم السجل", "العيادة", "المنطقة", "الفرع", "اسم الدواء", "التصنيف", "الكمية", "السعر", "عدد الحالات", "التكلفة الإجمالية", "شهر الصرف"]
                ws_drugs.append(headers)
                
                # تنسيق العناوين
                for col_num, header in enumerate(headers, 1):
                    cell = ws_drugs.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                # إضافة البيانات
                for row in drugs_data:
                    ws_drugs.append(list(row))
                
                # تعديل عرض الأعمدة
                for column in ws_drugs.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws_drugs.column_dimensions[column_letter].width = adjusted_width
        except Exception as e:
            pass
        
        # 2. تقرير الأنسولين
        try:
            ws_insulin = wb.create_sheet("تقرير الأنسولين")
            insulin_data = conn.execute('''
                SELECT 
                    i.id as "رقم السجل",
                    c.name as "العيادة",
                    a.name as "المنطقة",
                    b.name as "الفرع", 
                    i.name as "اسم الأنسولين",
                    i.type as "النوع",
                    i.unit as "الوحدة",
                    i.cases_count as "عدد الحالات",
                    i.quantity as "الكمية",
                    i.price as "السعر",
                    i.cost as "التكلفة",
                    i.category as "الفئة",
                    i.dispense_month as "شهر الصرف"
                FROM insulin_dispensed i
                JOIN clinics c ON i.clinic_id = c.id
                JOIN areas a ON i.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                ORDER BY i.dispense_month DESC, b.name, a.name, c.name
            ''').fetchall()
            
            if insulin_data:
                headers = ["رقم السجل", "العيادة", "المنطقة", "الفرع", "اسم الأنسولين", "النوع", "الوحدة", "عدد الحالات", "الكمية", "السعر", "التكلفة", "الفئة", "شهر الصرف"]
                ws_insulin.append(headers)
                
                for col_num, header in enumerate(headers, 1):
                    cell = ws_insulin.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                for row in insulin_data:
                    ws_insulin.append(list(row))
                
                for column in ws_insulin.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws_insulin.column_dimensions[column_letter].width = adjusted_width
        except Exception as e:
            pass
        
        # 3. تقرير المجموعات الدوائية
        try:
            ws_groups = wb.create_sheet("المجموعات الدوائية")
            groups_data = conn.execute('''
                SELECT 
                    dg.id as "رقم السجل",
                    c.name as "العيادة",
                    a.name as "المنطقة",
                    b.name as "الفرع",
                    dg.name as "اسم المجموعة",
                    dg.cost as "التكلفة",
                    dgc.code as "كود المجموعة",
                    dgc.description as "وصف المجموعة",
                    dg.dispense_month as "شهر الصرف"
                FROM drug_groups dg
                JOIN clinics c ON dg.clinic_id = c.id
                JOIN areas a ON dg.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                LEFT JOIN drug_group_codes dgc ON dg.group_code_id = dgc.id
                ORDER BY dg.dispense_month DESC, b.name, a.name, c.name
            ''').fetchall()
            
            if groups_data:
                headers = ["رقم السجل", "العيادة", "المنطقة", "الفرع", "اسم المجموعة", "التكلفة", "كود المجموعة", "وصف المجموعة", "شهر الصرف"]
                ws_groups.append(headers)
                
                for col_num, header in enumerate(headers, 1):
                    cell = ws_groups.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                for row in groups_data:
                    ws_groups.append(list(row))
                
                for column in ws_groups.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws_groups.column_dimensions[column_letter].width = adjusted_width
        except Exception as e:
            pass
        
        # 4. ملخص العيادات
        try:
            ws_clinics = wb.create_sheet("ملخص العيادات")
            clinics_data = conn.execute('''
                SELECT 
                    c.name as "العيادة",
                    a.name as "المنطقة",
                    b.name as "الفرع",
                    COUNT(DISTINCT d.id) as "عدد سجلات الأدوية",
                    COUNT(DISTINCT i.id) as "عدد سجلات الأنسولين",
                    COUNT(DISTINCT dg.id) as "عدد المجموعات الدوائية",
                    COALESCE(SUM(dd.quantity * dd.price), 0) as "إجمالي تكلفة الأدوية",
                    COALESCE(SUM(i.cost), 0) as "إجمالي تكلفة الأنسولين",
                    COALESCE(SUM(dg.cost), 0) as "إجمالي تكلفة المجموعات",
                    (COALESCE(SUM(dd.quantity * dd.price), 0) + COALESCE(SUM(i.cost), 0) + COALESCE(SUM(dg.cost), 0)) as "إجمالي التكلفة الكلية"
                FROM clinics c
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                LEFT JOIN dispensed d ON c.id = d.clinic_id
                LEFT JOIN dispensed_details dd ON d.id = dd.dispensed_id
                LEFT JOIN insulin_dispensed i ON c.id = i.clinic_id
                LEFT JOIN drug_groups dg ON c.id = dg.clinic_id
                GROUP BY c.id, c.name, a.name, b.name
                ORDER BY b.name, a.name, c.name
            ''').fetchall()
            
            if clinics_data:
                headers = ["العيادة", "المنطقة", "الفرع", "عدد سجلات الأدوية", "عدد سجلات الأنسولين", "عدد المجموعات الدوائية", "إجمالي تكلفة الأدوية", "إجمالي تكلفة الأنسولين", "إجمالي تكلفة المجموعات", "إجمالي التكلفة الكلية"]
                ws_clinics.append(headers)
                
                for col_num, header in enumerate(headers, 1):
                    cell = ws_clinics.cell(row=1, column=col_num)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                
                for row in clinics_data:
                    ws_clinics.append(list(row))
                
                for column in ws_clinics.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws_clinics.column_dimensions[column_letter].width = adjusted_width
        except Exception as e:
            pass
        
        conn.close()
        
        # إنشاء ملف في الذاكرة
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        # إنشاء اسم الملف مع التاريخ
        current_date = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        filename = f'تقرير_شامل_جميع_البيانات_{current_date}.xlsx'
        
        # إرجاع الملف للتحميل
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
        
    except Exception as e:
        flash(f'حدث خطأ أثناء تصدير التقارير: {str(e)}', 'danger')
        return redirect(url_for('reports'))