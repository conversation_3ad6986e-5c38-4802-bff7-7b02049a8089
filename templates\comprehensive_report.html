{% extends "base.html" %}

{% block title %}التقرير الشامل{% endblock %}

{% block styles %}
<style>
.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    color: white;
}

.report-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.stats-card {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border-radius: 10px;
    padding: 15px;
    color: white;
    text-align: center;
    margin-bottom: 15px;
}

.export-buttons {
    position: sticky;
    top: 20px;
    z-index: 1000;
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.section-title {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    font-weight: bold;
}

.hidden-element {
    display: none;
}

.report-logo {
    max-height: 60px;
    max-width: 100px;
    object-fit: contain;
}

@media print {
    .no-print, .export-buttons, .filter-card {
        display: none !important;
    }

    .report-logo {
        max-height: 50px;
        max-width: 80px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ url_for('reports') }}">التقارير</a></li>
                        <li class="breadcrumb-item active">التقرير الشامل</li>
                    </ol>
                </div>
                <h4 class="page-title">📊 التقرير الشامل</h4>
            </div>
        </div>
    </div>

    <!-- أزرار التصدير والعودة -->
    <div class="export-buttons no-print">
        <div class="row">
            <div class="col-md-3">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary btn-lg">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
            <div class="col-md-3">
                <button type="button" onclick="exportToPDF()" class="btn btn-danger btn-lg">
                    <i class="mdi mdi-file-pdf me-1"></i>تصدير PDF
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" onclick="exportToExcel()" class="btn btn-success btn-lg">
                    <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                </button>
            </div>
            <div class="col-md-3 text-end">
                <button type="button" onclick="window.print()" class="btn btn-info btn-lg">
                    <i class="mdi mdi-printer me-1"></i>طباعة
                </button>
            </div>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="filter-card no-print">
        <form method="GET" action="{{ url_for('comprehensive_report') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="date_range" class="form-label">نطاق التاريخ:</label>
                    <select class="form-select" id="date_range" name="date_range" onchange="toggleCustomDates()">
                        <option value="month" {{ 'selected' if date_range == 'month' else '' }}>الشهر الحالي</option>
                        <option value="quarter" {{ 'selected' if date_range == 'quarter' else '' }}>الربع الحالي</option>
                        <option value="quarter_1" {{ 'selected' if date_range == 'quarter_1' else '' }}>الربع الأول</option>
                        <option value="quarter_2" {{ 'selected' if date_range == 'quarter_2' else '' }}>الربع الثاني</option>
                        <option value="quarter_3" {{ 'selected' if date_range == 'quarter_3' else '' }}>الربع الثالث</option>
                        <option value="quarter_4" {{ 'selected' if date_range == 'quarter_4' else '' }}>الربع الرابع</option>
                        <option value="year" {{ 'selected' if date_range == 'year' else '' }}>السنة الحالية</option>
                        <option value="custom" {{ 'selected' if date_range == 'custom' else '' }}>فترة محددة</option>
                    </select>
                </div>

                <div class="col-md-3 mb-3 hidden-element" id="custom_dates">
                    <label for="start_date" class="form-label">من تاريخ:</label>
                    <input type="month" class="form-control" id="start_date" name="start_date" value="{{ start_date or '' }}">
                </div>

                <div class="col-md-3 mb-3 hidden-element" id="custom_dates_end">
                    <label for="end_date" class="form-label">إلى تاريخ:</label>
                    <input type="month" class="form-control" id="end_date" name="end_date" value="{{ end_date or '' }}">
                </div>

                <div class="col-md-3 mb-3">
                    <label for="scope_type" class="form-label">نطاق التقرير:</label>
                    <select class="form-select" id="scope_type" name="scope_type" onchange="toggleLocationSelectors()">
                        <option value="all" {{ 'selected' if scope_type == 'all' else '' }}>جميع المواقع</option>
                        <option value="branch" {{ 'selected' if scope_type == 'branch' else '' }}>فرع محدد</option>
                        <option value="area" {{ 'selected' if scope_type == 'area' else '' }}>منطقة محددة</option>
                        <option value="clinic" {{ 'selected' if scope_type == 'clinic' else '' }}>عيادة محددة</option>
                    </select>
                </div>
            </div>

            <!-- محددات الموقع -->
            <div class="row hidden-element" id="location_selectors">
                <div class="col-md-4 mb-3" id="branch_container">
                    <label for="branch_id" class="form-label">اختر الفرع:</label>
                    <select class="form-select" id="branch_id" name="branch_id" onchange="updateAreas()">
                        <option value="">-- اختر الفرع --</option>
                        {% for branch in branches %}
                        <option value="{{ branch.id }}" {{ 'selected' if branch_id == branch.id|string else '' }}>{{ branch.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-4 mb-3 hidden-element" id="area_container">
                    <label for="area_id" class="form-label">اختر المنطقة:</label>
                    <select class="form-select" id="area_id" name="area_id" onchange="updateClinics()">
                        <option value="">-- اختر المنطقة --</option>
                    </select>
                </div>

                <div class="col-md-4 mb-3 hidden-element" id="clinic_container">
                    <label for="clinic_id" class="form-label">اختر العيادة:</label>
                    <select class="form-select" id="clinic_id" name="clinic_id">
                        <option value="">-- اختر العيادة --</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-12 text-center">
                    <button type="submit" class="btn btn-light btn-lg">
                        <i class="mdi mdi-magnify me-1"></i>تطبيق الفلاتر
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- معلومات التقرير -->
    <div class="row">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white text-center">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الشعار" class="report-logo">
                        </div>
                        <div class="col-md-8 text-center">
                            <h5 class="card-title mb-0">
                                <i class="mdi mdi-chart-box-multiple me-2"></i>التقرير الشامل
                            </h5>
                        </div>
                        <div class="col-md-2 text-center">
                            <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الشعار" class="report-logo">
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <strong>الفترة:</strong><br>
                            <span class="text-primary">{{ date_range_text }}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>النطاق:</strong><br>
                            <span class="text-success">{{ scope_text }}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>تاريخ التقرير:</strong><br>
                            <span class="text-info">{{ current_date }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card">
                <h6>إجمالي الأدوية المنصرفة</h6>
                <h3>{{ total_drugs_dispensed }}</h3>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h6>إجمالي الأنسولين المنصرف</h6>
                <h3>{{ total_insulin_dispensed }}</h3>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h6>إجمالي التكلفة</h6>
                <h3>{{ "%.2f"|format(total_cost) }} جنيه مصري</h3>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <h6>عدد العيادات النشطة</h6>
                <h3>{{ active_clinics_count }}</h3>
            </div>
        </div>
    </div>

    <!-- تقرير الأدوية -->
    <div class="report-section">
        <div class="section-title">
            <i class="mdi mdi-pill me-2"></i>تقرير الأدوية
        </div>
        {% if drugs_data %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #343a40; color: white;">
                    <tr>
                        <th style="color: white; background-color: #343a40;">اسم الدواء</th>
                        <th style="color: white; background-color: #343a40;">التصنيف</th>
                        <th style="color: white; background-color: #343a40;">الكمية</th>
                        <th style="color: white; background-color: #343a40;">الوحدة</th>
                        <th style="color: white; background-color: #343a40;">عدد الحالات</th>
                        <th style="color: white; background-color: #343a40;">سعر الوحدة</th>
                        <th style="color: white; background-color: #343a40;">التكلفة (جنيه مصري)</th>
                        <th style="color: white; background-color: #343a40;">شهر الصرف</th>
                        <th style="color: white; background-color: #343a40;">العيادة</th>
                        <th style="color: white; background-color: #343a40;">المنطقة</th>
                        <th style="color: white; background-color: #343a40;">الفرع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for drug in drugs_data %}
                    <tr>
                        <td>{{ drug.drug_name }}</td>
                        <td>{{ drug.category_name }}</td>
                        <td>{{ drug.quantity or 0 }}</td>
                        <td>{{ drug.unit }}</td>
                        <td>{{ drug.cases_count or 0 }}</td>
                        <td>{{ "%.2f"|format(drug.price or 0) }}</td>
                        <td>{{ "%.2f"|format(drug.cost or 0) }}</td>
                        <td>{{ drug.dispense_date }}</td>
                        <td>{{ drug.clinic_name }}</td>
                        <td>{{ drug.area_name }}</td>
                        <td>{{ drug.branch_name }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info text-center">
            <i class="mdi mdi-information me-2"></i>لا توجد بيانات أدوية للفترة المحددة
        </div>
        {% endif %}
    </div>

    <!-- تقرير الأنسولين -->
    <div class="report-section">
        <div class="section-title">
            <i class="mdi mdi-needle me-2"></i>تقرير الأنسولين
        </div>
        {% if insulin_data %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #343a40; color: white;">
                    <tr>
                        <th style="color: white; background-color: #343a40;">اسم الأنسولين</th>
                        <th style="color: white; background-color: #343a40;">النوع</th>
                        <th style="color: white; background-color: #343a40;">الفئة</th>
                        <th style="color: white; background-color: #343a40;">الكمية</th>
                        <th style="color: white; background-color: #343a40;">عدد الحالات</th>
                        <th style="color: white; background-color: #343a40;">سعر الوحدة</th>
                        <th style="color: white; background-color: #343a40;">المعدل</th>
                        <th style="color: white; background-color: #343a40;">التكلفة (جنيه مصري)</th>
                        <th style="color: white; background-color: #343a40;">شهر الصرف</th>
                        <th style="color: white; background-color: #343a40;">العيادة</th>
                        <th style="color: white; background-color: #343a40;">المنطقة</th>
                        <th style="color: white; background-color: #343a40;">الفرع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for insulin in insulin_data %}
                    <tr>
                        <td>{{ insulin.name }}</td>
                        <td>{{ insulin.type }}</td>
                        <td>{{ insulin.category }}</td>
                        <td>{{ insulin.quantity }}</td>
                        <td>{{ insulin.cases_count }}</td>
                        <td>{{ "%.2f"|format(insulin.price or 0) }}</td>
                        <td>{{ "%.2f"|format(insulin.rate or 0) }}</td>
                        <td>{{ "%.2f"|format(insulin.cost or 0) }}</td>
                        <td>{{ insulin.dispense_month }}</td>
                        <td>{{ insulin.clinic_name }}</td>
                        <td>{{ insulin.area_name }}</td>
                        <td>{{ insulin.branch_name }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info text-center">
            <i class="mdi mdi-information me-2"></i>لا توجد بيانات أنسولين للفترة المحددة
        </div>
        {% endif %}
    </div>

    {% if drug_groups_data %}
    <!-- تقرير المجموعات الدوائية -->
    <div class="report-section">
        <div class="section-title">
            <i class="mdi mdi-pills me-2"></i>تقرير المجموعات الدوائية
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #343a40; color: white;">
                    <tr>
                        <th style="color: white; background-color: #343a40;">اسم المجموعة</th>
                        <th style="color: white; background-color: #343a40;">عدد الأدوية</th>
                        <th style="color: white; background-color: #343a40;">إجمالي الكمية</th>
                        <th style="color: white; background-color: #343a40;">إجمالي التكلفة (جنيه مصري)</th>
                        <th style="color: white; background-color: #343a40;">شهر الصرف</th>
                        <th style="color: white; background-color: #343a40;">العيادة</th>
                        <th style="color: white; background-color: #343a40;">المنطقة</th>
                        <th style="color: white; background-color: #343a40;">الفرع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for group in drug_groups_data %}
                    <tr>
                        <td>{{ group.group_name }}</td>
                        <td>{{ group.drug_count }}</td>
                        <td>{{ group.total_quantity }}</td>
                        <td>{{ "%.2f"|format(group.total_cost) }}</td>
                        <td>{{ group.dispense_month }}</td>
                        <td>{{ group.clinic_name }}</td>
                        <td>{{ group.area_name }}</td>
                        <td>{{ group.branch_name }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    {% if clinics_summary %}
    <!-- ملخص العيادات -->
    <div class="report-section">
        <div class="section-title">
            <i class="mdi mdi-hospital-building me-2"></i>ملخص العيادات
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #343a40; color: white;">
                    <tr>
                        <th style="color: white; background-color: #343a40;">اسم العيادة</th>
                        <th style="color: white; background-color: #343a40;">المنطقة</th>
                        <th style="color: white; background-color: #343a40;">الفرع</th>
                        <th style="color: white; background-color: #343a40;">عدد صرف الأدوية</th>
                        <th style="color: white; background-color: #343a40;">تكلفة الأدوية (جنيه مصري)</th>
                        <th style="color: white; background-color: #343a40;">عدد حالات الأدوية</th>
                        <th style="color: white; background-color: #343a40;">عدد صرف الأنسولين</th>
                        <th style="color: white; background-color: #343a40;">تكلفة الأنسولين (جنيه مصري)</th>
                        <th style="color: white; background-color: #343a40;">عدد حالات الأنسولين</th>
                        <th style="color: white; background-color: #343a40;">تكلفة المجموعات (جنيه مصري)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for clinic in clinics_summary %}
                    <tr>
                        <td>{{ clinic.clinic_name }}</td>
                        <td>{{ clinic.area_name }}</td>
                        <td>{{ clinic.branch_name }}</td>
                        <td>{{ clinic.drugs_count or 0 }}</td>
                        <td>{{ "%.2f"|format(clinic.drugs_cost or 0) }}</td>
                        <td>{{ clinic.drugs_cases or 0 }}</td>
                        <td>{{ clinic.insulin_count or 0 }}</td>
                        <td>{{ "%.2f"|format(clinic.insulin_cost or 0) }}</td>
                        <td>{{ clinic.insulin_cases or 0 }}</td>
                        <td>{{ "%.2f"|format(clinic.groups_cost or 0) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

</div>
{% endblock %}

{% block scripts %}
<!-- تمرير البيانات إلى JavaScript -->
<script type="application/json" id="app-data">
{
    "branches": {{ branches|tojson }},
    "areas": {{ areas|tojson }},
    "clinics": {{ clinics|tojson }}
}
</script>

<!-- Scripts للتصدير -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<script>
// تحميل البيانات من JSON
let appData;
try {
    appData = JSON.parse(document.getElementById('app-data').textContent);
    console.log('البيانات المحملة:', appData);
    console.log('عدد الفروع:', appData.branches.length);
    console.log('عدد المناطق:', appData.areas.length);
    console.log('عدد العيادات:', appData.clinics.length);
} catch (error) {
    console.error('خطأ في تحميل البيانات:', error);
    appData = { branches: [], areas: [], clinics: [] };
}

// وظائف التحكم في الفلاتر
function toggleCustomDates() {
    const dateRange = document.getElementById('date_range').value;
    const customDates = document.getElementById('custom_dates');
    const customDatesEnd = document.getElementById('custom_dates_end');

    if (dateRange === 'custom') {
        customDates.classList.remove('hidden-element');
        customDatesEnd.classList.remove('hidden-element');
    } else {
        customDates.classList.add('hidden-element');
        customDatesEnd.classList.add('hidden-element');
    }
}

function toggleLocationSelectors() {
    const scopeType = document.getElementById('scope_type').value;
    const locationSelectors = document.getElementById('location_selectors');
    const areaContainer = document.getElementById('area_container');
    const clinicContainer = document.getElementById('clinic_container');

    if (scopeType === 'all') {
        locationSelectors.classList.add('hidden-element');
    } else {
        locationSelectors.classList.remove('hidden-element');

        if (scopeType === 'branch') {
            areaContainer.classList.add('hidden-element');
            clinicContainer.classList.add('hidden-element');
        } else if (scopeType === 'area') {
            areaContainer.classList.remove('hidden-element');
            clinicContainer.classList.add('hidden-element');
        } else if (scopeType === 'clinic') {
            areaContainer.classList.remove('hidden-element');
            clinicContainer.classList.remove('hidden-element');
        }
    }
}

function updateAreas() {
    const branchId = document.getElementById('branch_id').value;
    const areaSelect = document.getElementById('area_id');

    console.log('تحديث المناطق للفرع:', branchId);
    console.log('المناطق المتاحة:', appData.areas);

    areaSelect.innerHTML = '<option value="">-- اختر المنطقة --</option>';

    if (branchId) {
        const filteredAreas = appData.areas.filter(area => area.branch_id.toString() === branchId);
        console.log('المناطق المفلترة:', filteredAreas);

        filteredAreas.forEach(function(area) {
            areaSelect.innerHTML += `<option value="${area.id}">${area.name}</option>`;
        });
    }

    // إعادة تعيين العيادات
    updateClinics();
}

function updateClinics() {
    const areaId = document.getElementById('area_id').value;
    const clinicSelect = document.getElementById('clinic_id');

    console.log('تحديث العيادات للمنطقة:', areaId);
    console.log('العيادات المتاحة:', appData.clinics);

    clinicSelect.innerHTML = '<option value="">-- اختر العيادة --</option>';

    if (areaId) {
        const filteredClinics = appData.clinics.filter(clinic => clinic.area_id.toString() === areaId);
        console.log('العيادات المفلترة:', filteredClinics);

        filteredClinics.forEach(function(clinic) {
            clinicSelect.innerHTML += `<option value="${clinic.id}">${clinic.name}</option>`;
        });
    }
}

// تشغيل الفلاتر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleCustomDates();
    toggleLocationSelectors();

    // تحديث المناطق والعيادات إذا كانت محددة مسبقاً
    const branchId = document.getElementById('branch_id').value;
    const areaId = '{{ area_id if area_id else "" }}';
    const clinicId = '{{ clinic_id if clinic_id else "" }}';

    console.log('القيم المحددة مسبقاً - فرع:', branchId, 'منطقة:', areaId, 'عيادة:', clinicId);

    if (branchId) {
        updateAreas();

        // تحديد المنطقة المحددة مسبقاً
        setTimeout(function() {
            if (areaId) {
                document.getElementById('area_id').value = areaId;
                updateClinics();

                // تحديد العيادة المحددة مسبقاً
                setTimeout(function() {
                    if (clinicId) {
                        document.getElementById('clinic_id').value = clinicId;
                    }
                }, 100);
            }
        }, 100);
    }
});

// وظيفة تصدير إلى PDF
async function exportToPDF() {
    try {
        // إخفاء العناصر غير المطلوبة
        var noprint = document.querySelectorAll('.no-print');
        noprint.forEach(function(element) {
            element.style.display = 'none';
        });

        // الحصول على المحتوى المراد تحويله
        var element = document.querySelector('.container-fluid');

        // تحويل إلى صورة
        const canvas = await html2canvas(element, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            width: element.scrollWidth,
            height: element.scrollHeight
        });

        // إنشاء PDF بالوضع الأفقي
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('l', 'mm', 'a4');

        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 295; // عرض A4 أفقي
        const pageHeight = 210; // ارتفاع A4 أفقي
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        // إضافة الصفحة الأولى
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        const filename = `التقرير_الشامل_${new Date().toISOString().slice(0,10)}.pdf`;
        pdf.save(filename);

        // إظهار العناصر مرة أخرى
        noprint.forEach(function(element) {
            element.style.display = '';
        });

        console.log('تم تصدير PDF بنجاح');
    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير PDF. يرجى المحاولة مرة أخرى.');

        // إظهار العناصر مرة أخرى في حالة الخطأ
        var noprint = document.querySelectorAll('.no-print');
        noprint.forEach(function(element) {
            element.style.display = '';
        });
    }
}

// وظيفة تصدير إلى Excel
function exportToExcel() {
    // الحصول على المعاملات الحالية من URL
    const urlParams = new URLSearchParams(window.location.search);

    // بناء URL للتصدير مع نفس المعاملات
    const exportUrl = '/reports/comprehensive/export-excel?' + urlParams.toString();

    // فتح رابط التحميل
    window.location.href = exportUrl;
}
</script>
{% endblock %}

<!-- حقوق الملكية -->
{% include 'includes/copyright_footer.html' %}
