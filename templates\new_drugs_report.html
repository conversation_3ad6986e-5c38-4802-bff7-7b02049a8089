{% extends 'base.html' %}

{% block title %}تقرير الأدوية{% endblock %}

{% block styles %}
<style>
    @media print {
        body {
            background-color: #fff !important;
        }
        .no-print {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .card-header {
            background-color: #f8f9fc !important;
            color: #000 !important;
            border-bottom: 2px solid #4e73df !important;
        }
        .table {
            width: 100% !important;
            border-collapse: collapse !important;
        }
        .table th, .table td {
            border: 1px solid #ddd !important;
        }
        footer {
            display: none !important;
        }

        /* تنسيق التصنيفات عند الطباعة */
        .category-section {
            margin-bottom: 30px !important;
            page-break-inside: avoid !important;
            break-inside: avoid !important;
            border: 2px solid #4e73df !important;
            border-radius: 5px !important;
            overflow: hidden !important;
        }

        .category-header {
            background-color: #4e73df !important;
            color: white !important;
            padding: 12px 15px !important;
            margin-bottom: 0 !important;
            break-after: avoid !important;
            border-bottom: none !important;
        }

        .category-title {
            font-size: 16pt !important;
            font-weight: bold !important;
            color: white !important;
            text-shadow: none !important;
        }

        .category-total {
            background-color: white !important;
            color: #28a745 !important;
            border: 1px solid #28a745 !important;
            padding: 5px 10px !important;
            box-shadow: none !important;
        }

        /* إضافة اللوجو في بداية صفحة الطباعة */
        body::before {
            content: '';
            display: block;
            background-image: url("{{ url_for('static', filename='images/logo.png') }}");
            background-repeat: no-repeat;
            background-position: center top;
            background-size: 150px auto;
            height: 100px;
            margin-bottom: 20px;
        }
    }

    .report-header {
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        text-align: center;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .report-header h2 {
        color: #4e73df;
        margin-bottom: 1rem;
    }

    .report-header p {
        color: #5a5c69;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    .drug-card {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
    }

    .drug-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }

    .drug-card .card-header {
        background-color: #1cc88a;
        color: white;
        padding: 1rem;
    }

    .total-card {
        background-color: #4e73df;
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
        text-align: center;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .total-card h3 {
        margin-bottom: 0;
    }

    .btn-print {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .btn-print:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .scope-badge {
        background-color: #f6c23e;
        color: #fff;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        display: inline-block;
        margin-top: 0.5rem;
    }

    /* تنسيق قسم التصنيف */
    .category-section {
        margin-bottom: 40px;
        border: 1px solid #e3e6f0;
        border-radius: 8px;
        overflow: hidden;
    }

    .category-header {
        background-color: #4e73df;
        padding: 15px 20px;
        margin-bottom: 0 !important;
        color: white;
    }

    .category-title {
        color: white;
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    }

    .category-total {
        background-color: white;
        color: #28a745;
        padding: 8px 15px;
        border-radius: 30px;
        font-weight: 700;
        font-size: 1.1rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* أنماط أزرار الإجراءات */
    .action-buttons {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .action-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        font-size: 24px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
    }

    .action-btn:hover {
        transform: scale(1.1);
        color: white;
        text-decoration: none;
    }

    .print-btn {
        background-color: #4e73df;
    }

    .print-btn:hover {
        background-color: #3a5fc9;
    }

    .back-btn {
        background-color: #6c757d;
    }

    .back-btn:hover {
        background-color: #5a6268;
    }

    .excel-btn {
        background-color: #1d6f42;
    }

    .excel-btn:hover {
        background-color: #185a36;
    }

    .pdf-btn {
        background-color: #e74a3b;
    }

    .pdf-btn:hover {
        background-color: #c0392b;
    }

    .pdf-icon {
        font-family: Arial, sans-serif;
        font-weight: bold;
        font-size: 20px;
        color: white;
        text-align: center;
        display: block;
        line-height: 1;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        letter-spacing: 0.5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-pill me-2"></i>تقرير الأدوية
                </h4>
                <button class="btn btn-light btn-print no-print" onclick="window.print()">
                    <i class="mdi mdi-printer me-1"></i>طباعة التقرير
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="report-header">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                <h2><i class="mdi mdi-file-document-outline me-2"></i>{{ report_title|default('تقرير استهلاك الأدوية') }}</h2>

                {% if scope_type == 'all' %}
                <p><i class="mdi mdi-domain me-2"></i>النطاق: جميع الفروع</p>
                {% elif scope_type == 'branch' %}
                <p><i class="mdi mdi-domain me-2"></i>الفرع: {{ scope_name }}</p>
                {% elif scope_type == 'area' %}
                <p><i class="mdi mdi-map-marker me-2"></i>المنطقة: {{ scope_name }}</p>
                {% elif scope_type == 'clinic' %}
                <p><i class="mdi mdi-hospital-building me-2"></i>العيادة: {{ scope_name }}</p>
                {% endif %}

                <p><i class="mdi mdi-calendar me-2"></i>الفترة: {{ period }}</p>

                {% if category_name %}
                <p><i class="mdi mdi-tag-multiple me-2"></i>تصنيف الدواء: {{ category_name }}</p>
                {% endif %}
            </div>

            {% if categories %}
                {% for category in categories %}
                <!-- بداية قسم التصنيف -->
                <div class="category-section mb-5">
                    <!-- عنوان التصنيف مع خلفية ملونة -->
                    <div class="category-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="category-title">
                                <i class="mdi mdi-tag-multiple me-2"></i>{{ category.name }}
                            </h4>
                            <span class="category-total">{{ category.total_cost }} جنيه</span>
                        </div>
                    </div>

                    <!-- جدول الأدوية في التصنيف -->
                    <div class="card shadow-sm">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">
                                    <thead style="background: linear-gradient(135deg, #16a085, #1abc9c); color: white;">
                                        <tr>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                                <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                                <span>#</span>
                                            </th>
                                            <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                                <i class="mdi mdi-pill me-2" style="color: #f39c12;"></i>
                                                <span>اسم الدواء</span>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 120px;">
                                                <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                                <span>الكمية المنصرفة</span><br>
                                                <small style="color: #ecf0f1; font-size: 11px;">(الوحدات)</small>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e74c3c; min-width: 100px;">
                                                <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                                <span>عدد الحالات</span><br>
                                                <small style="color: #ecf0f1; font-size: 11px;">(الصناديق)</small>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 100px;">
                                                <i class="mdi mdi-currency-usd me-2" style="color: #f1c40f;"></i>
                                                <span>السعر</span><br>
                                                <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                                            </th>
                                            <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 150px;">
                                                <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                                <span>إجمالي التكلفة</span><br>
                                                <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for drug in category.drugs %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ drug.name }}</td>
                                            <td class="text-center">{{ drug.total_quantity }}</td>
                                            <td class="text-center">{{ drug.total_cases }}</td>
                                            <td class="text-center">{{ drug.price }}</td>
                                            <td class="text-center fw-bold">{{ drug.total_cost }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th colspan="5" class="text-start">إجمالي التصنيف</th>
                                            <th class="text-center">{{ category.total_cost }} جنيه</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- نهاية قسم التصنيف -->
                {% endfor %}

                <div class="total-card">
                    <h3><i class="mdi mdi-cash-multiple me-2"></i>إجمالي تكلفة الأدوية: {{ total_cost }} جنيه</h3>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="mdi mdi-information-outline me-2"></i>لا توجد بيانات للأدوية في الفترة المحددة
                </div>
            {% endif %}

            <div class="text-center mt-4">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary no-print">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="action-buttons no-print">
    <button class="action-btn print-btn" onclick="showPrintOptions('print')" title="طباعة التقرير">
        <i class="mdi mdi-printer"></i>
    </button>
    <button class="action-btn excel-btn" onclick="exportToExcel()" title="تصدير إلى Excel">
        <i class="mdi mdi-file-excel"></i>
    </button>
    <button class="action-btn pdf-btn" onclick="showPrintOptions('pdf')" title="طباعة كملف PDF">
        <span class="pdf-icon">PDF</span>
    </button>
    <a href="{{ url_for('reports') }}" class="action-btn back-btn" title="العودة إلى التقارير">
        <i class="mdi mdi-arrow-left"></i>
    </a>
</div>

<!-- مربع حوار خيارات الطباعة -->
<div class="modal fade" id="printOptionsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">اتجاه الصفحة</label>
                    <div class="d-flex gap-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="portrait" value="portrait" checked>
                            <label class="form-check-label" for="portrait">
                                <i class="mdi mdi-file-document-outline me-1"></i>عمودي
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="landscape" value="landscape">
                            <label class="form-check-label" for="landscape">
                                <i class="mdi mdi-file-document-outline me-1"></i>أفقي
                            </label>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="fontSize" class="form-label">حجم الخط</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmPrint">
                    <i class="mdi mdi-printer me-1"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script>
    // متغيرات عامة
    var printAction = 'print'; // 'print' أو 'pdf'
    var printModal;

    // وظيفة إظهار خيارات الطباعة
    function showPrintOptions(action) {
        printAction = action;

        // تهيئة مربع الحوار إذا لم يكن موجودًا
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));

            // إضافة حدث للزر "طباعة"
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }

        // عرض مربع الحوار
        printModal.show();
    }

    // وظيفة تنفيذ الطباعة بناءً على الخيارات المحددة
    function performPrint() {
        try {
            // الحصول على الخيارات المحددة
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            // تطبيق بعض التعديلات قبل الطباعة
            var originalOverflow = document.body.style.overflow;
            var originalWidth = document.body.style.width;

            // تعديل نمط الصفحة للطباعة
            document.body.style.overflow = 'visible';
            document.body.style.width = 'auto';

            // تطبيق اتجاه الصفحة
            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; }';

            // تطبيق حجم الخط
            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            // إضافة العنصر إلى الصفحة
            document.head.appendChild(styleElement);

            // طباعة الصفحة
            window.print();

            // إزالة العنصر بعد الطباعة
            setTimeout(function() {
                document.head.removeChild(styleElement);
                document.body.style.overflow = originalOverflow;
                document.body.style.width = originalWidth;
            }, 1000);
        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
        }
    }

    // وظيفة تصدير البيانات إلى Excel مع تنسيق
    async function exportToExcel() {
        try {
            // إنشاء مصنف جديد
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('تقرير الأدوية');

            let currentRow = 1;

            // إضافة عنوان التقرير
            const reportTitle = document.querySelector('.report-header h2');
            const titleCell = worksheet.getCell('A' + currentRow);
            titleCell.value = reportTitle ? reportTitle.innerText : 'تقرير استهلاك الأدوية';
            titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
            titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4E73DF' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
            currentRow += 2;

            // إضافة معلومات التقرير
            const reportInfo = document.querySelectorAll('.report-header p');
            reportInfo.forEach(function(info) {
                const infoCell = worksheet.getCell('A' + currentRow);
                infoCell.value = info.innerText;
                infoCell.font = { bold: true, size: 12 };
                infoCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FC' } };
                currentRow++;
            });
            currentRow++; // سطر فارغ

            // معالجة كل تصنيف
            const categories = document.querySelectorAll('.category-section');
            categories.forEach(function(categorySection) {
                // عنوان التصنيف
                const categoryTitle = categorySection.querySelector('.category-title');
                if (categoryTitle) {
                    const catTitleCell = worksheet.getCell('A' + currentRow);
                    catTitleCell.value = categoryTitle.innerText;
                    catTitleCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
                    catTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF1CC88A' } };
                    catTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
                    currentRow++;

                    // عناوين الأعمدة
                    const table = categorySection.querySelector('.table');
                    if (table) {
                        const headers = [];
                        table.querySelectorAll('thead th').forEach(function(th) {
                            headers.push(th.innerText);
                        });

                        headers.forEach((header, index) => {
                            const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                            cell.value = header;
                            cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF36B9CC' } };
                            cell.alignment = { horizontal: 'center', vertical: 'middle' };
                            cell.border = {
                                top: { style: 'thin' },
                                left: { style: 'thin' },
                                bottom: { style: 'thin' },
                                right: { style: 'thin' }
                            };
                        });
                        currentRow++;

                        // بيانات الأدوية
                        table.querySelectorAll('tbody tr').forEach(function(tr) {
                            const rowData = [];
                            tr.querySelectorAll('td').forEach(function(td) {
                                const text = td.innerText.trim();
                                if (!isNaN(text) && text !== '') {
                                    rowData.push(parseFloat(text));
                                } else {
                                    rowData.push(text);
                                }
                            });

                            rowData.forEach((data, index) => {
                                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                                cell.value = data;
                                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                                cell.border = {
                                    top: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                                    left: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                                    bottom: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                                    right: { style: 'thin', color: { argb: 'FFDDDDDD' } }
                                };
                            });
                            currentRow++;
                        });

                        // إجمالي التصنيف
                        const footerRow = table.querySelector('tfoot tr');
                        if (footerRow) {
                            const totalData = [];
                            footerRow.querySelectorAll('th, td').forEach(function(cell) {
                                const text = cell.innerText.trim();
                                if (!isNaN(text) && text !== '') {
                                    totalData.push(parseFloat(text));
                                } else {
                                    totalData.push(text);
                                }
                            });

                            totalData.forEach((data, index) => {
                                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                                cell.value = data;
                                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                                cell.border = {
                                    top: { style: 'thin' },
                                    left: { style: 'thin' },
                                    bottom: { style: 'thin' },
                                    right: { style: 'thin' }
                                };
                            });
                            currentRow++;
                        }
                    }
                    currentRow++; // سطر فارغ بين التصنيفات
                }
            });

            // الإجمالي العام
            const totalCard = document.querySelector('.total-card h3');
            if (totalCard) {
                const grandTotalData = [totalCard.innerText, '', '', '', '', ''];
                grandTotalData.forEach((data, index) => {
                    const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    cell.value = data;
                    cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            }

            // تحديد عرض الأعمدة
            worksheet.columns = [
                { width: 5 },   // #
                { width: 35 },  // اسم الدواء
                { width: 18 },  // الكمية المنصرفة
                { width: 15 },  // عدد الحالات
                { width: 12 },  // السعر
                { width: 18 }   // إجمالي التكلفة
            ];

            // تصدير الملف
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const fileName = "تقرير_الأدوية_منسق_" + new Date().toISOString().slice(0, 10) + ".xlsx";
            saveAs(blob, fileName);
        } catch (error) {
            console.error("Error exporting to Excel:", error);
            alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
        }
    }
</script>
{% endblock %}
