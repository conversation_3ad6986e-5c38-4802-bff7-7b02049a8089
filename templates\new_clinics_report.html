{% extends 'base.html' %}

{% block title %}تقرير العيادة{% endblock %}

{% block styles %}
<style>
    @media print {
        @page {
            margin: 0.5in;
            size: A4;
        }

        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            transition: none !important;
            transform: none !important;
            animation: none !important;
        }

        body {
            background-color: #fff !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 12pt !important;
            line-height: 1.4 !important;
        }

        /* إخفاء جميع العناصر التفاعلية والحواشي العلوية */
        .btn, .navbar, footer, .no-print,
        .row.mb-3, .d-flex.justify-content-between,
        button, .action-buttons, .badge {
            display: none !important;
        }

        .container-fluid {
            padding: 0 !important;
            margin: 0 !important;
            max-width: 100% !important;
        }

        .card {
            border: none !important;
            border-radius: 0 !important;
            box-shadow: none !important;
            margin: 0 !important;
            page-break-inside: avoid;
        }

        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
            border-bottom: 2px solid #4e73df !important;
            padding: 15px !important;
            margin: 0 !important;
        }

        .card-body {
            padding: 15px !important;
            margin: 0 !important;
        }

        .table {
            width: 100% !important;
            border-collapse: collapse !important;
            margin: 10px 0 !important;
            font-size: 11pt !important;
        }

        .table th, .table td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
            text-align: center !important;
        }

        .table th {
            background-color: #f8f9fa !important;
            font-weight: bold !important;
        }

        .report-logo {
            max-width: 120px !important;
            height: auto !important;
            margin: 0 auto 15px !important;
            display: block !important;
        }

        .report-header {
            background-color: transparent !important;
            border: none !important;
            padding: 10px !important;
            margin: 0 !important;
        }

        h1, h2, h3, h4, h5, h6 {
            margin: 10px 0 !important;
            page-break-after: avoid;
        }

        /* منع الصفحة الفارغة في البداية */
        .py-4 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

        .mb-3, .mb-4 {
            margin-bottom: 10px !important;
        }

        .mt-3 {
            margin-top: 10px !important;
        }

        /* إزالة المسافات من العناصر الأولى */
        body > *:first-child,
        .container-fluid > *:first-child {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        .text-center {
            text-align: center !important;
        }

        .fw-bold {
            font-weight: bold !important;
        }
    }

    .report-header {
        background-color: #f8f9fc;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        text-align: center;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .report-header h2 {
        color: #4e73df;
        margin-bottom: 1rem;
    }

    .report-header p {
        color: #5a5c69;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    .category-card {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
    }

    .category-card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }

    .category-card .card-header {
        background-color: #4e73df;
        color: white;
        padding: 1rem;
    }

    .total-card {
        background-color: #1cc88a;
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 1.5rem;
        text-align: center;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .total-card h3 {
        margin-bottom: 0;
    }

    .btn-print {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .btn-print:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="mdi mdi-hospital-building me-2"></i>تقرير العيادة
                </h4>
                <button class="btn btn-light btn-print no-print" onclick="showPrintOptions('print')">
                    <i class="mdi mdi-printer me-1"></i>طباعة التقرير
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="report-header">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
                <h2><i class="mdi mdi-file-document-outline me-2"></i>تقرير منصرف العيادة - {{ clinic_name }} - {% if category_name %}تصنيف {{ category_name }}{% else %}جميع التصنيفات{% endif %}</h2>
                <p><i class="mdi mdi-hospital-building me-2"></i>عيادة: {{ clinic_name }}</p>
                <p><i class="mdi mdi-map-marker me-2"></i>المنطقة: {{ area_name }} - {{ branch_name }}</p>
                <p><i class="mdi mdi-calendar me-2"></i>شهر: {{ month }}</p>
                <p><i class="mdi mdi-tag-multiple me-2"></i>تصنيف الدواء: {% if category_name %}{{ category_name }}{% else %}جميع التصنيفات{% endif %}</p>
            </div>

            {% for category in categories %}
            <div class="category-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="mdi mdi-tag-multiple me-2"></i>{{ category.name }}</h5>
                        <span class="badge bg-light text-dark">{{ category.total_cost }} جنيه</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead style="background: linear-gradient(135deg, #8e44ad, #9b59b6); color: white;">
                                <tr>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 50px; text-align: center;">
                                        <i class="mdi mdi-numeric me-1" style="color: #f1c40f;"></i>
                                        <span>#</span>
                                    </th>
                                    <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 200px;">
                                        <i class="mdi mdi-pill me-2" style="color: #f39c12;"></i>
                                        <span>اسم الدواء</span>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db; min-width: 100px;">
                                        <i class="mdi mdi-package-variant me-2" style="color: #2ecc71;"></i>
                                        <span>الكمية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(الوحدات)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 100px;">
                                        <i class="mdi mdi-currency-usd me-2" style="color: #f1c40f;"></i>
                                        <span>السعر</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(ج.م)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e74c3c; min-width: 120px;">
                                        <i class="mdi mdi-archive me-2" style="color: #e74c3c;"></i>
                                        <span>عدد الحالات</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(الصناديق)</small>
                                    </th>
                                    <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px;">
                                        <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                        <span>التكلفة الإجمالية</span><br>
                                        <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in category.drugs %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ item.drug_name }}</td>
                                    <td class="text-center">{{ item.quantity }}</td>
                                    <td class="text-center">{{ item.price }}</td>
                                    <td class="text-center">{{ item.cases }}</td>
                                    <td class="text-center fw-bold">{{ item.cost }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-primary">
                                    <th colspan="5" class="text-start">إجمالي التصنيف</th>
                                    <th class="text-center">{{ category.total_cost }} جنيه</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            {% endfor %}

            <div class="total-card">
                <h3><i class="mdi mdi-cash-multiple me-2"></i>إجمالي تكلفة المنصرف للعيادة: {{ total_cost }} جنيه</h3>
            </div>

            <div class="text-center mt-4">
                <a href="{{ url_for('reports') }}" class="btn btn-secondary no-print">
                    <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- أزرار الإجراءات -->
<div class="action-buttons no-print">
    <button class="action-btn print-btn" onclick="showPrintOptions('print')" title="طباعة التقرير">
        <i class="mdi mdi-printer"></i>
    </button>
    <button class="action-btn excel-btn" onclick="exportToExcel()" title="تصدير إلى Excel">
        <i class="mdi mdi-file-excel"></i>
    </button>
    <button class="action-btn pdf-btn" onclick="showPrintOptions('pdf')" title="طباعة كملف PDF">
        <span class="pdf-icon">PDF</span>
    </button>
    <a href="{{ url_for('reports') }}" class="action-btn back-btn" title="العودة إلى التقارير">
        <i class="mdi mdi-arrow-left"></i>
    </a>
</div>

<!-- مربع حوار خيارات الطباعة -->
<div class="modal fade" id="printOptionsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">اتجاه الصفحة</label>
                    <div class="d-flex gap-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="portrait" value="portrait" checked>
                            <label class="form-check-label" for="portrait">
                                <i class="mdi mdi-file-document-outline me-1"></i>عمودي
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="pageOrientation" id="landscape" value="landscape">
                            <label class="form-check-label" for="landscape">
                                <i class="mdi mdi-file-document-outline me-1"></i>أفقي
                            </label>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="fontSize" class="form-label">حجم الخط</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPrint">
                    <i class="mdi mdi-printer me-1"></i>طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- حقوق الملكية المصغرة -->
{% include 'includes/copyright_footer.html' %}
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/4.3.0/exceljs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script>
    // متغيرات عامة
    var printAction = 'print'; // 'print' أو 'pdf'
    var printModal;

    // وظيفة إظهار خيارات الطباعة
    function showPrintOptions(action) {
        if (action === 'pdf') {
            exportToPDF();
            return;
        }

        printAction = action;

        // تهيئة مربع الحوار إذا لم يكن موجودًا
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));

            // إضافة حدث للزر "طباعة"
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }

        // عرض مربع الحوار
        printModal.show();
    }

    // وظيفة تنفيذ الطباعة بناءً على الخيارات المحددة
    function performPrint() {
        var elementsToHide, container, originalOverflow, originalWidth, styleElement;

        try {
            // الحصول على الخيارات المحددة
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            // إخفاء العناصر غير المطلوبة للطباعة
            elementsToHide = document.querySelectorAll('.no-print, .btn, button, .row.mb-3, .d-flex.justify-content-between, .badge, .action-buttons');
            elementsToHide.forEach(function(element) {
                element.style.display = 'none';
            });

            // إزالة المسافات الإضافية من البداية
            container = document.querySelector('.container-fluid');
            if (container) {
                container.style.paddingTop = '0';
                container.style.marginTop = '0';
            }

            // تطبيق بعض التعديلات قبل الطباعة
            originalOverflow = document.body.style.overflow;
            originalWidth = document.body.style.width;

            // تعديل نمط الصفحة للطباعة
            document.body.style.overflow = 'visible';
            document.body.style.width = 'auto';

            // تطبيق اتجاه الصفحة
            styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; margin: 0.5in; }';
            styleElement.innerHTML += '@media print { body { margin: 0; padding: 20px; } }';

            // تطبيق حجم الخط
            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            // إضافة العنصر إلى الصفحة
            document.head.appendChild(styleElement);

            // إضافة مستمع لحدث afterprint للتنظيف
            var cleanupFunction = function() {
                try {
                    if (styleElement && styleElement.parentNode) {
                        document.head.removeChild(styleElement);
                    }
                    document.body.style.overflow = originalOverflow;
                    document.body.style.width = originalWidth;

                    // إظهار العناصر المخفية
                    if (elementsToHide) {
                        elementsToHide.forEach(function(element) {
                            element.style.display = '';
                        });
                    }

                    // إعادة تعيين المسافات
                    if (container) {
                        container.style.paddingTop = '';
                        container.style.marginTop = '';
                    }
                } catch (e) {
                    console.error("Error in cleanup:", e);
                }

                // إزالة المستمع
                window.removeEventListener('afterprint', cleanupFunction);
            };

            // إضافة مستمع لحدث afterprint
            window.addEventListener('afterprint', cleanupFunction);

            // طباعة الصفحة
            window.print();

            // تنظيف احتياطي بعد 3 ثوانٍ
            setTimeout(cleanupFunction, 3000);

        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");

            // تنظيف في حالة الخطأ
            try {
                if (styleElement && styleElement.parentNode) {
                    document.head.removeChild(styleElement);
                }
                if (originalOverflow !== undefined) {
                    document.body.style.overflow = originalOverflow;
                }
                if (originalWidth !== undefined) {
                    document.body.style.width = originalWidth;
                }
                if (elementsToHide) {
                    elementsToHide.forEach(function(element) {
                        element.style.display = '';
                    });
                }
                if (container) {
                    container.style.paddingTop = '';
                    container.style.marginTop = '';
                }
            } catch (e) {
                console.error("Error in error cleanup:", e);
            }
        }
    }

    // وظيفة تصدير إلى PDF حقيقي
    async function exportToPDF() {
        try {
            // إخفاء العناصر غير المطلوبة
            var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
            noprint.forEach(function(element) {
                element.style.display = 'none';
            });

            // الحصول على المحتوى المراد تحويله
            var element = document.querySelector('.container-fluid');

            // تحويل إلى صورة
            const canvas = await html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: element.scrollWidth,
                height: element.scrollHeight
            });

            // إنشاء PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');

            const imgData = canvas.toDataURL('image/png');
            const imgWidth = 210; // عرض A4 بالمليمتر
            const pageHeight = 295; // ارتفاع A4 بالمليمتر
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            // إضافة الصفحة الأولى
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // إضافة صفحات إضافية إذا لزم الأمر
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // حفظ الملف
            const fileName = "تقرير_العيادة_" + new Date().toISOString().slice(0, 10) + ".pdf";
            pdf.save(fileName);

            // إظهار العناصر المخفية
            noprint.forEach(function(element) {
                element.style.display = '';
            });

        } catch (error) {
            console.error("Error exporting to PDF:", error);
            alert("حدث خطأ أثناء التصدير إلى PDF. يرجى المحاولة مرة أخرى.");

            // إظهار العناصر المخفية في حالة الخطأ
            var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons');
            noprint.forEach(function(element) {
                element.style.display = '';
            });
        }
    }

    // وظيفة تصدير البيانات إلى Excel مع تنسيق
    async function exportToExcel() {
        try {
            // إنشاء مصنف جديد
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('تقرير العيادة');

            let currentRow = 1;

            // إضافة عنوان التقرير
            const reportTitle = document.querySelector('.report-header h2');
            const titleCell = worksheet.getCell('A' + currentRow);
            titleCell.value = reportTitle ? reportTitle.innerText : 'تقرير منصرف العيادة';
            titleCell.font = { bold: true, size: 16, color: { argb: 'FFFFFFFF' } };
            titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF6F42C1' } };
            titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
            worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
            currentRow += 2;

            // إضافة معلومات التقرير
            const reportInfo = document.querySelectorAll('.report-header p');
            reportInfo.forEach(function(info) {
                const infoCell = worksheet.getCell('A' + currentRow);
                infoCell.value = info.innerText;
                infoCell.font = { bold: true, size: 12 };
                infoCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF8F9FC' } };
                worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
                currentRow++;
            });
            currentRow++; // سطر فارغ

            // معالجة كل تصنيف
            const categoryCards = document.querySelectorAll('.category-card');
            categoryCards.forEach(function(categoryCard) {
                // عنوان التصنيف
                const categoryTitle = categoryCard.querySelector('.card-header h5');
                if (categoryTitle) {
                    const catTitleCell = worksheet.getCell('A' + currentRow);
                    catTitleCell.value = categoryTitle.innerText;
                    catTitleCell.font = { bold: true, size: 14, color: { argb: 'FFFFFFFF' } };
                    catTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF1CC88A' } };
                    catTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    worksheet.mergeCells('A' + currentRow + ':F' + currentRow);
                    currentRow++;

                    // عناوين الأعمدة
                    const table = categoryCard.querySelector('.table');
                    if (table) {
                        const headers = [];
                        table.querySelectorAll('thead th').forEach(function(th) {
                            headers.push(th.innerText);
                        });

                        headers.forEach((header, index) => {
                            const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                            cell.value = header;
                            cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF36B9CC' } };
                            cell.alignment = { horizontal: 'center', vertical: 'middle' };
                            cell.border = {
                                top: { style: 'thin' },
                                left: { style: 'thin' },
                                bottom: { style: 'thin' },
                                right: { style: 'thin' }
                            };
                        });
                        currentRow++;

                        // بيانات الأدوية
                        table.querySelectorAll('tbody tr').forEach(function(tr) {
                            const rowData = [];
                            tr.querySelectorAll('td').forEach(function(td) {
                                const text = td.innerText.trim();
                                if (!isNaN(text) && text !== '') {
                                    rowData.push(parseFloat(text));
                                } else {
                                    rowData.push(text);
                                }
                            });

                            rowData.forEach((data, index) => {
                                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                                cell.value = data;
                                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                                cell.border = {
                                    top: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                                    left: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                                    bottom: { style: 'thin', color: { argb: 'FFDDDDDD' } },
                                    right: { style: 'thin', color: { argb: 'FFDDDDDD' } }
                                };
                            });
                            currentRow++;
                        });

                        // إجمالي التصنيف
                        const footerRow = table.querySelector('tfoot tr');
                        if (footerRow) {
                            const totalData = [];
                            footerRow.querySelectorAll('th, td').forEach(function(cell) {
                                const text = cell.innerText.trim();
                                if (!isNaN(text) && text !== '') {
                                    totalData.push(parseFloat(text));
                                } else {
                                    totalData.push(text);
                                }
                            });

                            totalData.forEach((data, index) => {
                                const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                                cell.value = data;
                                cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                                cell.alignment = { horizontal: 'center', vertical: 'middle' };
                                cell.border = {
                                    top: { style: 'thin' },
                                    left: { style: 'thin' },
                                    bottom: { style: 'thin' },
                                    right: { style: 'thin' }
                                };
                            });
                            currentRow++;
                        }
                    }
                    currentRow++; // سطر فارغ بين التصنيفات
                }
            });

            // الإجمالي العام
            const totalCard = document.querySelector('.total-card h3');
            if (totalCard) {
                const grandTotalData = [totalCard.innerText, '', '', '', '', ''];
                grandTotalData.forEach((data, index) => {
                    const cell = worksheet.getCell(String.fromCharCode(65 + index) + currentRow);
                    cell.value = data;
                    cell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF28A745' } };
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            }

            // تحديد عرض الأعمدة
            worksheet.columns = [
                { width: 5 },   // #
                { width: 35 },  // اسم الدواء
                { width: 15 },  // الكمية
                { width: 12 },  // السعر
                { width: 15 },  // عدد الحالات
                { width: 18 }   // التكلفة
            ];

            // تصدير الملف
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const fileName = "تقرير_العيادة_منسق_" + new Date().toISOString().slice(0, 10) + ".xlsx";
            saveAs(blob, fileName);
        } catch (error) {
            console.error("Error exporting to Excel:", error);
            alert("حدث خطأ أثناء التصدير إلى Excel. يرجى المحاولة مرة أخرى.");
        }
    }
</script>
{% endblock %}
