-- مخطط قاعدة البيانات لتطبيق منصرف الأدوية

-- جدول الفروع
CREATE TABLE IF NOT EXISTS branches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE
);

-- جدول المناطق
CREATE TABLE IF NOT EXISTS areas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    branch_id INTEGER NOT NULL,
    FOREIGN KEY (branch_id) REFERENCES branches (id) ON DELETE CASCADE
);

-- جدو<PERSON> العيادات والإدارات
CREATE TABLE IF NOT EXISTS clinics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    area_id INTEGER NOT NULL,
    FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE
);

-- جدول تصنيفات الأدوية
CREATE TABLE IF NOT EXISTS drug_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE
);

-- جدول الأدوية
CREATE TABLE IF NOT EXISTS drugs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    scientific_name TEXT,
    category_id INTEGER NOT NULL,
    unit TEXT,
    FOREIGN KEY (category_id) REFERENCES drug_categories (id) ON DELETE CASCADE
);

-- جدول المنصرف
CREATE TABLE IF NOT EXISTS dispensed (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    drug_id INTEGER NOT NULL,
    clinic_id INTEGER NOT NULL,
    dispense_month DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (drug_id) REFERENCES drugs (id) ON DELETE CASCADE,
    FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE
);

-- جدول تفاصيل المنصرف
CREATE TABLE IF NOT EXISTS dispensed_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dispensed_id INTEGER NOT NULL,
    quantity REAL NOT NULL,
    price REAL NOT NULL,
    cases_count INTEGER NOT NULL,
    FOREIGN KEY (dispensed_id) REFERENCES dispensed (id) ON DELETE CASCADE
);

-- جدول المجموعات الدوائية
CREATE TABLE IF NOT EXISTS drug_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    cost REAL NOT NULL,
    clinic_id INTEGER NOT NULL,
    area_id INTEGER NOT NULL,
    dispense_month DATE NOT NULL,
    FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
    FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE
);

-- جدول تكويد الأنسولين
CREATE TABLE IF NOT EXISTS insulin_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code INTEGER NOT NULL UNIQUE,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أنواع الأنسولين
CREATE TABLE IF NOT EXISTS insulin_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فئات الأنسولين
CREATE TABLE IF NOT EXISTS insulin_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأنسولين
CREATE TABLE IF NOT EXISTS insulin_dispensed (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    unit TEXT NOT NULL,
    cases_count INTEGER NOT NULL,
    quantity REAL NOT NULL,
    price REAL NOT NULL,
    cost REAL NOT NULL,
    rate REAL NOT NULL,
    balance REAL NOT NULL,
    category TEXT NOT NULL,
    clinic_id INTEGER NOT NULL,
    area_id INTEGER NOT NULL,
    dispense_month DATE NOT NULL,
    insulin_code_id INTEGER,
    FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
    FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE,
    FOREIGN KEY (insulin_code_id) REFERENCES insulin_codes (id) ON DELETE SET NULL
);

-- جداول الأحكام القضائية
-- جدول المرضى
CREATE TABLE IF NOT EXISTS judicial_patients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    diagnosis TEXT,
    court_ruling_date DATE NOT NULL,
    treatment_start_date DATE NOT NULL,
    clinic_id INTEGER NOT NULL,
    area_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
    FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE
);

-- جدول أدوية أحكام المحكمة
CREATE TABLE IF NOT EXISTS judicial_medicines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    unit TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الأدوية المصروفة للأحكام القضائية
CREATE TABLE IF NOT EXISTS judicial_dispensed (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,
    diagnosis TEXT NOT NULL,
    medicine_name TEXT NOT NULL,
    unit TEXT NOT NULL,
    unit_price REAL NOT NULL,
    monthly_dose REAL NOT NULL,
    monthly_cost REAL NOT NULL,
    dispense_month TEXT NOT NULL,
    clinic_id INTEGER NOT NULL,
    area_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES judicial_patients (id) ON DELETE CASCADE,
    FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
    FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE
);

-- إنشاء بعض البيانات الافتراضية للاختبار
INSERT OR IGNORE INTO branches (name) VALUES ('الفرع الرئيسي'), ('الفرع الشمالي'), ('الفرع الجنوبي');

INSERT OR IGNORE INTO drug_categories (name) VALUES ('مضادات حيوية'), ('مسكنات'), ('أدوية القلب'), ('أدوية السكري'), ('أدوية الضغط');
