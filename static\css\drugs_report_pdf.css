/* أنماط خاصة بتقرير الأدوية */

/* أنماط أزرار الإجراءات */
.action-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    font-size: 24px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
}

.action-btn:hover {
    transform: scale(1.1);
    color: white;
    text-decoration: none;
}

.print-btn {
    background-color: #4e73df;
    color: white;
}

.print-btn:hover {
    background-color: #3a5fc9;
}

.excel-btn {
    background-color: #1d6f42;
    color: white;
}

.excel-btn:hover {
    background-color: #185a36;
}

.pdf-btn {
    background-color: #e74c3c;
    color: white;
}

.pdf-btn:hover {
    background-color: #c0392b;
}

.back-btn {
    background-color: #6c757d;
    color: white;
}

.back-btn:hover {
    background-color: #5a6268;
}

/* تنسيق نص PDF داخل الزر */
.pdf-text {
    font-family: Arial, sans-serif !important;
    font-weight: bold !important;
    font-size: 20px !important;
    color: white !important;
    text-align: center !important;
    display: block !important;
    line-height: 1 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5) !important;
    letter-spacing: 0.5px !important;
}
