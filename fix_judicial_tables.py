#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإصلاح جداول الأحكام القضائية المفقودة
"""

import sqlite3
import os

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

def create_judicial_tables():
    """إنشاء جداول الأحكام القضائية المفقودة"""
    conn = get_db_connection()
    
    try:
        print("🔧 بدء إصلاح جداول الأحكام القضائية...")
        
        # إنشاء جدول المرضى
        print("📋 إنشاء جدول judicial_patients...")
        conn.execute('''CREATE TABLE IF NOT EXISTS judicial_patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            diagnosis TEXT,
            court_ruling_date DATE NOT NULL,
            treatment_start_date DATE NOT NULL,
            clinic_id INTEGER NOT NULL,
            area_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
            FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE
        )''')
        
        # إنشاء جدول أدوية أحكام المحكمة
        print("💊 إنشاء جدول judicial_medicines...")
        conn.execute('''CREATE TABLE IF NOT EXISTS judicial_medicines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            unit TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )''')
        
        # إنشاء جدول الأدوية المصروفة للأحكام القضائية
        print("📊 إنشاء جدول judicial_dispensed...")
        conn.execute('''CREATE TABLE IF NOT EXISTS judicial_dispensed (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id INTEGER NOT NULL,
            diagnosis TEXT NOT NULL,
            medicine_name TEXT NOT NULL,
            unit TEXT NOT NULL,
            unit_price REAL NOT NULL,
            monthly_dose REAL NOT NULL,
            monthly_cost REAL NOT NULL,
            dispense_month TEXT NOT NULL,
            clinic_id INTEGER NOT NULL,
            area_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES judicial_patients (id) ON DELETE CASCADE,
            FOREIGN KEY (clinic_id) REFERENCES clinics (id) ON DELETE CASCADE,
            FOREIGN KEY (area_id) REFERENCES areas (id) ON DELETE CASCADE
        )''')
        
        # إضافة بعض البيانات التجريبية للأدوية
        print("🧪 إضافة بيانات تجريبية للأدوية...")
        sample_medicines = [
            ('أملوديبين 5 مجم', 'قرص', 'دواء لعلاج ارتفاع ضغط الدم'),
            ('لوسارتان 50 مجم', 'قرص', 'دواء لعلاج ارتفاع ضغط الدم'),
            ('ميتفورمين 500 مجم', 'قرص', 'دواء لعلاج السكري النوع الثاني'),
            ('ديكلوفيناك 50 مجم', 'قرص', 'مضاد للالتهاب ومسكن للألم'),
            ('أوميبرازول 20 مجم', 'كبسولة', 'دواء لعلاج قرحة المعدة'),
            ('سيمفاستاتين 20 مجم', 'قرص', 'دواء لخفض الكوليسترول'),
        ]
        
        for medicine in sample_medicines:
            conn.execute('''
                INSERT OR IGNORE INTO judicial_medicines (name, unit, description)
                VALUES (?, ?, ?)
            ''', medicine)
        
        conn.commit()
        print("✅ تم إنشاء جداول الأحكام القضائية بنجاح!")
        
        # التحقق من الجداول المنشأة
        print("\n📊 التحقق من الجداول المنشأة:")
        tables = ['judicial_patients', 'judicial_medicines', 'judicial_dispensed']
        
        for table in tables:
            count = conn.execute(f'SELECT COUNT(*) FROM {table}').fetchone()[0]
            print(f"   - {table}: {count} سجل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False
    finally:
        conn.close()

def check_existing_tables():
    """فحص الجداول الموجودة"""
    conn = get_db_connection()
    
    try:
        print("🔍 فحص الجداول الموجودة:")
        
        # جلب قائمة الجداول
        tables = conn.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """).fetchall()
        
        for table in tables:
            table_name = table[0]
            count = conn.execute(f'SELECT COUNT(*) FROM {table_name}').fetchone()[0]
            print(f"   ✓ {table_name}: {count} سجل")
        
        # فحص الجداول المطلوبة للأحكام القضائية
        required_tables = ['judicial_patients', 'judicial_medicines', 'judicial_dispensed']
        missing_tables = []
        
        for table in required_tables:
            result = conn.execute(f"""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='{table}'
            """).fetchone()
            
            if not result:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"\n⚠️ الجداول المفقودة: {', '.join(missing_tables)}")
            return False
        else:
            print("\n✅ جميع جداول الأحكام القضائية موجودة!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {e}")
        return False
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 بدء إصلاح قاعدة البيانات...")
    
    # فحص الجداول الموجودة
    if not check_existing_tables():
        # إنشاء الجداول المفقودة
        if create_judicial_tables():
            print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
            print("يمكنك الآن الوصول إلى تقارير الأحكام القضائية.")
        else:
            print("\n❌ فشل في إصلاح قاعدة البيانات!")
    else:
        print("\n✅ قاعدة البيانات سليمة ولا تحتاج إصلاح!")
    
    input("\nاضغط Enter للخروج...")
