{% extends "base.html" %}

{% block title %}تقرير التكلفة - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); min-height: 100vh;">
    <div class="row justify-content-center">
        <div class="col-12">
            <!-- Header Card -->
            <div class="card shadow-lg mb-4" style="border: none; border-radius: 20px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
                <div class="card-header text-white text-center py-4" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border-radius: 20px 20px 0 0; border: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <i class="mdi mdi-currency-usd" style="font-size: 3rem; margin-bottom: 10px;"></i>
                            <h2 class="mb-0 fw-bold">تقرير التكلفة</h2>
                            <p class="mb-0 opacity-75">تحليل شامل للتكاليف والنفقات</p>
                        </div>
                        <div class="d-flex gap-2 no-print">
                            <div class="btn-group">
                                <button type="button" class="btn btn-light btn-sm dropdown-toggle rounded-pill px-3" data-bs-toggle="dropdown">
                                    <i class="mdi mdi-download me-1"></i>تصدير
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                        <i class="mdi mdi-file-excel text-success me-2"></i>تصدير إلى Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                        <i class="mdi mdi-file-pdf text-danger me-2"></i>تصدير إلى PDF
                                    </a></li>
                                </ul>
                            </div>
                            <button onclick="window.print()" class="btn btn-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">فلاتر التقرير</h6>
                                    <form method="GET" class="row g-3">
                                        <div class="col-md-2">
                                            <label class="form-label">السنة</label>
                                            <select name="year" class="form-select">
                                                {% for y in range(2020, 2030) %}
                                                <option value="{{ y }}" {% if y|string == year %}selected{% endif %}>{{ y }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">نوع التحليل</label>
                                            <select name="analysis_type" class="form-select">
                                                <option value="summary" {% if analysis_type == 'summary' %}selected{% endif %}>إجمالي</option>
                                                <option value="monthly" {% if analysis_type == 'monthly' %}selected{% endif %}>شهري</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">نطاق العرض</label>
                                            <select name="scope_type" class="form-select">
                                                <option value="branches" {% if scope_type == 'branches' %}selected{% endif %}>الفروع</option>
                                                <option value="areas" {% if scope_type == 'areas' %}selected{% endif %}>المناطق</option>
                                                <option value="clinics" {% if scope_type == 'clinics' %}selected{% endif %}>العيادات</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">الفرع</label>
                                            <select name="branch_id" class="form-select">
                                                <option value="">جميع الفروع</option>
                                                {% for branch in branches %}
                                                <option value="{{ branch.id }}" {% if branch_id|string == branch.id|string %}selected{% endif %}>{{ branch.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">المنطقة</label>
                                            <select name="area_id" class="form-select" id="areaSelect" onchange="filterClinics()">
                                                <option value="">جميع المناطق</option>
                                                {% for area in areas %}
                                                <option value="{{ area.id }}" data-branch="{{ area.branch_id }}" {% if area_id|string == area.id|string %}selected{% endif %}>{{ area.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">العيادة</label>
                                            <select name="clinic_id" class="form-select" id="clinicSelect">
                                                <option value="">جميع العيادات</option>
                                                {% for clinic in clinics %}
                                                <option value="{{ clinic.id }}" data-area="{{ clinic.area_id }}" {% if clinic_id|string == clinic.id|string %}selected{% endif %}>{{ clinic.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="mdi mdi-magnify me-1"></i>تطبيق الفلاتر
                                            </button>
                                            <a href="{{ url_for('cost_report') }}" class="btn btn-secondary ms-2">
                                                <i class="mdi mdi-refresh me-1"></i>مسح الفلاتر
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التقرير -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><strong>تاريخ التقرير:</strong> {{ now.strftime('%Y-%m-%d %H:%M') if now else 'غير محدد' }}</h6>
                            <h6><strong>السنة:</strong> {{ year }}</h6>
                            <h6><strong>نوع التحليل:</strong> {{ 'شهري' if analysis_type == 'monthly' else 'إجمالي' }}</h6>
                            <h6><strong>نطاق العرض:</strong>
                                {% if scope_type == 'areas' %}المناطق
                                {% elif scope_type == 'clinics' %}العيادات
                                {% else %}الفروع{% endif %}
                            </h6>
                        </div>
                        <div class="col-md-6 text-end">
                            {% if cost_data %}
                            {% set total_cost = 0 %}
                            {% set total_dispensed = 0 %}
                            {% for item in cost_data %}
                                {% set total_cost = total_cost + (item.total_cost or 0) %}
                                {% set total_dispensed = total_dispensed + (item.total_dispensed or 0) %}
                            {% endfor %}
                            <h6><strong>إجمالي التكلفة:</strong> {{ "{:,.2f}".format(total_cost) }} ج.م</h6>
                            <h6><strong>إجمالي المنصرف:</strong> {{ total_dispensed }}</h6>
                            {% else %}
                            <h6><strong>إجمالي التكلفة:</strong> 0.00 ج.م</h6>
                            <h6><strong>إجمالي المنصرف:</strong> 0</h6>
                            {% endif %}
                        </div>
                    </div>

                    <!-- جدول التكلفة -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="mdi mdi-chart-line me-2"></i>تفاصيل التكلفة
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if cost_data %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-success">
                                        <tr>
                                            <th>#</th>
                                            {% if analysis_type == 'monthly' %}
                                            <th>الشهر</th>
                                            {% elif scope_type == 'areas' %}
                                            <th>المنطقة</th>
                                            <th>الفرع</th>
                                            {% elif scope_type == 'clinics' %}
                                            <th>العيادة</th>
                                            <th>المنطقة</th>
                                            <th>الفرع</th>
                                            {% else %}
                                            <th>الفرع</th>
                                            {% endif %}
                                            <th>التكلفة الإجمالية</th>
                                            <th>عدد المنصرف</th>
                                            {% if analysis_type != 'monthly' %}
                                            <th>الأدوية المختلفة</th>
                                            {% endif %}
                                            <th>متوسط التكلفة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in cost_data %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            {% if analysis_type == 'monthly' %}
                                            <td><strong>{{ item.month_year }}</strong></td>
                                            {% elif scope_type == 'areas' %}
                                            <td><strong>{{ item.area_name }}</strong></td>
                                            <td>{{ item.branch_name }}</td>
                                            {% elif scope_type == 'clinics' %}
                                            <td><strong>{{ item.clinic_name }}</strong></td>
                                            <td>{{ item.area_name }}</td>
                                            <td>{{ item.branch_name }}</td>
                                            {% else %}
                                            <td><strong>{{ item.branch_name }}</strong></td>
                                            {% endif %}
                                            <td>{{ "{:,.2f}".format(item.total_cost or 0) }} ج.م</td>
                                            <td>{{ item.total_dispensed or 0 }}</td>
                                            {% if analysis_type != 'monthly' %}
                                            <td>{{ item.unique_drugs or 0 }}</td>
                                            {% endif %}
                                            <td>{{ "{:,.2f}".format((item.total_cost or 0) / (item.total_dispensed or 1) if (item.total_dispensed or 0) > 0 else 0) }} ج.م</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-secondary">
                                        <tr>
                                            <th colspan="2">الإجمالي</th>
                                            {% set footer_total_cost = 0 %}
                                            {% set footer_total_dispensed = 0 %}
                                            {% for item in cost_data %}
                                                {% set footer_total_cost = footer_total_cost + (item.total_cost or 0) %}
                                                {% set footer_total_dispensed = footer_total_dispensed + (item.total_dispensed or 0) %}
                                            {% endfor %}
                                            <th>{{ "{:,.2f}".format(footer_total_cost) }} ج.م</th>
                                            <th>{{ footer_total_dispensed }}</th>
                                            {% if analysis_type != 'monthly' %}
                                            <th>-</th>
                                            {% endif %}
                                            <th>-</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="mdi mdi-information me-3" style="font-size: 2rem;"></i>
                                <strong>لا توجد بيانات تكلفة في الفترة المحددة.</strong>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- إحصائيات إضافية -->
                    {% if cost_data and cost_data|length > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">متوسط التكلفة</h6>
                                    <h4 class="text-primary">{{ "{:,.2f}".format(avg_cost) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">أعلى تكلفة</h6>
                                    <h4 class="text-success">{{ "{:,.2f}".format(max_cost) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">أقل تكلفة</h6>
                                    <h4 class="text-warning">{{ "{:,.2f}".format(min_cost) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- حقوق الحفظ للطباعة -->
                    <div class="row mt-4 print-footer">
                        <div class="col-12 text-center">
                            <hr>
                            <div class="footer-content">
                                <p class="mb-1"><strong>الهيئة العامة للتأمين الصحي</strong></p>
                                <p class="mb-1">نظام إدارة منصرف الأدوية</p>
                                <p class="mb-1">تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</p>
                                <p class="mb-0 small text-muted">© {{ now.year }} جميع الحقوق محفوظة - الهيئة العامة للتأمين الصحي</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.print-footer {
    margin-top: 30px;
    page-break-inside: avoid;
}

.footer-content {
    border: 1px solid #ddd;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

@media print {
    .btn, .no-print {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
    .table {
        font-size: 11px;
    }
    .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        margin-top: 20px;
        page-break-inside: avoid;
    }
    .footer-content {
        background-color: #f8f9fa !important;
        border: 1px solid #000 !important;
        padding: 10px !important;
        font-size: 10px;
    }
    body {
        margin-bottom: 100px;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
// فلترة العيادات حسب المنطقة المختارة
function filterClinics() {
    const areaSelect = document.getElementById('areaSelect');
    const clinicSelect = document.getElementById('clinicSelect');
    const selectedAreaId = areaSelect.value;

    // إعادة تعيين العيادات
    for (let option of clinicSelect.options) {
        if (option.value === '') {
            option.style.display = 'block';
        } else {
            const optionAreaId = option.getAttribute('data-area');
            if (selectedAreaId === '' || optionAreaId === selectedAreaId) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        }
    }

    // إعادة تعيين اختيار العيادة إذا لم تعد متاحة
    if (clinicSelect.value !== '') {
        const selectedOption = clinicSelect.options[clinicSelect.selectedIndex];
        if (selectedOption.style.display === 'none') {
            clinicSelect.value = '';
        }
    }
}

// دالة التصدير
function exportReport(format) {
    if (format === 'excel') {
        // تصدير Excel عبر Backend
        const urlParams = new URLSearchParams(window.location.search);
        const exportParams = new URLSearchParams();

        // إضافة جميع المعاملات الحالية
        for (const [key, value] of urlParams) {
            exportParams.append(key, value);
        }

        // إضافة نوع التصدير
        exportParams.append('format', format);

        // إنشاء رابط التصدير
        const exportUrl = '/reports/cost/export?' + exportParams.toString();

        // تحميل الملف مباشرة بدلاً من فتح نافذة جديدة
        const link = document.createElement('a');
        link.href = exportUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } else if (format === 'pdf') {
        // تصدير PDF باستخدام jsPDF + html2canvas
        exportToPDF();
    }
}

// دالة تصدير PDF مثل باقي التقارير
function exportToPDF() {
    // إخفاء العناصر غير المطلوبة
    var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons, .dropdown');
    noprint.forEach(function(element) {
        element.style.display = 'none';
    });

    // الحصول على المحتوى المراد تحويله
    var element = document.querySelector('.container-fluid');

    // تحويل إلى صورة
    html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
    }).then(function(canvas) {
        // إنشاء PDF
        const { jsPDF } = window.jspdf;
        var pdf = new jsPDF('p', 'mm', 'a4');

        var imgData = canvas.toDataURL('image/png');
        var imgWidth = 210;
        var pageHeight = 295;
        var imgHeight = (canvas.height * imgWidth) / canvas.width;
        var heightLeft = imgHeight;
        var position = 0;

        // إضافة الصفحة الأولى
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        var now = new Date();
        var dateStr = now.getFullYear() + '-' +
                     String(now.getMonth() + 1).padStart(2, '0') + '-' +
                     String(now.getDate()).padStart(2, '0');
        var filename = 'تقرير_التكلفة_' + dateStr + '.pdf';

        pdf.save(filename);

        // إظهار العناصر مرة أخرى
        noprint.forEach(function(element) {
            element.style.display = '';
        });

        console.log('تم تصدير PDF بنجاح');
    }).catch(function(error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير PDF. يرجى المحاولة مرة أخرى.');

        // إظهار العناصر مرة أخرى في حالة الخطأ
        noprint.forEach(function(element) {
            element.style.display = '';
        });
    });
}

// تطبيق الفلترة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    filterClinics();
});
</script>
{% endblock %}
