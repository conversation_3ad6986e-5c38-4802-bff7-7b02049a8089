#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لتطبيق إدارة منصرف الأدوية
"""

import sys
import os
from datetime import datetime

# إصلاح مشكلة ترميز النصوص العربية والرموز التعبيرية في Windows
if os.name == 'nt':  # Windows
    try:
        # ضبط ترميز وحدة التحكم إلى UTF-8
        import ctypes
        ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        ctypes.windll.kernel32.SetConsoleCP(65001)
    except Exception:
        pass

    # إعادة تكوين تدفقات الإخراج لاستخدام UTF-8
    try:
        # للإصدارات الحديثة من Python (3.7+)
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        # للإصدارات الأقدم من Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)

def main():
    print("=" * 60)
    print("🏥 نظام إدارة منصرف الأدوية والأنسولين")
    print("=" * 60)
    print("📋 المطور: أحمد علي أحمد (أحمد كوكب)")
    print("📧 البريد الإلكتروني: <EMAIL>")
    print("📱 الهاتف/واتساب: 01000314398")
    print("🌐 الموقع الإلكتروني: <EMAIL>")
    print("=" * 60)
    
    try:
        print("🔄 تحميل التطبيق...")
        from app import app

        # إضافة متغير التاريخ
        @app.context_processor
        def inject_now_date():
            return {'now_date': datetime.now().date()}

        print("✅ تم تحميل التطبيق بنجاح")
        print("🚀 بدء تشغيل الخادم على المنفذ 8080...")
        print("🔗 رابط التطبيق: http://localhost:8080")
        print("=" * 60)
        print("📝 ملاحظة: اضغط Ctrl+C لإيقاف الخادم")
        print("=" * 60)

        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=8080,
            debug=True,
            use_reloader=False
        )
        
    except ImportError as e:
        print(f"❌ خطأ في تحميل التطبيق: {e}")
        print("🔧 تأكد من وجود ملف app.py")
        sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("⏹️  تم إيقاف الخادم بواسطة المستخدم")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print("=" * 60)
        sys.exit(1)

if __name__ == '__main__':
    main()
