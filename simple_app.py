#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من تطبيق منصرف الأدوية
تعمل مع المكتبات الأساسية فقط
"""

import os
import sys
import sqlite3
from datetime import datetime
import json

# إصلاح مشكلة ترميز النصوص العربية والرموز التعبيرية في Windows
if os.name == 'nt':  # Windows
    try:
        import ctypes
        ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        ctypes.windll.kernel32.SetConsoleCP(65001)
    except Exception:
        pass

    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)

def safe_print(text):
    """طباعة آمنة للنصوص العربية والرموز التعبيرية"""
    try:
        print(text)
    except UnicodeEncodeError:
        try:
            print(text.encode('utf-8', 'replace').decode('utf-8', 'replace'))
        except:
            print("رسالة نصية (تعذر عرض الترميز الأصلي)")
    except Exception as e:
        print(f"خطأ في الطباعة: {str(e)}")

def get_db_connection():
    """الاتصال بقاعدة البيانات"""
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات"""
    conn = get_db_connection()
    
    try:
        # قراءة ملف schema.sql إذا كان موجوداً
        if os.path.exists('schema.sql'):
            with open('schema.sql', 'r', encoding='utf-8') as f:
                schema = f.read()
                conn.executescript(schema)
                safe_print("✅ تم تطبيق مخطط قاعدة البيانات")
        else:
            # إنشاء الجداول الأساسية
            conn.execute('''
                CREATE TABLE IF NOT EXISTS branches (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS drug_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE
                )
            ''')
            
            safe_print("✅ تم إنشاء الجداول الأساسية")
        
        conn.commit()
        
    except Exception as e:
        safe_print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
    finally:
        conn.close()

def show_database_info():
    """عرض معلومات قاعدة البيانات"""
    conn = get_db_connection()
    
    try:
        # عرض الجداول الموجودة
        tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
        safe_print(f"📊 عدد الجداول: {len(tables)}")
        
        for table in tables:
            table_name = table['name']
            count = conn.execute(f"SELECT COUNT(*) as count FROM {table_name}").fetchone()
            safe_print(f"  - {table_name}: {count['count']} سجل")
            
    except Exception as e:
        safe_print(f"❌ خطأ في عرض معلومات قاعدة البيانات: {e}")
    finally:
        conn.close()

def main():
    safe_print("=" * 60)
    safe_print("🏥 تطبيق منصرف الأدوية - النسخة المبسطة")
    safe_print("=" * 60)
    safe_print("📋 المطور: أحمد علي أحمد (أحمد كوكب)")
    safe_print("📧 البريد الإلكتروني: <EMAIL>")
    safe_print("📱 الهاتف/واتساب: 01000314398")
    safe_print("=" * 60)
    
    # تهيئة قاعدة البيانات
    safe_print("🔄 تهيئة قاعدة البيانات...")
    init_database()
    
    # عرض معلومات قاعدة البيانات
    safe_print("\n📊 معلومات قاعدة البيانات:")
    show_database_info()
    
    safe_print("\n" + "=" * 60)
    safe_print("✅ التطبيق جاهز!")
    safe_print("💡 لتشغيل التطبيق الكامل، استخدم: python run_app.py")
    safe_print("💡 تأكد من تثبيت Flask أولاً: pip install flask openpyxl")
    safe_print("=" * 60)

if __name__ == "__main__":
    main()
