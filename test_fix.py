#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة sqlite3.Row object has no attribute 'get'
"""

import sqlite3
import os

def test_sqlite_row_fix():
    """اختبار إصلاح مشكلة sqlite3.Row"""
    
    # إنشاء اتصال مؤقت بقاعدة البيانات
    if not os.path.exists('instance'):
        os.makedirs('instance')
    
    conn = sqlite3.connect('instance/medicine_dispenser.db')
    conn.row_factory = sqlite3.Row
    
    try:
        # اختبار استعلام بسيط
        result = conn.execute("SELECT name FROM branches LIMIT 1").fetchone()
        
        if result:
            print("✅ اختبار sqlite3.Row نجح")
            print(f"اسم الفرع: {result['name']}")
            
            # اختبار التحويل إلى dict
            result_dict = dict(result)
            print(f"تحويل إلى dict: {result_dict}")
            
            # اختبار الوصول للبيانات
            name = result['name'] if 'name' in result else 'غير محدد'
            print(f"الوصول الآمن للبيانات: {name}")
            
            return True
        else:
            print("❌ لا توجد بيانات للاختبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False
    finally:
        conn.close()

def test_detailed_comparison_logic():
    """اختبار منطق تقرير المقارنة التفصيلية"""
    
    # محاكاة sqlite3.Row object
    class MockRow:
        def __init__(self, data):
            self._data = data
        
        def __getitem__(self, key):
            return self._data[key]
        
        def __contains__(self, key):
            return key in self._data
        
        def keys(self):
            return self._data.keys()
    
    # بيانات تجريبية
    test_row = MockRow({
        'drug_name': 'دواء تجريبي',
        'item_type': 'دواء',
        'cases_count': 10,
        'total_quantity': 100,
        'total_cost': 500.0,
        'avg_price': 5.0
    })
    
    try:
        # اختبار المنطق المُصحح
        drug_data = {
            'name': test_row['drug_name'],
            'type': test_row['item_type'] if 'item_type' in test_row else 'دواء',
            'cases_count': test_row['cases_count'],
            'total_quantity': test_row['total_quantity'],
            'total_cost': test_row['total_cost'],
            'avg_price': test_row['avg_price']
        }
        
        print("✅ اختبار منطق المقارنة التفصيلية نجح")
        print(f"البيانات المُعالجة: {drug_data}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المنطق: {e}")
        return False

if __name__ == "__main__":
    print("=== اختبار إصلاح مشكلة sqlite3.Row ===")
    
    # اختبار قاعدة البيانات
    db_test = test_sqlite_row_fix()
    
    # اختبار المنطق
    logic_test = test_detailed_comparison_logic()
    
    if db_test and logic_test:
        print("\n✅ جميع الاختبارات نجحت! المشكلة تم إصلاحها.")
    else:
        print("\n❌ بعض الاختبارات فشلت.")
