#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد مبسط للتطبيق - بدون مكتبات خارجية
"""

import os
import sys
import sqlite3
from datetime import datetime

# إصلاح مشكلة ترميز النصوص العربية والرموز التعبيرية في Windows
if os.name == 'nt':  # Windows
    try:
        import ctypes
        ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        ctypes.windll.kernel32.SetConsoleCP(65001)
    except Exception:
        pass

    try:
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)

def safe_print(text):
    """طباعة آمنة للنصوص العربية والرموز التعبيرية"""
    try:
        print(text)
    except UnicodeEncodeError:
        try:
            print(text.encode('utf-8', 'replace').decode('utf-8', 'replace'))
        except:
            print("رسالة نصية (تعذر عرض الترميز الأصلي)")
    except Exception as e:
        print(f"خطأ في الطباعة: {str(e)}")

def check_database():
    """التحقق من قاعدة البيانات وإنشاؤها إذا لزم الأمر"""
    try:
        # التأكد من وجود مجلد instance
        if not os.path.exists('instance'):
            os.makedirs('instance')
            safe_print("✅ تم إنشاء مجلد instance")

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('instance/medicine_dispenser.db')
        conn.row_factory = sqlite3.Row
        
        # التحقق من وجود الجداول الأساسية
        tables_to_check = ['branches', 'areas', 'clinics', 'drugs', 'drug_categories']
        
        for table in tables_to_check:
            result = conn.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'").fetchone()
            if result:
                safe_print(f"✅ جدول {table} موجود")
            else:
                safe_print(f"⚠️ جدول {table} غير موجود")
        
        conn.close()
        safe_print("✅ تم فحص قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        safe_print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def main():
    safe_print("=" * 60)
    safe_print("🏥 إعداد تطبيق منصرف الأدوية")
    safe_print("=" * 60)
    
    # فحص قاعدة البيانات
    safe_print("🔍 فحص قاعدة البيانات...")
    if check_database():
        safe_print("✅ قاعدة البيانات جاهزة")
    else:
        safe_print("❌ مشكلة في قاعدة البيانات")
    
    safe_print("\n" + "=" * 60)
    safe_print("📋 ملاحظات مهمة:")
    safe_print("1. تأكد من تثبيت Python بشكل صحيح")
    safe_print("2. قم بتثبيت Flask: pip install flask")
    safe_print("3. قم بتثبيت openpyxl: pip install openpyxl")
    safe_print("4. شغل التطبيق: python run_app.py")
    safe_print("=" * 60)

if __name__ == "__main__":
    main()
