{% extends "base.html" %}

{% block title %}تقرير أعلى الأصناف تكلفة - تطبيق منصرف الأدوية{% endblock %}

{% block content %}
<div class="container-fluid py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
    <div class="row justify-content-center">
        <div class="col-12">
            <!-- Header Card -->
            <div class="card shadow-lg mb-4" style="border: none; border-radius: 20px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
                <div class="card-header text-white text-center py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px 20px 0 0; border: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div></div>
                        <div class="text-center">
                            <i class="mdi mdi-trophy" style="font-size: 3rem; margin-bottom: 10px;"></i>
                            <h2 class="mb-0 fw-bold">تقرير أعلى {{ limit }} أصناف تكلفة</h2>
                            <p class="mb-0 opacity-75">تحليل الأدوية والأنسولين الأكثر تكلفة في النظام</p>
                        </div>
                        <div class="d-flex gap-2 no-print">
                            <div class="btn-group">
                                <button type="button" class="btn btn-light btn-sm dropdown-toggle rounded-pill px-3" data-bs-toggle="dropdown">
                                    <i class="mdi mdi-download me-1"></i>تصدير
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportTopDrugsReport('excel')">
                                        <i class="mdi mdi-file-excel text-success me-2"></i>تصدير إلى Excel
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportTopDrugsReport('pdf')">
                                        <i class="mdi mdi-file-pdf text-danger me-2"></i>تصدير إلى PDF
                                    </a></li>
                                </ul>
                            </div>
                            <button onclick="window.print()" class="btn btn-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-printer me-1"></i>طباعة
                            </button>
                            <a href="{{ url_for('reports') }}" class="btn btn-outline-light btn-sm rounded-pill px-3">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">فلاتر التقرير</h6>
                                    <form method="GET" class="row g-3">
                                        <div class="col-md-2">
                                            <label class="form-label">عدد الأصناف</label>
                                            <select name="limit" class="form-select">
                                                <option value="10" {% if limit == '10' %}selected{% endif %}>أعلى 10</option>
                                                <option value="20" {% if limit == '20' %}selected{% endif %}>أعلى 20</option>
                                                <option value="50" {% if limit == '50' %}selected{% endif %}>أعلى 50</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">نوع الفترة</label>
                                            <select name="date_filter_type" class="form-select" id="dateFilterType" onchange="toggleDateFilters()">
                                                <option value="month" {% if date_filter_type == 'month' %}selected{% endif %}>شهر</option>
                                                <option value="year" {% if date_filter_type == 'year' %}selected{% endif %}>سنة</option>
                                                <option value="quarter" {% if date_filter_type == 'quarter' %}selected{% endif %}>ربع سنوي</option>
                                                <option value="custom" {% if date_filter_type == 'custom' %}selected{% endif %}>من شهر إلى شهر</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2" id="monthFilter">
                                            <label class="form-label">الشهر/السنة</label>
                                            <input type="month" name="month" class="form-control" value="{{ month or '' }}">
                                        </div>
                                        <div class="col-md-2" id="yearFilter" style="display: none;">
                                            <label class="form-label">السنة</label>
                                            <select name="year" class="form-select">
                                                {% for y in range(2020, 2030) %}
                                                <option value="{{ y }}" {% if y|string == year %}selected{% endif %}>{{ y }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2" id="quarterFilter" style="display: none;">
                                            <label class="form-label">الربع</label>
                                            <select name="quarter" class="form-select">
                                                <option value="1" {% if quarter == '1' %}selected{% endif %}>الربع الأول (يناير-مارس)</option>
                                                <option value="2" {% if quarter == '2' %}selected{% endif %}>الربع الثاني (أبريل-يونيو)</option>
                                                <option value="3" {% if quarter == '3' %}selected{% endif %}>الربع الثالث (يوليو-سبتمبر)</option>
                                                <option value="4" {% if quarter == '4' %}selected{% endif %}>الربع الرابع (أكتوبر-ديسمبر)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2" id="startMonthFilter" style="display: none;">
                                            <label class="form-label">من شهر</label>
                                            <input type="month" name="start_month" class="form-control" value="{{ start_date or '' }}">
                                        </div>
                                        <div class="col-md-2" id="endMonthFilter" style="display: none;">
                                            <label class="form-label">إلى شهر</label>
                                            <input type="month" name="end_month" class="form-control" value="{{ end_date or '' }}">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">الفرع</label>
                                            <select name="branch_id" class="form-select">
                                                <option value="">جميع الفروع</option>
                                                {% for branch in branches %}
                                                <option value="{{ branch.id }}" {% if branch_id|string == branch.id|string %}selected{% endif %}>{{ branch.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">المنطقة</label>
                                            <select name="area_id" class="form-select" id="areaSelect2" onchange="filterClinics2()">
                                                <option value="">جميع المناطق</option>
                                                {% for area in areas %}
                                                <option value="{{ area.id }}" data-branch="{{ area.branch_id }}" {% if area_id|string == area.id|string %}selected{% endif %}>{{ area.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">العيادة</label>
                                            <select name="clinic_id" class="form-select" id="clinicSelect2">
                                                <option value="">جميع العيادات</option>
                                                {% for clinic in clinics %}
                                                <option value="{{ clinic.id }}" data-area="{{ clinic.area_id }}" {% if clinic_id|string == clinic.id|string %}selected{% endif %}>{{ clinic.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="submit" class="btn btn-primary mt-4">
                                                <i class="mdi mdi-magnify me-1"></i>تطبيق الفلاتر
                                            </button>
                                            <a href="{{ url_for('top_drugs_cost_report') }}" class="btn btn-secondary mt-2">
                                                <i class="mdi mdi-refresh me-1"></i>مسح الفلاتر
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">عدد الأصناف</h6>
                                    <h4>{{ top_drugs|length }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">إجمالي التكلفة</h6>
                                    <h4>{{ "{:,.2f}".format(total_cost) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">إجمالي الكمية</h6>
                                    <h4>{{ "{:,}".format(total_quantity) }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h6 class="card-title">متوسط التكلفة</h6>
                                    <h4>{{ "{:,.2f}".format(total_cost / top_drugs|length if top_drugs|length > 0 else 0) }} ج.م</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول البيانات -->
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="mdi mdi-table me-2"></i>
                                أعلى {{ limit }} أصناف تكلفة (أدوية + أنسولين) -
                                {% if date_filter_type == 'month' %}{{ month }}
                                {% elif date_filter_type == 'quarter' %}الربع {{ quarter }} من {{ year }}
                                {% elif date_filter_type == 'custom' %}{{ start_date }} إلى {{ end_date }}
                                {% else %}{{ year }}{% endif %}
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if top_drugs %}
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>الترتيب</th>
                                            <th>اسم الصنف</th>
                                            <th>النوع</th>
                                            <th>الفئة</th>
                                            <th>إجمالي التكلفة</th>
                                            <th>الكمية المنصرفة</th>
                                            <th>عدد مرات الصرف</th>
                                            <th>عدد العيادات</th>
                                            <th>متوسط السعر</th>
                                            <th>النسبة من الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for drug in top_drugs %}
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">{{ loop.index }}</span>
                                            </td>
                                            <td><strong>{{ drug.drug_name }}</strong></td>
                                            <td>
                                                {% if drug.item_type == 'أنسولين' %}
                                                <span class="badge bg-info">{{ drug.item_type }}</span>
                                                {% else %}
                                                <span class="badge bg-success">{{ drug.item_type }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ drug.category_name }}</span>
                                            </td>
                                            <td>
                                                <strong class="text-success">{{ "{:,.2f}".format(drug.total_cost) }} ج.م</strong>
                                            </td>
                                            <td>{{ "{:,}".format(drug.total_quantity) }}</td>
                                            <td>{{ drug.dispensed_count }}</td>
                                            <td>{{ drug.clinics_count }}</td>
                                            <td>{{ "{:,.2f}".format(drug.avg_price) }} ج.م</td>
                                            <td>
                                                {% if total_cost > 0 %}
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: {{ (drug.total_cost / total_cost * 100)|round(1) }}%">
                                                        {{ "{:.1f}".format(drug.total_cost / total_cost * 100) }}%
                                                    </div>
                                                </div>
                                                {% else %}
                                                0%
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-success">
                                        <tr>
                                            <th colspan="4">الإجمالي</th>
                                            <th>{{ "{:,.2f}".format(total_cost) }} ج.م</th>
                                            <th>{{ "{:,}".format(total_quantity) }}</th>
                                            <th>{{ top_drugs|sum(attribute='dispensed_count') }}</th>
                                            <th>{{ top_drugs|length }}</th>
                                            <th>{{ "{:,.2f}".format(total_cost / total_quantity if total_quantity > 0 else 0) }} ج.م</th>
                                            <th>100%</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="mdi mdi-database-off" style="font-size: 4rem; color: #ccc;"></i>
                                <h5 class="text-muted mt-3">لا توجد بيانات للعرض</h5>
                                <p class="text-muted">يرجى تعديل الفلاتر والمحاولة مرة أخرى</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- حقوق الحفظ للطباعة -->
                    <div class="row mt-4 print-footer">
                        <div class="col-12 text-center">
                            <hr>
                            <div class="footer-content">
                                <p class="mb-1"><strong>الهيئة العامة للتأمين الصحي</strong></p>
                                <p class="mb-1">نظام إدارة منصرف الأدوية</p>
                                <p class="mb-1">تاريخ الطباعة: {{ now.strftime('%Y-%m-%d %H:%M') }}</p>
                                <p class="mb-0 small text-muted">© {{ now.year }} جميع الحقوق محفوظة - الهيئة العامة للتأمين الصحي</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.print-footer {
    margin-top: 30px;
    page-break-inside: avoid;
}

.footer-content {
    border: 1px solid #ddd;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

@media print {
    .btn, .no-print {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
    .table {
        font-size: 11px;
    }
    .print-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        margin-top: 20px;
        page-break-inside: avoid;
    }
    .footer-content {
        background-color: #f8f9fa !important;
        border: 1px solid #000 !important;
        padding: 10px !important;
        font-size: 10px;
    }
    body {
        margin-bottom: 100px;
    }
}
</style>

<script>
// إظهار/إخفاء فلاتر التاريخ حسب النوع المختار
function toggleDateFilters() {
    const filterType = document.getElementById('dateFilterType').value;

    // إخفاء جميع الفلاتر
    document.getElementById('yearFilter').style.display = 'none';
    document.getElementById('monthFilter').style.display = 'none';
    document.getElementById('quarterFilter').style.display = 'none';
    document.getElementById('startMonthFilter').style.display = 'none';
    document.getElementById('endMonthFilter').style.display = 'none';

    // إظهار الفلاتر المناسبة
    if (filterType === 'month') {
        document.getElementById('monthFilter').style.display = 'block';
    } else if (filterType === 'year') {
        document.getElementById('yearFilter').style.display = 'block';
    } else if (filterType === 'quarter') {
        document.getElementById('yearFilter').style.display = 'block';
        document.getElementById('quarterFilter').style.display = 'block';
    } else if (filterType === 'custom') {
        document.getElementById('startMonthFilter').style.display = 'block';
        document.getElementById('endMonthFilter').style.display = 'block';
    }
}

// فلترة العيادات حسب المنطقة المختارة
function filterClinics2() {
    const areaSelect = document.getElementById('areaSelect2');
    const clinicSelect = document.getElementById('clinicSelect2');
    const selectedAreaId = areaSelect.value;

    // إعادة تعيين العيادات
    for (let option of clinicSelect.options) {
        if (option.value === '') {
            option.style.display = 'block';
        } else {
            const optionAreaId = option.getAttribute('data-area');
            if (selectedAreaId === '' || optionAreaId === selectedAreaId) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        }
    }

    // إعادة تعيين اختيار العيادة إذا لم تعد متاحة
    if (clinicSelect.value !== '') {
        const selectedOption = clinicSelect.options[clinicSelect.selectedIndex];
        if (selectedOption.style.display === 'none') {
            clinicSelect.value = '';
        }
    }
}

// دالة التصدير لتقرير أعلى الأصناف
function exportTopDrugsReport(format) {
    if (format === 'excel') {
        // تصدير Excel عبر Backend
        const urlParams = new URLSearchParams(window.location.search);
        const exportParams = new URLSearchParams();

        // إضافة جميع المعاملات الحالية
        for (const [key, value] of urlParams) {
            exportParams.append(key, value);
        }

        // إضافة نوع التصدير
        exportParams.append('format', format);

        // إنشاء رابط التصدير
        const exportUrl = '/reports/top_drugs_cost/export?' + exportParams.toString();

        // تحميل الملف مباشرة بدلاً من فتح نافذة جديدة
        const link = document.createElement('a');
        link.href = exportUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } else if (format === 'pdf') {
        // تصدير PDF باستخدام jsPDF + html2canvas
        exportTopDrugsToPDF();
    }
}

// دالة تصدير PDF مثل باقي التقارير
function exportTopDrugsToPDF() {
    // إخفاء العناصر غير المطلوبة
    var noprint = document.querySelectorAll('.no-print, .btn, button, .action-buttons, .dropdown');
    noprint.forEach(function(element) {
        element.style.display = 'none';
    });

    // الحصول على المحتوى المراد تحويله
    var element = document.querySelector('.container-fluid');

    // تحويل إلى صورة
    html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
    }).then(function(canvas) {
        // إنشاء PDF
        const { jsPDF } = window.jspdf;
        var pdf = new jsPDF('p', 'mm', 'a4');

        var imgData = canvas.toDataURL('image/png');
        var imgWidth = 210;
        var pageHeight = 295;
        var imgHeight = (canvas.height * imgWidth) / canvas.width;
        var heightLeft = imgHeight;
        var position = 0;

        // إضافة الصفحة الأولى
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // إضافة صفحات إضافية إذا لزم الأمر
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // حفظ الملف
        var now = new Date();
        var dateStr = now.getFullYear() + '-' +
                     String(now.getMonth() + 1).padStart(2, '0') + '-' +
                     String(now.getDate()).padStart(2, '0');
        var filename = 'أعلى_الأصناف_تكلفة_' + dateStr + '.pdf';

        pdf.save(filename);

        // إظهار العناصر مرة أخرى
        noprint.forEach(function(element) {
            element.style.display = '';
        });

        console.log('تم تصدير PDF بنجاح');
    }).catch(function(error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير PDF. يرجى المحاولة مرة أخرى.');

        // إظهار العناصر مرة أخرى في حالة الخطأ
        noprint.forEach(function(element) {
            element.style.display = '';
        });
    });
}

// تطبيق الفلاتر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    toggleDateFilters();
    filterClinics2();
});
</script>
{% endblock %}
