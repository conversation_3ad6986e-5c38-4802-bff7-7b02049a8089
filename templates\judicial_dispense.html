{% extends "base.html" %}

{% block title %}منصرف أدوية الأحكام القضائية{% endblock %}

{% block extra_css %}
<style>
.table th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.85rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.btn-group .btn {
    margin: 0 1px;
}

.badge {
    font-size: 0.75rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header .header-title {
    color: white;
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    color: white !important;
    border: none;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.table tbody tr:nth-child(even) {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* تحسينات الفلترة */
.card-header .btn-light {
    background-color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: #495057;
    font-weight: 600;
    transition: all 0.3s ease;
}

.card-header .btn-light:hover {
    background-color: white;
    border-color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card-header .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

#judicialFilters .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

#judicialFilters .form-select,
#judicialFilters .form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#judicialFilters .form-select:focus,
#judicialFilters .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.collapse.show {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
                        <li class="breadcrumb-item active">منصرف أدوية الأحكام القضائية</li>
                    </ol>
                </div>
                <h4 class="page-title">منصرف أدوية الأحكام القضائية</h4>
            </div>
        </div>
    </div>

    <!-- عرض الرسائل -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <!-- إضافة مريض جديد -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">إضافة مريض جديد</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('judicial_dispense') }}">
                        <input type="hidden" name="action" value="add_patient">
                        
                        <div class="mb-3">
                            <label for="patient_name" class="form-label">اسم المريض</label>
                            <input type="text" class="form-control" id="patient_name" name="patient_name" required>
                        </div>

                        <div class="mb-3">
                            <label for="patient_diagnosis" class="form-label">التشخيص</label>
                            <input type="text" class="form-control" id="patient_diagnosis" name="patient_diagnosis" placeholder="مثال: ارتفاع ضغط الدم">
                        </div>

                        <div class="mb-3">
                            <label for="clinic_id" class="form-label">العيادة</label>
                            <select class="form-select" id="clinic_id" name="clinic_id" required>
                                <option value="">اختر العيادة</option>
                                {% for clinic in clinics %}
                                    <option value="{{ clinic.id }}">{{ clinic.branch_name }} - {{ clinic.area_name }} - {{ clinic.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="court_ruling_date" class="form-label">تاريخ حكم المحكمة</label>
                            <input type="date" class="form-control" id="court_ruling_date" name="court_ruling_date" required>
                        </div>

                        <div class="mb-3">
                            <label for="treatment_start_date" class="form-label">تاريخ بداية صرف العلاج</label>
                            <input type="date" class="form-control" id="treatment_start_date" name="treatment_start_date" required>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-plus me-1"></i>إضافة المريض
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- إضافة دواء جديد -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">إضافة دواء للمريض</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('judicial_dispense') }}" id="medicine-form">
                        <input type="hidden" name="action" value="add_medicine">
                        
                        <div class="mb-3">
                            <label for="patient_id" class="form-label">المريض</label>
                            <select class="form-select" id="patient_id" name="patient_id" required onchange="loadPatientData()">
                                <option value="">اختر المريض</option>
                                {% for patient in patients %}
                                    <option value="{{ patient.id }}">{{ patient.name }} - {{ patient.clinic_name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="court_ruling_date_display" class="form-label">تاريخ حكم المحكمة</label>
                                    <input type="text" class="form-control" id="court_ruling_date_display" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="treatment_start_date_display" class="form-label">تاريخ بداية العلاج</label>
                                    <input type="text" class="form-control" id="treatment_start_date_display" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="diagnosis" class="form-label">التشخيص</label>
                            <input type="text" class="form-control" id="diagnosis" name="diagnosis" required placeholder="سيتم تحميله تلقائياً عند اختيار المريض">
                            <div class="form-text">يمكنك تعديل التشخيص إذا لزم الأمر</div>
                        </div>

                        <div class="mb-3">
                            <label for="medicine_select" class="form-label">اسم صنف العلاج</label>
                            <select class="form-select" id="medicine_select" onchange="loadMedicineData()">
                                <option value="">اختر الدواء من القائمة</option>
                                {% for med in judicial_medicines %}
                                    <option value="{{ med.id }}">{{ med.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">أو أدخل اسم الدواء يدوياً:</div>
                            <input type="text" class="form-control mt-2" id="medicine_name" name="medicine_name" required placeholder="اسم الدواء">
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="unit" class="form-label">الوحدة</label>
                                    <input type="text" class="form-control" id="unit" name="unit" required placeholder="سيتم تحميلها تلقائياً">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="unit_price" class="form-label">سعر الوحدة</label>
                                    <input type="number" step="0.01" class="form-control" id="unit_price" name="unit_price" required onchange="calculateCost()">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="monthly_dose" class="form-label">الجرعة شهرياً</label>
                                    <input type="number" step="0.01" class="form-control" id="monthly_dose" name="monthly_dose" required onchange="calculateCost()">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="monthly_cost_display" class="form-label">التكلفة شهرياً</label>
                                    <input type="text" class="form-control" id="monthly_cost_display" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dispense_month" class="form-label">شهر الصرف</label>
                                    <input type="month" class="form-control" id="dispense_month" name="dispense_month" required>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" id="clinic_id_hidden" name="clinic_id">

                        <button type="submit" class="btn btn-success">
                            <i class="mdi mdi-plus me-1"></i>إضافة الدواء
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المرضى -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">قائمة مرضى الأحكام القضائية</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">
                            <thead class="table-dark">
                                <tr class="text-center">
                                    <th style="background-color: #495057; color: white;">اسم المريض</th>
                                    <th style="background-color: #495057; color: white;">التشخيص</th>
                                    <th style="background-color: #495057; color: white;">العيادة</th>
                                    <th style="background-color: #495057; color: white;">تاريخ حكم المحكمة</th>
                                    <th style="background-color: #495057; color: white;">تاريخ بداية العلاج</th>
                                    <th style="background-color: #495057; color: white;">تاريخ الإضافة</th>
                                    <th style="background-color: #495057; color: white;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for patient in patients %}
                                <tr class="align-middle">
                                    <td class="fw-bold text-primary">{{ patient.name }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ patient.diagnosis or 'غير محدد' }}</span>
                                    </td>
                                    <td class="text-muted small">{{ patient.branch_name }} - {{ patient.area_name }} - {{ patient.clinic_name }}</td>
                                    <td class="text-center">{{ patient.court_ruling_date }}</td>
                                    <td class="text-center">{{ patient.treatment_start_date }}</td>
                                    <td class="text-center">{{ patient.created_at[:10] if patient.created_at else 'غير محدد' }}</td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-warning btn-sm" onclick="editPatient({{ patient.id }}, '{{ patient.name }}', '{{ patient.diagnosis or '' }}', '{{ patient.court_ruling_date }}', '{{ patient.treatment_start_date }}', {{ patient.clinic_id }})" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <form method="POST" action="{{ url_for('delete_judicial_patient', patient_id=patient.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المريض؟')">
                                                <button type="submit" class="btn btn-danger btn-sm" title="حذف">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الأدوية المصروفة -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="header-title mb-0">قائمة الأدوية المصروفة</h4>
                    <div>
                        <button class="btn btn-light btn-sm shadow-sm{% if request.args.get('filter_clinic') or request.args.get('filter_patient') or request.args.get('filter_month') %} btn-warning{% endif %}" type="button" data-bs-toggle="collapse" data-bs-target="#judicialFilters" aria-expanded="{% if request.args.get('filter_clinic') or request.args.get('filter_patient') or request.args.get('filter_month') %}true{% else %}false{% endif %}" aria-controls="judicialFilters" style="border: 2px solid #fff; font-weight: 600;">
                            <i class="mdi mdi-filter-variant me-1"></i>فلترة السجلات
                            {% if request.args.get('filter_clinic') or request.args.get('filter_patient') or request.args.get('filter_month') %}
                            <span class="badge bg-danger text-white ms-1">مفعل</span>
                            {% endif %}
                        </button>
                    </div>
                </div>

                <!-- قسم الفلترة -->
                <div class="collapse{% if request.args.get('filter_clinic') or request.args.get('filter_patient') or request.args.get('filter_month') %} show{% endif %}" id="judicialFilters">
                    <div class="card-body border-bottom" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-top: 3px solid #007bff;">
                        <div class="row mb-2">
                            <div class="col-12">
                                <h6 class="text-primary mb-0"><i class="mdi mdi-filter me-1"></i>خيارات الفلترة</h6>
                                <small class="text-muted">اختر المعايير المطلوبة لفلترة السجلات</small>
                            </div>
                        </div>
                        <form method="GET" action="{{ url_for('judicial_dispense') }}" class="row g-3">
                            <div class="col-md-4">
                                <label for="filter_clinic" class="form-label">العيادة</label>
                                <select class="form-select" id="filter_clinic" name="filter_clinic">
                                    <option value="">جميع العيادات</option>
                                    {% for clinic in clinics %}
                                        <option value="{{ clinic.id }}" {% if request.args.get('filter_clinic') == clinic.id|string %}selected{% endif %}>
                                            {{ clinic.branch_name }} - {{ clinic.area_name }} - {{ clinic.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="filter_patient" class="form-label">المريض</label>
                                <select class="form-select" id="filter_patient" name="filter_patient">
                                    <option value="">جميع المرضى</option>
                                    {% for patient in patients %}
                                        <option value="{{ patient.id }}" {% if request.args.get('filter_patient') == patient.id|string %}selected{% endif %}>
                                            {{ patient.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-4">
                                <label for="filter_month" class="form-label">شهر الصرف</label>
                                <input type="month" class="form-control" id="filter_month" name="filter_month" value="{{ request.args.get('filter_month', '') }}">
                            </div>

                            <div class="col-12">
                                <div class="d-flex gap-2 justify-content-start">
                                    <button type="submit" id="apply-judicial-filter" class="btn btn-primary shadow-sm">
                                        <i class="mdi mdi-magnify me-1"></i>تطبيق الفلترة
                                    </button>
                                    <a href="{{ url_for('judicial_dispense') }}" class="btn btn-outline-danger shadow-sm">
                                        <i class="mdi mdi-refresh me-1"></i>إعادة تعيين
                                    </a>
                                    {% if request.args.get('filter_clinic') or request.args.get('filter_patient') or request.args.get('filter_month') %}
                                    <span class="badge bg-success align-self-center px-3 py-2">
                                        <i class="mdi mdi-check me-1"></i>الفلترة مطبقة
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">
                            <thead class="table-dark">
                                <tr class="text-center">
                                    <th style="background-color: #495057; color: white;">اسم المريض</th>
                                    <th style="background-color: #495057; color: white;">التشخيص</th>
                                    <th style="background-color: #495057; color: white;">اسم الدواء</th>
                                    <th style="background-color: #495057; color: white;">الوحدة</th>
                                    <th style="background-color: #495057; color: white;">سعر الوحدة</th>
                                    <th style="background-color: #495057; color: white;">الجرعة شهرياً</th>
                                    <th style="background-color: #495057; color: white;">التكلفة شهرياً</th>
                                    <th style="background-color: #495057; color: white;">شهر الصرف</th>
                                    <th style="background-color: #495057; color: white;">العيادة</th>
                                    <th style="background-color: #495057; color: white;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in medicines %}
                                <tr class="align-middle">
                                    <td class="fw-bold text-primary">{{ medicine.patient_name }}</td>
                                    <td>
                                        <span class="badge bg-info text-dark">{{ medicine.diagnosis }}</span>
                                    </td>
                                    <td class="fw-semibold">{{ medicine.medicine_name }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-secondary">{{ medicine.unit }}</span>
                                    </td>
                                    <td class="text-end">
                                        <span class="text-success fw-bold">{{ "%.2f"|format(medicine.unit_price) }} ج.م</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-warning text-dark">{{ "%.0f"|format(medicine.monthly_dose) }}</span>
                                    </td>
                                    <td class="text-end">
                                        <span class="text-danger fw-bold">{{ "%.2f"|format(medicine.monthly_cost) }} ج.م</span>
                                    </td>
                                    <td class="text-center">{{ medicine.dispense_month }}</td>
                                    <td class="text-muted small">{{ medicine.branch_name }} - {{ medicine.area_name }} - {{ medicine.clinic_name }}</td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-warning btn-sm" onclick="editMedicine({{ medicine.id }}, '{{ medicine.diagnosis }}', '{{ medicine.medicine_name }}', '{{ medicine.unit }}', {{ medicine.unit_price }}, {{ medicine.monthly_dose }}, '{{ medicine.dispense_month }}')" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <form method="POST" action="{{ url_for('delete_judicial_medicine', medicine_id=medicine.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الدواء؟')">
                                                <button type="submit" class="btn btn-danger btn-sm" title="حذف">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل المريض -->
<div class="modal fade" id="editPatientModal" tabindex="-1" aria-labelledby="editPatientModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPatientModalLabel">تعديل بيانات المريض</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('update_judicial_patient') }}" id="edit-patient-form">
                <div class="modal-body">
                    <input type="hidden" id="edit_patient_id" name="patient_id">

                    <div class="mb-3">
                        <label for="edit_patient_name" class="form-label">اسم المريض</label>
                        <input type="text" class="form-control" id="edit_patient_name" name="patient_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_patient_diagnosis" class="form-label">التشخيص</label>
                        <input type="text" class="form-control" id="edit_patient_diagnosis" name="patient_diagnosis">
                    </div>

                    <div class="mb-3">
                        <label for="edit_patient_clinic" class="form-label">العيادة</label>
                        <select class="form-select" id="edit_patient_clinic" name="clinic_id" required>
                            {% for clinic in clinics %}
                                <option value="{{ clinic.id }}">{{ clinic.branch_name }} - {{ clinic.area_name }} - {{ clinic.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="edit_court_ruling_date" class="form-label">تاريخ حكم المحكمة</label>
                        <input type="date" class="form-control" id="edit_court_ruling_date" name="court_ruling_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_treatment_start_date" class="form-label">تاريخ بداية صرف العلاج</label>
                        <input type="date" class="form-control" id="edit_treatment_start_date" name="treatment_start_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل المريض -->
<div class="modal fade" id="editPatientModal" tabindex="-1" aria-labelledby="editPatientModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPatientModalLabel">تعديل بيانات المريض</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('update_judicial_patient') }}" id="edit-patient-form">
                <div class="modal-body">
                    <input type="hidden" id="edit_patient_id" name="patient_id">

                    <div class="mb-3">
                        <label for="edit_patient_name" class="form-label">اسم المريض</label>
                        <input type="text" class="form-control" id="edit_patient_name" name="patient_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_patient_diagnosis" class="form-label">التشخيص</label>
                        <input type="text" class="form-control" id="edit_patient_diagnosis" name="patient_diagnosis">
                    </div>

                    <div class="mb-3">
                        <label for="edit_patient_clinic" class="form-label">العيادة</label>
                        <select class="form-select" id="edit_patient_clinic" name="clinic_id" required>
                            {% for clinic in clinics %}
                                <option value="{{ clinic.id }}">{{ clinic.branch_name }} - {{ clinic.area_name }} - {{ clinic.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="edit_court_ruling_date" class="form-label">تاريخ حكم المحكمة</label>
                        <input type="date" class="form-control" id="edit_court_ruling_date" name="court_ruling_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_treatment_start_date" class="form-label">تاريخ بداية صرف العلاج</label>
                        <input type="date" class="form-control" id="edit_treatment_start_date" name="treatment_start_date" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل الدواء -->
<div class="modal fade" id="editMedicineModal" tabindex="-1" aria-labelledby="editMedicineModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMedicineModalLabel">تعديل الدواء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('update_judicial_medicine') }}" id="edit-medicine-form">
                <div class="modal-body">
                    <input type="hidden" id="edit_medicine_id" name="medicine_id">

                    <div class="mb-3">
                        <label for="edit_diagnosis" class="form-label">التشخيص</label>
                        <input type="text" class="form-control" id="edit_diagnosis" name="diagnosis" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_medicine_name" class="form-label">اسم الدواء</label>
                        <input type="text" class="form-control" id="edit_medicine_name" name="medicine_name" required>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_unit" class="form-label">الوحدة</label>
                                <input type="text" class="form-control" id="edit_unit" name="unit" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_unit_price" class="form-label">سعر الوحدة</label>
                                <input type="number" step="0.01" class="form-control" id="edit_unit_price" name="unit_price" required onchange="calculateEditCost()">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_monthly_dose" class="form-label">الجرعة شهرياً</label>
                                <input type="number" step="0.01" class="form-control" id="edit_monthly_dose" name="monthly_dose" required onchange="calculateEditCost()">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_monthly_cost_display" class="form-label">التكلفة شهرياً</label>
                                <input type="text" class="form-control" id="edit_monthly_cost_display" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_dispense_month" class="form-label">شهر الصرف</label>
                                <input type="month" class="form-control" id="edit_dispense_month" name="dispense_month" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// تحميل بيانات المريض عند اختياره
function loadPatientData() {
    const patientId = document.getElementById('patient_id').value;
    if (patientId) {
        fetch(`/api/judicial/patient/${patientId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('court_ruling_date_display').value = data.patient.court_ruling_date;
                    document.getElementById('treatment_start_date_display').value = data.patient.treatment_start_date;
                    document.getElementById('clinic_id_hidden').value = data.patient.clinic_id;
                    // إضافة التشخيص تلقائياً
                    document.getElementById('diagnosis').value = data.patient.diagnosis || '';
                } else {
                    alert('خطأ في تحميل بيانات المريض: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في تحميل بيانات المريض');
            });
    } else {
        // مسح البيانات إذا لم يتم اختيار مريض
        document.getElementById('court_ruling_date_display').value = '';
        document.getElementById('treatment_start_date_display').value = '';
        document.getElementById('clinic_id_hidden').value = '';
        document.getElementById('diagnosis').value = '';
    }
}

// حساب التكلفة الشهرية
function calculateCost() {
    const unitPrice = parseFloat(document.getElementById('unit_price').value) || 0;
    const monthlyDose = parseFloat(document.getElementById('monthly_dose').value) || 0;
    const monthlyCost = unitPrice * monthlyDose;
    
    document.getElementById('monthly_cost_display').value = monthlyCost.toFixed(2);
}

// تعيين الشهر الحالي كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
    document.getElementById('dispense_month').value = currentMonth;

    // معالج الفلترة
    $('#apply-judicial-filter').on('click', function(e) {
        e.preventDefault();

        var clinic = $('#filter_clinic').val();
        var patient = $('#filter_patient').val();
        var month = $('#filter_month').val();

        // بناء URL مع المعاملات
        var url = "{{ url_for('judicial_dispense') }}";
        var params = [];

        if (clinic) params.push('filter_clinic=' + encodeURIComponent(clinic));
        if (patient) params.push('filter_patient=' + encodeURIComponent(patient));
        if (month) params.push('filter_month=' + encodeURIComponent(month));

        if (params.length > 0) {
            url += '?' + params.join('&');
        }

        // الانتقال إلى الصفحة المفلترة
        window.location.href = url;
    });
});

// دالة تعديل المريض
function editPatient(id, name, diagnosis, courtRulingDate, treatmentStartDate, clinicId) {
    document.getElementById('edit_patient_id').value = id;
    document.getElementById('edit_patient_name').value = name;
    document.getElementById('edit_patient_diagnosis').value = diagnosis || '';
    document.getElementById('edit_patient_clinic').value = clinicId;
    document.getElementById('edit_court_ruling_date').value = courtRulingDate;
    document.getElementById('edit_treatment_start_date').value = treatmentStartDate;

    // عرض النموذج
    var editModal = new bootstrap.Modal(document.getElementById('editPatientModal'));
    editModal.show();
}

// دالة تعديل الدواء
function editMedicine(id, diagnosis, medicineName, unit, unitPrice, monthlyDose, dispenseMonth) {
    document.getElementById('edit_medicine_id').value = id;
    document.getElementById('edit_diagnosis').value = diagnosis;
    document.getElementById('edit_medicine_name').value = medicineName;
    document.getElementById('edit_unit').value = unit;
    document.getElementById('edit_unit_price').value = unitPrice;
    document.getElementById('edit_monthly_dose').value = monthlyDose;
    document.getElementById('edit_dispense_month').value = dispenseMonth;

    // حساب التكلفة
    calculateEditCost();

    // عرض النموذج
    var editModal = new bootstrap.Modal(document.getElementById('editMedicineModal'));
    editModal.show();
}

// حساب التكلفة الشهرية في نموذج التعديل
function calculateEditCost() {
    const unitPrice = parseFloat(document.getElementById('edit_unit_price').value) || 0;
    const monthlyDose = parseFloat(document.getElementById('edit_monthly_dose').value) || 0;
    const monthlyCost = unitPrice * monthlyDose;

    document.getElementById('edit_monthly_cost_display').value = monthlyCost.toFixed(2);
}

// تحميل بيانات الدواء عند اختياره
function loadMedicineData() {
    const medicineId = document.getElementById('medicine_select').value;
    if (medicineId) {
        fetch(`/api/judicial/medicine/${medicineId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('medicine_name').value = data.medicine.name;
                    document.getElementById('unit').value = data.medicine.unit;
                } else {
                    alert('خطأ في تحميل بيانات الدواء: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في تحميل بيانات الدواء');
            });
    } else {
        // مسح البيانات إذا لم يتم اختيار دواء
        document.getElementById('medicine_name').value = '';
        document.getElementById('unit').value = '';
    }
}
</script>
{% endblock %}
