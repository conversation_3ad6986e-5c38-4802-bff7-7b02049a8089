{% extends "base.html" %}

{% block title %}إدارة أدوية الأحكام القضائية{% endblock %}

{% block extra_css %}
<style>
.table th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.85rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.btn-group .btn {
    margin: 0 1px;
}

.badge {
    font-size: 0.75rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header .header-title {
    color: white;
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    color: white !important;
    border: none;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.table tbody tr:nth-child(even) {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="/">الرئيسية</a></li>
                        <li class="breadcrumb-item active">إدارة أدوية الأحكام القضائية</li>
                    </ol>
                </div>
                <h4 class="page-title">إدارة أدوية الأحكام القضائية</h4>
            </div>
        </div>
    </div>

    <!-- عرض الرسائل -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <!-- إضافة دواء جديد -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">إضافة دواء جديد</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('manage_judicial_medicines') }}">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="mb-3">
                            <label for="medicine_name" class="form-label">اسم الدواء</label>
                            <input type="text" class="form-control" id="medicine_name" name="medicine_name" required placeholder="مثال: أملوديبين 5 مجم">
                        </div>

                        <div class="mb-3">
                            <label for="unit" class="form-label">الوحدة</label>
                            <input type="text" class="form-control" id="unit" name="unit" required placeholder="مثال: قرص، كبسولة، أمبولة">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف (اختياري)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="وصف إضافي للدواء"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-plus me-1"></i>إضافة الدواء
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- قائمة الأدوية -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="header-title">قائمة أدوية الأحكام القضائية</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">
                            <thead class="table-dark">
                                <tr class="text-center">
                                    <th style="background-color: #495057; color: white;">اسم الدواء</th>
                                    <th style="background-color: #495057; color: white;">الوحدة</th>
                                    <th style="background-color: #495057; color: white;">الوصف</th>
                                    <th style="background-color: #495057; color: white;">تاريخ الإضافة</th>
                                    <th style="background-color: #495057; color: white;">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in medicines %}
                                <tr class="align-middle">
                                    <td class="fw-bold text-primary">{{ medicine.name }}</td>
                                    <td class="text-center">
                                        <span class="badge bg-info text-dark">{{ medicine.unit }}</span>
                                    </td>
                                    <td class="text-muted">{{ medicine.description or 'غير محدد' }}</td>
                                    <td class="text-center">{{ medicine.created_at[:10] if medicine.created_at else 'غير محدد' }}</td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-warning btn-sm" onclick="editMedicine({{ medicine.id }}, '{{ medicine.name }}', '{{ medicine.unit }}', '{{ medicine.description or '' }}')" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <form method="POST" action="{{ url_for('delete_judicial_medicine_item', medicine_id=medicine.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الدواء؟')">
                                                <button type="submit" class="btn btn-danger btn-sm" title="حذف">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الدواء -->
<div class="modal fade" id="editMedicineModal" tabindex="-1" aria-labelledby="editMedicineModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMedicineModalLabel">تعديل الدواء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('manage_judicial_medicines') }}" id="edit-medicine-form">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" id="edit_medicine_id" name="medicine_id">
                    
                    <div class="mb-3">
                        <label for="edit_medicine_name" class="form-label">اسم الدواء</label>
                        <input type="text" class="form-control" id="edit_medicine_name" name="medicine_name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_unit" class="form-label">الوحدة</label>
                        <input type="text" class="form-control" id="edit_unit" name="unit" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف (اختياري)</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// دالة تعديل الدواء
function editMedicine(id, name, unit, description) {
    document.getElementById('edit_medicine_id').value = id;
    document.getElementById('edit_medicine_name').value = name;
    document.getElementById('edit_unit').value = unit;
    document.getElementById('edit_description').value = description;
    
    // عرض النموذج
    var editModal = new bootstrap.Modal(document.getElementById('editMedicineModal'));
    editModal.show();
}
</script>
{% endblock %}
