#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تثبيت المكتبات المطلوبة لتطبيق منصرف الأدوية
"""

import subprocess
import sys
import os

# إصلاح مشكلة ترميز النصوص العربية والرموز التعبيرية في Windows
if os.name == 'nt':  # Windows
    try:
        # ضبط ترميز وحدة التحكم إلى UTF-8
        import ctypes
        ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        ctypes.windll.kernel32.SetConsoleCP(65001)
    except Exception:
        pass

    # إعادة تكوين تدفقات الإخراج لاستخدام UTF-8
    try:
        # للإصدارات الحديثة من Python (3.7+)
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except AttributeError:
        # للإصدارات الأقدم من Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer,
                                      encoding='utf-8',
                                      errors='replace',
                                      line_buffering=True)

def install_package(package):
    """تثبيت مكتبة واحدة"""
    try:
        print(f"🔄 تثبيت {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل تثبيت {package}: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 تثبيت المكتبات المطلوبة لتطبيق منصرف الأدوية")
    print("=" * 60)
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        "Flask==2.2.5",
        "openpyxl==3.1.2",
        "Werkzeug==2.2.3",
        "Jinja2==3.1.2",
        "click==8.1.7",
        "itsdangerous==2.1.2",
        "MarkupSafe==2.1.3"
    ]
    
    success_count = 0
    total_count = len(required_packages)
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 النتيجة: {success_count}/{total_count} مكتبة تم تثبيتها بنجاح")
    
    if success_count == total_count:
        print("🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("✅ يمكنك الآن تشغيل التطبيق باستخدام: python run_app.py")
    else:
        print("⚠️ بعض المكتبات لم يتم تثبيتها. يرجى المحاولة مرة أخرى.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
