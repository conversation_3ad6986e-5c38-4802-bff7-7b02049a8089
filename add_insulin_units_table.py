#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإضافة جدول insulin_units لإدارة وحدات الأنسولين
"""

import sqlite3
import os

DATABASE = 'instance/medicine_dispenser.db'

def add_insulin_units_table():
    """إضافة جدول insulin_units"""
    
    if not os.path.exists(DATABASE):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    
    try:
        print("🔍 فحص جدول insulin_units...")
        
        # التحقق من وجود الجدول
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='insulin_units'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("➕ إنشاء جدول insulin_units...")
            conn.execute('''
                CREATE TABLE insulin_units (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            print("✅ تم إنشاء جدول insulin_units")
        else:
            print("✅ جدول insulin_units موجود بالفعل")
        
        conn.commit()
        
        # إضافة البيانات الافتراضية
        print("📝 إضافة الوحدات الافتراضية...")
        
        default_units = [
            ('فيال', 'فيال أنسولين'),
            ('قلم', 'قلم أنسولين'),
            ('خرطوشة', 'خرطوشة أنسولين'),
            ('علبة', 'علبة أنسولين'),
            ('أمبولة', 'أمبولة أنسولين'),
            ('حقنة معبأة', 'حقنة معبأة مسبقاً')
        ]
        
        for unit_name, unit_desc in default_units:
            conn.execute(
                'INSERT OR IGNORE INTO insulin_units (name, description) VALUES (?, ?)',
                (unit_name, unit_desc)
            )
        
        conn.commit()
        print("✅ تم إضافة الوحدات الافتراضية")
        
        # عرض النتيجة النهائية
        print("\n📊 الوحدات المتاحة:")
        units = conn.execute('SELECT id, name, description FROM insulin_units ORDER BY id').fetchall()
        for unit in units:
            print(f"  {unit['id']}: {unit['name']} - {unit['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_units_table():
    """التحقق من نجاح إضافة جدول الوحدات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    
    try:
        # اختبار استعلام على الجدول الجديد
        result = conn.execute('SELECT COUNT(*) as count FROM insulin_units').fetchone()
        if result and result['count'] > 0:
            print(f"✅ جدول الوحدات يعمل بشكل صحيح - يحتوي على {result['count']} وحدة")
            return True
        else:
            print("⚠️ جدول الوحدات فارغ")
            return True
    except Exception as e:
        print(f"❌ فشل التحقق من جدول الوحدات: {e}")
        return False
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 بدء إضافة جدول وحدات الأنسولين...")
    print("=" * 50)
    
    if add_insulin_units_table():
        print("\n🔍 التحقق من الجدول الجديد...")
        if verify_units_table():
            print("\n🎉 تم إضافة جدول الوحدات بنجاح!")
        else:
            print("\n❌ فشل التحقق من الجدول.")
    else:
        print("\n❌ فشل إضافة الجدول.")
    
    print("=" * 50)
