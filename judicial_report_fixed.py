# تقرير الأحكام القضائية
@app.route('/judicial/report', methods=['GET', 'POST'])
def judicial_report():
    """تقرير الأحكام القضائية"""
    conn = get_db_connection()

    # جلب البيانات الأساسية للفلاتر وتحويلها إلى قواميس
    branches_rows = conn.execute('SELECT * FROM branches ORDER BY name').fetchall()
    branches = [dict(row) for row in branches_rows]

    areas_rows = conn.execute('''
        SELECT areas.*, branches.name as branch_name
        FROM areas
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name
    ''').fetchall()
    areas = [dict(row) for row in areas_rows]

    clinics_rows = conn.execute('''
        SELECT clinics.*, areas.name as area_name, branches.name as branch_name
        FROM clinics
        JOIN areas ON clinics.area_id = areas.id
        JOIN branches ON areas.branch_id = branches.id
        ORDER BY branches.name, areas.name, clinics.name
    ''').fetchall()
    clinics = [dict(row) for row in clinics_rows]

    report_data = []
    report_title = "تقرير الأحكام القضائية"
    period_desc = "جميع الفترات"

    # تعيين القيم الافتراضية
    scope = request.form.get('scope', 'all') if request.method == 'POST' else 'all'
    scope_id = request.form.get('scope_id') if request.method == 'POST' else None
    report_type = request.form.get('report_type', 'medicine') if request.method == 'POST' else 'medicine'
    detail_level = request.form.get('detail_level', 'summary') if request.method == 'POST' else 'summary'
    date_range = request.form.get('date_range', 'month') if request.method == 'POST' else 'month'
    start_date = request.form.get('start_date') if request.method == 'POST' else None
    end_date = request.form.get('end_date') if request.method == 'POST' else None

    # بناء شروط الفلترة (يعمل لكل من GET و POST)
    where_conditions = []
    params = []

    # فلترة النطاق
    if scope == 'branch' and scope_id:
        where_conditions.append("b.id = ?")
        params.append(scope_id)
        branch_info = conn.execute("SELECT name FROM branches WHERE id = ?", (scope_id,)).fetchone()
        if branch_info:
            report_title += f" - {branch_info['name']}"
    elif scope == 'area' and scope_id:
        where_conditions.append("a.id = ?")
        params.append(scope_id)
        area_info = conn.execute("""
            SELECT areas.name, branches.name as branch_name
            FROM areas
            JOIN branches ON areas.branch_id = branches.id
            WHERE areas.id = ?
        """, (scope_id,)).fetchone()
        if area_info:
            report_title += f" - {area_info['branch_name']} - {area_info['name']}"
    elif scope == 'clinic' and scope_id:
        where_conditions.append("c.id = ?")
        params.append(scope_id)
        clinic_info = conn.execute("""
            SELECT clinics.name, areas.name as area_name, branches.name as branch_name
            FROM clinics
            JOIN areas ON clinics.area_id = areas.id
            JOIN branches ON areas.branch_id = branches.id
            WHERE clinics.id = ?
        """, (scope_id,)).fetchone()
        if clinic_info:
            report_title += f" - {clinic_info['branch_name']} - {clinic_info['area_name']} - {clinic_info['name']}"

    # معالجة الفترات الزمنية
    current_date = datetime.now()

    if date_range == 'month':
        # الشهر الحالي
        start_of_month = current_date.replace(day=1).strftime('%Y-%m')
        where_conditions.append("jd.dispense_month = ?")
        params.append(start_of_month)
        period_desc = f"الشهر الحالي ({start_of_month})"

    elif date_range == 'current_and_previous_month':
        # الشهر الحالي والسابق
        current_month = current_date.strftime('%Y-%m')
        prev_month = (current_date.replace(day=1) - timedelta(days=1)).strftime('%Y-%m')
        where_conditions.append("jd.dispense_month IN (?, ?)")
        params.extend([current_month, prev_month])
        period_desc = f"الشهر الحالي والسابق ({prev_month} - {current_month})"

    elif date_range == 'last_3_months':
        # آخر 3 أشهر
        months = []
        for i in range(3):
            month_date = current_date.replace(day=1) - timedelta(days=i*30)
            months.append(month_date.strftime('%Y-%m'))
        placeholders = ','.join(['?' for _ in months])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(months)
        period_desc = f"آخر 3 أشهر ({months[-1]} - {months[0]})"

    elif date_range == 'last_6_months':
        # آخر 6 أشهر
        months = []
        for i in range(6):
            month_date = current_date.replace(day=1) - timedelta(days=i*30)
            months.append(month_date.strftime('%Y-%m'))
        placeholders = ','.join(['?' for _ in months])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(months)
        period_desc = f"آخر 6 أشهر ({months[-1]} - {months[0]})"

    elif date_range == 'quarter':
        # الربع الحالي
        quarter = (current_date.month - 1) // 3 + 1
        quarter_months = [(quarter - 1) * 3 + i + 1 for i in range(3)]
        quarter_months_str = [f"{current_date.year}-{str(m).zfill(2)}" for m in quarter_months]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع {quarter} من {current_date.year}"

    elif date_range == 'q1':
        # الربع الأول
        quarter_months_str = [f"{current_date.year}-01", f"{current_date.year}-02", f"{current_date.year}-03"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الأول من {current_date.year}"

    elif date_range == 'q2':
        # الربع الثاني
        quarter_months_str = [f"{current_date.year}-04", f"{current_date.year}-05", f"{current_date.year}-06"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الثاني من {current_date.year}"

    elif date_range == 'q3':
        # الربع الثالث
        quarter_months_str = [f"{current_date.year}-07", f"{current_date.year}-08", f"{current_date.year}-09"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الثالث من {current_date.year}"

    elif date_range == 'q4':
        # الربع الرابع
        quarter_months_str = [f"{current_date.year}-10", f"{current_date.year}-11", f"{current_date.year}-12"]
        placeholders = ','.join(['?' for _ in quarter_months_str])
        where_conditions.append(f"jd.dispense_month IN ({placeholders})")
        params.extend(quarter_months_str)
        period_desc = f"الربع الرابع من {current_date.year}"

    elif date_range == 'year':
        # السنة الحالية
        where_conditions.append("jd.dispense_month LIKE ?")
        params.append(f'{current_date.year}-%')
        period_desc = f"السنة الحالية ({current_date.year})"

    elif date_range == 'custom' and start_date and end_date:
        # فترة مخصصة
        start_month = start_date[:7] if len(start_date) >= 7 else start_date
        end_month = end_date[:7] if len(end_date) >= 7 else end_date
        where_conditions.append("jd.dispense_month BETWEEN ? AND ?")
        params.extend([start_month, end_month])

        # تحسين عرض الفترة
        try:
            from datetime import datetime
            start_dt = datetime.strptime(start_month, '%Y-%m')
            end_dt = datetime.strptime(end_month, '%Y-%m')
            start_formatted = start_dt.strftime('%m/%Y')
            end_formatted = end_dt.strftime('%m/%Y')
            period_desc = f"من {start_formatted} إلى {end_formatted}"
        except:
            period_desc = f"من {start_month} إلى {end_month}"
    else:
        period_desc = "جميع الفترات"

    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

    # تنفيذ التقرير حسب النوع
    if report_type == 'medicine':
        if detail_level == 'summary':
            # تقرير الأدوية الإجمالي
            report_data = conn.execute(f"""
                SELECT
                    jd.medicine_name,
                    COUNT(DISTINCT jd.patient_id) as case_count,
                    SUM(jd.monthly_dose) as total_quantity,
                    SUM(jd.monthly_cost) as total_cost,
                    AVG(jd.monthly_cost) as avg_cost
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                GROUP BY jd.medicine_name
                ORDER BY total_cost DESC
            """, params).fetchall()
            report_title += " - تكلفة الأدوية (إجمالي)"
        else:
            # تقرير الأدوية التفصيلي
            report_data = conn.execute(f"""
                SELECT
                    jd.medicine_name,
                    jd.unit,
                    jd.monthly_dose as quantity,
                    jp.name as patient_name,
                    jd.monthly_cost as cost,
                    jd.unit_price as avg_cost,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                ORDER BY jd.medicine_name, jp.name
            """, params).fetchall()
            report_title += " - تكلفة الأدوية (تفصيلي)"

    elif report_type == 'patient':
        if detail_level == 'summary':
            # تقرير المرضى الإجمالي
            report_data = conn.execute(f"""
                SELECT
                    jp.name as patient_name,
                    jd.medicine_name,
                    SUM(jd.monthly_dose) as total_quantity,
                    SUM(jd.monthly_cost) as total_cost
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                GROUP BY jp.id, jd.medicine_name
                ORDER BY jp.name, jd.medicine_name
            """, params).fetchall()
            report_title += " - بيانات المرضى (إجمالي)"
        else:
            # تقرير المرضى التفصيلي
            report_data = conn.execute(f"""
                SELECT
                    jp.name as patient_name,
                    jp.diagnosis,
                    jd.medicine_name,
                    jd.unit,
                    jd.monthly_dose as quantity,
                    jd.monthly_cost as cost,
                    c.name as clinic_name,
                    a.name as area_name,
                    b.name as branch_name
                FROM judicial_dispensed jd
                JOIN judicial_patients jp ON jd.patient_id = jp.id
                JOIN clinics c ON jd.clinic_id = c.id
                JOIN areas a ON c.area_id = a.id
                JOIN branches b ON a.branch_id = b.id
                WHERE {where_clause}
                ORDER BY jp.name, jd.medicine_name
            """, params).fetchall()
            report_title += " - بيانات المرضى (تفصيلي)"

    conn.close()

    return render_template('judicial_report.html',
                         branches=branches,
                         areas=areas,
                         clinics=clinics,
                         report_data=report_data,
                         report_title=report_title,
                         period_desc=period_desc,
                         request=request)
