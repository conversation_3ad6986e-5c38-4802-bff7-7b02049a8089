#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإضافة عمود الوحدة إلى جدول الأدوية وإزالة عمود تاريخ الصلاحية
"""

import sqlite3
import os

DATABASE = 'instance/medicine_dispenser.db'

def add_unit_column_to_drugs():
    """إضافة عمود الوحدة إلى جدول الأدوية"""
    
    if not os.path.exists(DATABASE):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    
    try:
        print("🔍 فحص جدول drugs...")
        
        # التحقق من وجود عمود unit
        cursor = conn.execute("PRAGMA table_info(drugs)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'unit' not in columns:
            print("➕ إضافة عمود unit إلى جدول drugs...")
            conn.execute('ALTER TABLE drugs ADD COLUMN unit TEXT')
            print("✅ تم إضافة عمود unit")
        else:
            print("✅ عمود unit موجود بالفعل")
        
        # التحقق من وجود عمود expiry_date وإزالته إذا كان موجوداً
        if 'expiry_date' in columns:
            print("🗑️ إزالة عمود expiry_date...")
            
            # إنشاء جدول مؤقت بدون عمود expiry_date
            conn.execute('''
                CREATE TABLE drugs_temp (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    scientific_name TEXT,
                    category_id INTEGER NOT NULL,
                    unit TEXT,
                    FOREIGN KEY (category_id) REFERENCES drug_categories (id) ON DELETE CASCADE
                )
            ''')
            
            # نسخ البيانات إلى الجدول المؤقت
            conn.execute('''
                INSERT INTO drugs_temp (id, name, scientific_name, category_id, unit)
                SELECT id, name, scientific_name, category_id, unit FROM drugs
            ''')
            
            # حذف الجدول الأصلي
            conn.execute('DROP TABLE drugs')
            
            # إعادة تسمية الجدول المؤقت
            conn.execute('ALTER TABLE drugs_temp RENAME TO drugs')
            
            print("✅ تم إزالة عمود expiry_date")
        else:
            print("✅ عمود expiry_date غير موجود")
        
        conn.commit()
        
        # إضافة بعض الوحدات الافتراضية للأدوية الموجودة
        print("📝 تحديث الوحدات للأدوية الموجودة...")
        
        # الحصول على الأدوية التي لا تحتوي على وحدة
        drugs_without_unit = conn.execute('SELECT id, name FROM drugs WHERE unit IS NULL OR unit = ""').fetchall()
        
        if drugs_without_unit:
            print(f"🔄 تحديث {len(drugs_without_unit)} دواء...")
            
            # تحديث الأدوية بوحدات افتراضية بناءً على نوع الدواء
            for drug in drugs_without_unit:
                drug_name = drug['name'].lower()
                unit = 'قرص'  # الوحدة الافتراضية
                
                # تحديد الوحدة بناءً على اسم الدواء
                if any(word in drug_name for word in ['شراب', 'سيرب', 'محلول']):
                    unit = 'زجاجة'
                elif any(word in drug_name for word in ['كريم', 'مرهم', 'جل']):
                    unit = 'أنبوب'
                elif any(word in drug_name for word in ['حقن', 'أمبول', 'فيال']):
                    unit = 'أمبولة'
                elif any(word in drug_name for word in ['كبسولة', 'كبسول']):
                    unit = 'كبسولة'
                elif any(word in drug_name for word in ['نقط', 'قطرة']):
                    unit = 'زجاجة'
                
                conn.execute('UPDATE drugs SET unit = ? WHERE id = ?', (unit, drug['id']))
            
            conn.commit()
            print("✅ تم تحديث الوحدات للأدوية الموجودة")
        else:
            print("✅ جميع الأدوية تحتوي على وحدات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_drugs_table():
    """التحقق من بنية جدول الأدوية"""
    
    if not os.path.exists(DATABASE):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    
    try:
        print("\n📊 بنية جدول drugs:")
        cursor = conn.execute("PRAGMA table_info(drugs)")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"  {column[1]} ({column[2]}) - {'NOT NULL' if column[3] else 'NULL'}")
        
        # عرض عينة من البيانات
        print("\n📋 عينة من الأدوية:")
        drugs = conn.execute('SELECT id, name, unit FROM drugs LIMIT 5').fetchall()
        for drug in drugs:
            print(f"  {drug['id']}: {drug['name']} - {drug['unit'] or 'بدون وحدة'}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 بدء تحديث جدول الأدوية...")
    print("=" * 50)
    
    if add_unit_column_to_drugs():
        print("\n🔍 التحقق من الجدول المحدث...")
        if verify_drugs_table():
            print("\n🎉 تم تحديث جدول الأدوية بنجاح!")
        else:
            print("\n❌ فشل التحقق من الجدول.")
    else:
        print("\n❌ فشل تحديث الجدول.")
    
    print("=" * 50)
