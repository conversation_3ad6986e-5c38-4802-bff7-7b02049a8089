#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت لإصلاح جدول insulin_codes بإضافة الأعمدة المفقودة
بدون فقدان البيانات الموجودة
"""

import sqlite3
import os

DATABASE = 'instance/medicine_dispenser.db'

def fix_insulin_codes_table():
    """إضافة الأعمدة المفقودة لجدول insulin_codes"""
    
    if not os.path.exists(DATABASE):
        print("❌ قاعدة البيانات غير موجودة!")
        return False
    
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    
    try:
        print("🔍 فحص جدول insulin_codes...")
        
        # فحص الأعمدة الموجودة
        cursor = conn.execute("PRAGMA table_info(insulin_codes)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"الأعمدة الموجودة: {columns}")
        
        # التحقق من وجود عمود unit
        if 'unit' not in columns:
            print("➕ إضافة عمود unit...")
            conn.execute('ALTER TABLE insulin_codes ADD COLUMN unit TEXT')
            print("✅ تم إضافة عمود unit")
        else:
            print("✅ عمود unit موجود بالفعل")
        
        # التحقق من وجود عمود type
        if 'type' not in columns:
            print("➕ إضافة عمود type...")
            conn.execute('ALTER TABLE insulin_codes ADD COLUMN type TEXT')
            print("✅ تم إضافة عمود type")
        else:
            print("✅ عمود type موجود بالفعل")
        
        conn.commit()
        
        # تحديث البيانات الموجودة بقيم افتراضية
        print("🔄 تحديث البيانات الموجودة...")
        
        # جلب البيانات الحالية
        existing_codes = conn.execute('SELECT id, name FROM insulin_codes').fetchall()
        
        for code in existing_codes:
            # تحديد الوحدة والنوع بناءً على الاسم
            unit = 'قلم'  # افتراضي
            type_name = 'أنسولين سريع المفعول'  # افتراضي
            
            if 'لانتوس' in code['name']:
                unit = 'فيال'
                type_name = 'أنسولين طويل المفعول'
            elif 'هيومالوج' in code['name']:
                unit = 'خرطوشة'
                type_name = 'أنسولين سريع المفعول'
            elif 'ليفيمير' in code['name']:
                unit = 'قلم'
                type_name = 'أنسولين متوسط المفعول'
            elif 'نوفورابيد' in code['name']:
                unit = 'قلم'
                type_name = 'أنسولين سريع المفعول'
            
            # تحديث السجل فقط إذا كانت القيم فارغة
            conn.execute('''
                UPDATE insulin_codes 
                SET unit = COALESCE(unit, ?), type = COALESCE(type, ?)
                WHERE id = ?
            ''', (unit, type_name, code['id']))
        
        conn.commit()
        print("✅ تم تحديث جميع البيانات بنجاح")
        
        # عرض النتيجة النهائية
        print("\n📊 البيانات النهائية:")
        final_codes = conn.execute('SELECT id, code, name, unit, type FROM insulin_codes').fetchall()
        for code in final_codes:
            print(f"  {code['id']}: {code['code']} - {code['name']} ({code['unit']}) - {code['type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def verify_fix():
    """التحقق من نجاح الإصلاح"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    
    try:
        # اختبار استعلام يستخدم الأعمدة الجديدة
        result = conn.execute('SELECT id, code, name, unit, type FROM insulin_codes LIMIT 1').fetchone()
        if result:
            print("✅ الإصلاح نجح - يمكن الوصول للأعمدة الجديدة")
            return True
        else:
            print("⚠️ لا توجد بيانات في الجدول")
            return True
    except Exception as e:
        print(f"❌ الإصلاح فشل: {e}")
        return False
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 بدء إصلاح جدول insulin_codes...")
    print("=" * 50)
    
    if fix_insulin_codes_table():
        print("\n🔍 التحقق من الإصلاح...")
        if verify_fix():
            print("\n🎉 تم الإصلاح بنجاح! يمكنك الآن تشغيل التطبيق.")
        else:
            print("\n❌ فشل التحقق من الإصلاح.")
    else:
        print("\n❌ فشل الإصلاح.")
    
    print("=" * 50)
