{% extends "base.html" %}

{% block title %}تفريغ متقدم{% endblock %}

{% block styles %}
<style>
.icon-large {
    font-size: 24px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4><i class="mdi mdi-tune-vertical me-2"></i>تفريغ متقدم للبيانات</h4>
                    <p class="mb-0">اختر البيانات المراد تفريغها بدقة</p>
                </div>
                <div class="card-body">
                    
                    <!-- تفريغ حسب التصنيف -->
                    <div class="mb-5">
                        <div class="d-flex align-items-center mb-3">
                            <i class="mdi mdi-tag-multiple text-primary me-2 icon-large"></i>
                            <h5 class="text-primary mb-0">تفريغ حسب التصنيف</h5>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="mdi mdi-information me-2"></i>
                            عدد التصنيفات المتوفرة: <strong>{{ categories|length }}</strong>
                        </div>
                        
                        {% if categories %}
                        <form method="POST" action="/admin/clear-data" onsubmit="return confirmClear('categories')">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="categories">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">
                            
                            <div class="row">
                                {% for category in categories %}
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="card border-primary">
                                        <div class="card-body p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="selected_categories" value="{{ category.id }}" 
                                                       id="cat_{{ category.id }}">
                                                <label class="form-check-label w-100" for="cat_{{ category.id }}">
                                                    <strong>{{ category.name }}</strong><br>
                                                    <small class="text-muted">
                                                        📦 {{ category.drugs_count }} دواء<br>
                                                        📋 {{ category.dispensed_count }} سجل صرف
                                                    </small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="selectAllCategories()">
                                    <i class="mdi mdi-check-all me-1"></i>تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearAllCategories()">
                                    <i class="mdi mdi-close-circle me-1"></i>إلغاء التحديد
                                </button>
                                <button type="submit" class="btn btn-warning">
                                    <i class="mdi mdi-delete me-2"></i>تفريغ التصنيفات المحددة
                                </button>
                            </div>
                        </form>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="mdi mdi-alert-triangle me-2"></i>
                            لا توجد تصنيفات متوفرة حالياً
                        </div>
                        {% endif %}
                    </div>

                    <hr class="my-4">

                    <!-- تفريغ حسب نوع الأنسولين -->
                    <div class="mb-5">
                        <div class="d-flex align-items-center mb-3">
                            <i class="mdi mdi-needle text-danger me-2 icon-large"></i>
                            <h5 class="text-danger mb-0">تفريغ حسب نوع الأنسولين</h5>
                        </div>

                        <div class="alert alert-info">
                            <i class="mdi mdi-information me-2"></i>
                            عدد أنواع الأنسولين المتوفرة: <strong>{{ insulin_types|length if insulin_types else 0 }}</strong>
                        </div>

                        {% if insulin_types and insulin_types|length > 0 %}
                        <form method="POST" action="/admin/clear-data" onsubmit="return confirmClear('insulin_types')">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="insulin_types">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">

                            <div class="row">
                                {% for type in insulin_types %}
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="card border-danger">
                                        <div class="card-body p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                       name="selected_insulin_types" value="{{ type.name }}"
                                                       id="insulin_type_{{ type.id }}">
                                                <label class="form-check-label w-100" for="insulin_type_{{ type.id }}">
                                                    <strong>{{ type.name }}</strong><br>
                                                    <small class="text-muted">
                                                        💉 {{ type.insulin_count }} سجل أنسولين
                                                    </small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>

                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-danger btn-sm me-2" onclick="selectAllInsulinTypes()">
                                    <i class="mdi mdi-check-all me-1"></i>تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearAllInsulinTypes()">
                                    <i class="mdi mdi-close-circle me-1"></i>إلغاء التحديد
                                </button>
                                <button type="submit" class="btn btn-danger">
                                    <i class="mdi mdi-delete me-1"></i>تفريغ الأنواع المحددة
                                </button>
                            </div>
                        </form>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="mdi mdi-alert-triangle me-2"></i>
                            لا توجد أنواع أنسولين متوفرة حالياً
                        </div>
                        {% endif %}
                    </div>

                    <hr class="my-4">

                    <!-- تفريغ حسب العيادة -->
                    <div class="mb-5">
                        <div class="d-flex align-items-center mb-3">
                            <i class="mdi mdi-hospital-building text-success me-2 icon-large"></i>
                            <h5 class="text-success mb-0">تفريغ حسب العيادة</h5>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="mdi mdi-information me-2"></i>
                            عدد العيادات المتوفرة: <strong>{{ clinics|length }}</strong>
                        </div>
                        
                        {% if clinics %}
                        <form method="POST" action="/admin/clear-data" onsubmit="return confirmClear('clinics')">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="clinics">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">
                            
                            <div class="row">
                                {% for clinic in clinics %}
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="card border-success">
                                        <div class="card-body p-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="selected_clinics" value="{{ clinic.id }}" 
                                                       id="clinic_{{ clinic.id }}">
                                                <label class="form-check-label w-100" for="clinic_{{ clinic.id }}">
                                                    <strong>{{ clinic.name }}</strong><br>
                                                    <small class="text-muted">
                                                        📍 {{ clinic.area_name }}<br>
                                                        💊 {{ clinic.dispensed_count }} سجل أدوية<br>
                                                        💉 {{ clinic.insulin_count }} سجل أنسولين
                                                    </small>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-success btn-sm me-2" onclick="selectAllClinics()">
                                    <i class="mdi mdi-check-all me-1"></i>تحديد الكل
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearAllClinics()">
                                    <i class="mdi mdi-close-circle me-1"></i>إلغاء التحديد
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="mdi mdi-delete me-2"></i>تفريغ العيادات المحددة
                                </button>
                            </div>
                        </form>
                        {% else %}
                        <div class="alert alert-warning">
                            <i class="mdi mdi-alert-triangle me-2"></i>
                            لا توجد عيادات متوفرة حالياً
                        </div>
                        {% endif %}
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- تفريغ حسب الفترة الزمنية -->
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <i class="mdi mdi-calendar-range text-warning me-2 icon-large"></i>
                            <h5 class="text-warning mb-0">تفريغ حسب الفترة الزمنية</h5>
                        </div>
                        
                        <form method="POST" action="/admin/clear-data" onsubmit="return confirmClear('date_range')">
                            <input type="hidden" name="clear_type" value="advanced">
                            <input type="hidden" name="advanced_type" value="date_range">
                            <input type="hidden" name="confirm_text" value="تأكيد الحذف">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label" for="start_date">من شهر:</label>
                                    <input type="month" class="form-control month-input" id="start_date" name="start_date" required title="اختر شهر البداية" aria-label="شهر البداية" placeholder="YYYY-MM">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label" for="end_date">إلى شهر:</label>
                                    <input type="month" class="form-control month-input" id="end_date" name="end_date" required title="اختر شهر النهاية" aria-label="شهر النهاية" placeholder="YYYY-MM">
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <button type="submit" class="btn btn-warning">
                                    <i class="mdi mdi-delete me-2"></i>تفريغ الفترة المحددة
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- أزرار التنقل -->
                    <div class="text-center mt-4 pt-3 border-top">
                        <a href="/admin/data-management" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left me-2"></i>العودة لإدارة البيانات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAllCategories() {
    document.querySelectorAll('input[name="selected_categories"]').forEach(cb => cb.checked = true);
}

function clearAllCategories() {
    document.querySelectorAll('input[name="selected_categories"]').forEach(cb => cb.checked = false);
}

function selectAllInsulinTypes() {
    document.querySelectorAll('input[name="selected_insulin_types"]').forEach(cb => cb.checked = true);
}

function clearAllInsulinTypes() {
    document.querySelectorAll('input[name="selected_insulin_types"]').forEach(cb => cb.checked = false);
}

function selectAllClinics() {
    document.querySelectorAll('input[name="selected_clinics"]').forEach(cb => cb.checked = true);
}

function clearAllClinics() {
    document.querySelectorAll('input[name="selected_clinics"]').forEach(cb => cb.checked = false);
}

function confirmClear(type) {
    let selectedItems = [];
    let message = '';
    
    if (type === 'categories') {
        selectedItems = document.querySelectorAll('input[name="selected_categories"]:checked');
        if (selectedItems.length === 0) {
            alert('يجب اختيار تصنيف واحد على الأقل');
            return false;
        }
        message = `هل تريد تفريغ بيانات ${selectedItems.length} تصنيف محدد؟\n\nسيتم حذف جميع سجلات الصرف لهذه التصنيفات نهائياً.`;
    } else if (type === 'insulin_types') {
        selectedItems = document.querySelectorAll('input[name="selected_insulin_types"]:checked');
        if (selectedItems.length === 0) {
            alert('يجب اختيار نوع أنسولين واحد على الأقل');
            return false;
        }
        message = `هل تريد تفريغ بيانات ${selectedItems.length} نوع أنسولين محدد؟\n\nسيتم حذف جميع سجلات الأنسولين لهذه الأنواع نهائياً.`;
    } else if (type === 'clinics') {
        selectedItems = document.querySelectorAll('input[name="selected_clinics"]:checked');
        if (selectedItems.length === 0) {
            alert('يجب اختيار عيادة واحدة على الأقل');
            return false;
        }
        message = `هل تريد تفريغ بيانات ${selectedItems.length} عيادة محددة؟\n\nسيتم حذف جميع سجلات الصرف لهذه العيادات نهائياً.`;
    } else if (type === 'date_range') {
        const startDate = document.querySelector('input[name="start_date"]').value;
        const endDate = document.querySelector('input[name="end_date"]').value;
        if (!startDate || !endDate) {
            alert('يجب تحديد تاريخ البداية والنهاية');
            return false;
        }
        message = `هل تريد تفريغ بيانات الفترة من ${startDate} إلى ${endDate}؟\n\nسيتم حذف جميع سجلات الصرف في هذه الفترة نهائياً.`;
    }
    
    message += '\n\n⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!\n✅ سيتم إنشاء نسخة احتياطية تلقائياً قبل التفريغ.';
    
    return confirm(message);
}

// دعم المتصفحات التي لا تدعم input[type=month]
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من دعم input[type=month]
    var testInput = document.createElement('input');
    testInput.type = 'month';

    if (testInput.type !== 'month') {
        // إذا لم يكن مدعوماً، تحويل إلى text مع pattern
        var monthInputs = document.querySelectorAll('.month-input');
        monthInputs.forEach(function(input) {
            input.type = 'text';
            input.pattern = '[0-9]{4}-[0-9]{2}';
            input.placeholder = 'YYYY-MM (مثال: 2025-01)';
            input.title = 'أدخل التاريخ بصيغة YYYY-MM (مثال: 2025-01)';
        });
    }
});
</script>
{% endblock %}
