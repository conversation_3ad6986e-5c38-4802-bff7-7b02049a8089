{% extends "base.html" %}

{% block title %}صرف الأدوية - تطبيق منصرف الأدوية{% endblock %}

{% block styles %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        border-radius: 20px 20px 0 0 !important;
        border: none !important;
        padding: 20px 30px;
    }

    .section-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .section-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }

    .section-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 15px 20px;
        margin: -1px -1px 20px -1px;
        border-radius: 15px 15px 0 0;
    }

    .drug-section .section-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .list-section .section-header {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e3e6f0;
        padding: 12px 15px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-success {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(17, 153, 142, 0.3);
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
        background: linear-gradient(135deg, #0d8377 0%, #2dd36f 100%);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 15px 40px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        border: none;
        border-radius: 25px;
        padding: 15px 40px;
        font-weight: 600;
        color: #333;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(168, 237, 234, 0.3);
    }

    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(168, 237, 234, 0.4);
        color: #333;
    }

    .btn-danger {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .btn-danger:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
    }

    .btn-warning {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        border-radius: 20px;
        transition: all 0.3s ease;
        color: white;
        font-weight: 600;
    }

    .btn-warning:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
    }

    .table-warning {
        background-color: rgba(255, 193, 7, 0.1) !important;
        border: 2px solid #ffc107;
    }

    .table {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
        text-align: center;
    }

    /* تمييز بصري للعناصر المكررة */
    .duplicate-item {
        background-color: #ffebee !important;
        border: 2px solid #f44336 !important;
        animation: duplicateWarning 2s ease-in-out;
    }

    .duplicate-item td {
        color: #c62828 !important;
        font-weight: 600;
    }

    @keyframes duplicateWarning {
        0%, 100% { background-color: #ffebee; }
        50% { background-color: #ffcdd2; }
    }

    .duplicate-warning {
        background: linear-gradient(135deg, #ff5722 0%, #f44336 100%);
        color: white;
        padding: 10px 15px;
        border-radius: 8px;
        margin: 10px 0;
        display: none;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .duplicate-icon {
        color: #f44336;
        font-size: 18px;
        margin-left: 5px;
    }

    /* تمييز السجلات المحمية (أكثر من 4 أشهر) */
    .protected-record {
        background-color: #f8f9fa !important;
        opacity: 0.7;
    }

    .protected-record td {
        color: #6c757d !important;
    }

    .lock-icon {
        color: #dc3545;
        font-size: 14px;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.02);
    }

    .table tbody td {
        padding: 15px;
        text-align: center;
        border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .badge {
        border-radius: 20px;
        padding: 8px 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .total-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(17, 153, 142, 0.3);
        transition: all 0.3s ease;
    }

    .total-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(17, 153, 142, 0.4);
    }

    .history-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 30px;
    }

    .history-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 20px 30px;
        border-radius: 20px 20px 0 0;
        border: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .history-header .btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        transition: all 0.3s ease;
    }

    .history-header .btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
        transform: translateY(-2px);
    }

    #historyFilters .card-body {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        border: 1px solid #dee2e6;
        margin: 15px;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #historyFilters .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }

    #historyFilters .form-select,
    #historyFilters .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 10px 15px;
        transition: all 0.3s ease;
        background: white;
    }

    #historyFilters .form-select:focus,
    #historyFilters .form-control:focus {
        border-color: #11998e;
        box-shadow: 0 0 0 0.2rem rgba(17, 153, 142, 0.25);
        transform: translateY(-2px);
    }

    #historyFilters .btn-primary {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    #historyFilters .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
    }

    #historyFilters .btn-outline-secondary {
        border: 2px solid #6c757d;
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    #historyFilters .btn-outline-secondary:hover {
        transform: translateY(-2px);
        background: #6c757d;
        border-color: #6c757d;
    }

    .floating-label {
        position: relative;
        margin-bottom: 20px;
    }

    .floating-label .form-control,
    .floating-label .form-select {
        padding-top: 20px;
        padding-bottom: 5px;
    }

    .floating-label label {
        position: absolute;
        top: 15px;
        right: 15px;
        transition: all 0.3s ease;
        color: #6c757d;
        pointer-events: none;
        background: white;
        padding: 0 5px;
        border-radius: 5px;
    }

    .floating-label .form-control:focus + label,
    .floating-label .form-control:not(:placeholder-shown) + label,
    .floating-label .form-select:focus + label,
    .floating-label .form-select:not([value=""]) + label {
        top: -8px;
        font-size: 0.8rem;
        color: #667eea;
        font-weight: 600;
    }

    .icon-input {
        position: relative;
    }

    .icon-input i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #667eea;
        z-index: 10;
    }

    .icon-input .form-control,
    .icon-input .form-select {
        padding-left: 45px;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .section-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .section-card:nth-child(2) {
        animation-delay: 0.2s;
    }

    .section-card:nth-child(3) {
        animation-delay: 0.4s;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="main-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="mb-0 fw-bold text-white">
                            <i class="mdi mdi-pill me-3"></i>صرف الأدوية
                        </h3>
                        <div>
                            <a href="{{ url_for('manage_drugs_new') }}" class="btn btn-light me-2">
                                <i class="mdi mdi-pill me-1"></i>إدارة الأدوية
                            </a>
                            <a href="/" class="btn btn-light">
                                <i class="mdi mdi-arrow-left me-1"></i>العودة
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <form id="drug-dispense-form" method="POST" action="/dispense/new">
                        <input type="hidden" id="selected_clinic_id" name="clinic_id" value="">
                        <div class="row g-4">
                            <!-- بيانات المنصرف -->
                            <div class="col-lg-4">
                                <div class="section-card mb-4">
                                    <div class="section-header">
                                        <h6 class="mb-0 fw-bold"><i class="mdi mdi-information me-2"></i>بيانات المنصرف</h6>
                                    </div>
                                    <div class="p-3">
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-hospital-building"></i>
                                            <select class="form-select" id="clinic_id" name="clinic_id" required>
                                                <option value="">-- اختر العيادة --</option>
                                                {% for clinic in clinics %}
                                                <option value="{{ clinic.id }}" {% if selected_clinic_id and clinic.id|string == selected_clinic_id %}selected{% endif %}>{{ clinic.name }} ({{ clinic.area_name }} - {{ clinic.branch_name }})</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-calendar"></i>
                                            <input type="month" class="form-control" id="dispense_month" name="dispense_month" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- بيانات الدواء -->
                                <div class="section-card drug-section">
                                    <div class="section-header">
                                        <h6 class="mb-0 fw-bold"><i class="mdi mdi-pill me-2"></i>بيانات الدواء</h6>
                                    </div>
                                    <div class="p-3">
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-tag-multiple"></i>
                                            <select class="form-select" id="category_id" required>
                                                <option value="">-- اختر التصنيف --</option>
                                                {% for category in categories %}
                                                <option value="{{ category.id }}">{{ category.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-pill"></i>
                                            <select class="form-select" id="drug_id" required disabled>
                                                <option value="">-- اختر التصنيف أولاً --</option>
                                            </select>
                                        </div>
                                        <div class="icon-input mb-3">
                                            <i class="mdi mdi-package-variant"></i>
                                            <input type="text" class="form-control" id="drug_unit" placeholder="الوحدة" readonly style="background-color: #f8f9fa;">
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-package-variant"></i>
                                                    <input type="number" step="0.01" min="0.01" class="form-control" id="quantity" placeholder="الكمية" required>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-currency-usd"></i>
                                                    <input type="number" step="0.01" min="0.01" class="form-control" id="price" placeholder="السعر" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-archive"></i>
                                                    <input type="number" min="1" class="form-control" id="cases_count" placeholder="عدد الحالات" required>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="icon-input mb-3">
                                                    <i class="mdi mdi-calculator"></i>
                                                    <input type="text" class="form-control" id="item_cost" placeholder="التكلفة" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <button type="button" id="add-drug-btn" class="btn btn-success btn-lg">
                                                <i class="mdi mdi-plus me-2"></i>إضافة إلى القائمة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- قائمة الأدوية المضافة -->
                            <div class="col-lg-8">
                                <div class="section-card list-section">
                                    <div class="section-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 fw-bold"><i class="mdi mdi-format-list-bulleted me-2"></i>قائمة الأدوية المضافة</h6>
                                            <span class="badge bg-white text-dark" id="items-count">0 عنصر</span>
                                        </div>
                                    </div>
                                    <div class="p-3">
                                        <!-- منطقة التحذير من التكرار -->
                                        <div id="duplicate-warning" class="duplicate-warning">
                                            <i class="mdi mdi-alert-circle"></i>
                                            <strong>تحذير:</strong> <span id="duplicate-message"></span>
                                        </div>

                                        <div id="items-list" class="table-responsive">
                                            <table class="table table-hover mb-0">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>الدواء</th>
                                                        <th>التصنيف</th>
                                                        <th>الوحدة</th>
                                                        <th>الكمية</th>
                                                        <th>السعر</th>
                                                        <th>عدد الحالات</th>
                                                        <th>التكلفة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="items-table-body">
                                                    <tr id="no-items-row">
                                                        <td colspan="9" class="text-center text-muted py-5">
                                                            <i class="mdi mdi-package-variant-closed" style="font-size: 3rem; opacity: 0.3;"></i>
                                                            <br>لا توجد أدوية مضافة بعد
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- إجمالي التكلفة -->
                                        <div class="row mt-4">
                                            <div class="col-md-6 offset-md-6">
                                                <div class="total-card">
                                                    <h5 class="mb-0 fw-bold">
                                                        <i class="mdi mdi-cash-multiple me-2"></i>
                                                        إجمالي التكلفة: <span id="total-cost">0.00</span> جنيه
                                                    </h5>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- أزرار الحفظ -->
                                        <div class="text-center mt-4">
                                            <button type="button" id="save-dispensed-btn" class="btn btn-primary btn-lg me-3" disabled>
                                                <i class="mdi mdi-content-save me-2"></i>حفظ المنصرف
                                            </button>
                                            <button type="button" class="btn btn-secondary btn-lg" onclick="clearList()">
                                                <i class="mdi mdi-refresh me-2"></i>مسح القائمة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- حقول مخفية للبيانات -->
                        <input type="hidden" id="items_data" name="items_data" value="">
                        <input type="hidden" id="total_cost" name="total_cost" value="0">
                    </form>
                </div>
            </div>

            <!-- سجل الصرف الأخير -->
            {% if dispensed_items %}
            <div class="history-card">
                <div class="history-header">
                    <h4 class="mb-0 fw-bold">
                        <i class="mdi mdi-history me-3"></i>سجل الصرف الأخير
                    </h4>
                    <button class="btn btn-outline-primary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#historyFilters" aria-expanded="false" aria-controls="historyFilters">
                        <i class="mdi mdi-filter-variant me-1"></i>فلترة السجلات
                    </button>
                </div>

                <!-- فلاتر سجل الصرف -->
                <div class="collapse" id="historyFilters">
                    <div class="card card-body border-0 bg-light">
                        <form method="GET" action="{{ url_for('dispense_new') }}" class="row g-3">
                            <div class="col-md-4">
                                <label for="filter_clinic" class="form-label">
                                    <i class="mdi mdi-hospital-building me-1"></i>العيادة
                                </label>
                                <select class="form-select" id="filter_clinic" name="filter_clinic">
                                    <option value="">جميع العيادات</option>
                                    {% for clinic in clinics %}
                                    <option value="{{ clinic.id }}" {% if request.args.get('filter_clinic') == clinic.id|string %}selected{% endif %}>
                                        {{ clinic.name }} - {{ clinic.area_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="filter_category" class="form-label">
                                    <i class="mdi mdi-tag me-1"></i>التصنيف
                                </label>
                                <select class="form-select" id="filter_category" name="filter_category">
                                    <option value="">جميع التصنيفات</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if request.args.get('filter_category') == category.id|string %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="filter_month" class="form-label">
                                    <i class="mdi mdi-calendar me-1"></i>شهر الصرف
                                </label>
                                <input type="month" class="form-control" id="filter_month" name="filter_month" value="{{ request.args.get('filter_month', '') }}">
                            </div>
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="mdi mdi-magnify me-1"></i>تطبيق الفلترة
                                    </button>
                                    <a href="{{ url_for('dispense_new') }}" class="btn btn-outline-secondary">
                                        <i class="mdi mdi-refresh me-1"></i>إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="p-4">
                    <!-- معلومات الفلترة والنتائج -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge bg-info fs-6">
                                <i class="mdi mdi-database me-1"></i>
                                عدد السجلات: {{ dispensed_items|length }}
                            </span>
                            {% if request.args.get('filter_clinic') or request.args.get('filter_category') or request.args.get('filter_month') %}
                            <span class="badge bg-warning text-dark ms-2">
                                <i class="mdi mdi-filter me-1"></i>
                                مفلتر
                            </span>
                            {% endif %}
                        </div>
                        <div>
                            {% if request.args.get('filter_clinic') or request.args.get('filter_category') or request.args.get('filter_month') %}
                            <small class="text-muted">
                                <i class="mdi mdi-information me-1"></i>
                                النتائج مفلترة حسب:
                                {% if request.args.get('filter_clinic') %}العيادة{% endif %}
                                {% if request.args.get('filter_category') %}{% if request.args.get('filter_clinic') %} + {% endif %}التصنيف{% endif %}
                                {% if request.args.get('filter_month') %}{% if request.args.get('filter_clinic') or request.args.get('filter_category') %} + {% endif %}الشهر{% endif %}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>العيادة</th>
                                    <th>الدواء</th>
                                    <th>التصنيف</th>
                                    <th>الوحدة</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>عدد الحالات</th>
                                    <th>التكلفة</th>
                                    <th>تاريخ الصرف</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in dispensed_items %}
                                <tr {% if not item.can_modify %}class="protected-record"{% endif %}>
                                    <td><span class="badge bg-primary">{{ loop.index }}</span></td>
                                    <td>{{ item.clinic_name }}</td>
                                    <td><strong>{{ item.drug_name }}</strong></td>
                                    <td><span class="badge bg-info">{{ item.category_name }}</span></td>
                                    <td><span class="badge bg-success">{{ item.drug_unit or 'قرص' }}</span></td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ item.price }} ج.م</td>
                                    <td>{{ item.cases_count }}</td>
                                    <td><strong class="text-success">{{ item.total_cost }} ج.م</strong></td>
                                    <td>
                                        {{ item.formatted_date or format_month_year(item.dispense_month) }}
                                        {% if not item.can_modify %}
                                        <br><small class="text-muted">
                                            <i class="mdi mdi-lock"></i> مر عليه {{ item.months_diff }} أشهر
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.can_modify %}
                                            <a href="/dispense/{{ item.id }}/edit" class="btn btn-sm btn-warning me-1" title="تعديل">
                                                <i class="mdi mdi-pencil"></i>
                                            </a>
                                            <form method="POST" action="/dispense/{{ item.id }}/delete" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا السجل؟');">
                                                <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                    <i class="mdi mdi-delete"></i>
                                                </button>
                                            </form>
                                        {% else %}
                                            <span class="text-muted" title="لا يمكن التعديل أو الحذف بعد مرور 4 أشهر">
                                                <i class="mdi mdi-lock"></i> محمي
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // متغيرات عامة
    var itemsList = [];
    var itemCounter = 0;
    var selectedCategoryId = null;
    var selectedCategoryName = null;
    var editingIndex = -1;

    $(document).ready(function() {
        // تعيين التاريخ الحالي كقيمة افتراضية لشهر الصرف
        var today = new Date();
        $('#dispense_month').val(formatMonthInput(today));

        // تحديث قائمة الأدوية عند اختيار التصنيف
        $('#category_id').change(function() {
            var categoryId = $(this).val();
            var categoryName = $(this).find('option:selected').text();
            var drugSelect = $('#drug_id');

            if (categoryId) {
                // حفظ التصنيف المختار
                selectedCategoryId = categoryId;
                selectedCategoryName = categoryName;

                // تعطيل قائمة التصنيف بعد الاختيار
                $(this).prop('disabled', true);

                drugSelect.prop('disabled', false);
                drugSelect.html('<option value="">جاري التحميل...</option>');

                $.ajax({
                    url: '/api/drugs_by_category/' + categoryId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        console.log('تم تحميل الأدوية:', data);
                        drugSelect.html('<option value="">-- اختر الدواء --</option>');

                        if (data && data.drugs && data.drugs.length > 0) {
                            $.each(data.drugs, function(index, drug) {
                                drugSelect.append('<option value="' + drug.id + '" data-unit="' + drug.unit + '">' + drug.name + '</option>');
                            });
                        } else {
                            drugSelect.append('<option value="">لا توجد أدوية في هذا التصنيف</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('خطأ في تحميل الأدوية:', error);
                        console.error('Response:', xhr.responseText);
                        drugSelect.html('<option value="">خطأ في تحميل الأدوية</option>');
                        alert('حدث خطأ في تحميل الأدوية. يرجى المحاولة مرة أخرى.');
                    }
                });
            } else {
                selectedCategoryId = null;
                selectedCategoryName = null;
                drugSelect.prop('disabled', true);
                drugSelect.html('<option value="">-- اختر التصنيف أولاً --</option>');
            }
        });

        // تحديث الوحدة عند اختيار الدواء
        $('#drug_id').change(function() {
            var selectedOption = $(this).find('option:selected');
            var unit = selectedOption.data('unit') || '';
            $('#drug_unit').val(unit);
        });

        // حساب التكلفة عند تغيير الكمية أو السعر
        $('#quantity, #price').on('input', function() {
            calculateItemCost();
        });

        // إضافة عنصر إلى القائمة
        $('#add-drug-btn').on('click', function() {
            addItemToList();
        });

        // حفظ المنصرف
        $('#save-dispensed-btn').on('click', function() {
            saveDispensed();
        });

        // حذف عنصر من القائمة
        $(document).on('click', '.remove-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var index = $(this).data('index');
            console.log('حذف العنصر:', index);
            removeItemFromList(index);
        });

        // تعديل عنصر في القائمة
        $(document).on('click', '.edit-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var index = $(this).data('index');
            console.log('تعديل العنصر:', index);
            editItemInList(index);
        });
    });

    // دالة لحساب تكلفة العنصر
    function calculateItemCost() {
        var quantity = parseFloat($('#quantity').val()) || 0;
        var price = parseFloat($('#price').val()) || 0;
        var cost = quantity * price;
        $('#item_cost').val(cost.toFixed(2));
    }

    // دالة للتحقق من التكرار
    function checkForDuplicates(newItem) {
        console.log('🔍 بدء التحقق من التكرار...');
        var clinicId = $('#clinic_id').val(); // استخدام الحقل الصحيح
        var dispenseMonth = $('#dispense_month').val();

        console.log('بيانات التحقق:', {
            clinicId: clinicId,
            dispenseMonth: dispenseMonth,
            drugId: newItem.drug_id,
            quantity: newItem.quantity,
            price: newItem.price
        });

        if (!clinicId || !dispenseMonth) {
            console.log('❌ بيانات ناقصة - تخطي التحقق');
            return Promise.resolve({ is_duplicate: false });
        }

        console.log('📡 إرسال طلب التحقق...');
        return $.ajax({
            url: '/api/check_duplicate',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                drug_id: newItem.drug_id,
                clinic_id: clinicId,
                dispense_month: dispenseMonth,
                quantity: newItem.quantity,
                price: newItem.price
            })
        }).done(function(response) {
            console.log('✅ استجابة التحقق:', response);
        }).fail(function(error) {
            console.error('❌ خطأ في التحقق:', error);
        });
    }

    // دالة لعرض تحذير التكرار
    function showDuplicateWarning(drugName) {
        console.log('🚨 عرض تحذير التكرار للدواء:', drugName);
        var warningDiv = $('#duplicate-warning');
        var messageSpan = $('#duplicate-message');

        console.log('عناصر التحذير:', {
            warningDiv: warningDiv.length,
            messageSpan: messageSpan.length
        });

        messageSpan.text(`الدواء "${drugName}" موجود بالفعل بنفس الكمية والسعر في هذه العيادة والشهر. سيتم تخطيه عند الحفظ.`);
        warningDiv.show();
        console.log('✅ تم عرض التحذير');

        // تمرير إلى التحذير
        $('html, body').animate({
            scrollTop: warningDiv.offset().top - 100
        }, 500);
    }

    // دالة لإخفاء تحذير التكرار
    function hideDuplicateWarning() {
        console.log('🔇 إخفاء تحذير التكرار');
        $('#duplicate-warning').hide();
    }

    // دالة للتحقق من وجود عناصر مكررة وإخفاء التحذير إذا لزم الأمر
    function checkAndHideDuplicateWarning() {
        var hasDuplicates = itemsList.some(function(item) {
            return item.is_duplicate === true;
        });

        console.log('🔍 فحص العناصر المكررة:', hasDuplicates);

        if (!hasDuplicates) {
            hideDuplicateWarning();
        }
    }

    // دالة لإضافة عنصر إلى القائمة
    function addItemToList() {
        console.log('بدء إضافة عنصر إلى القائمة');

        var drugId = $('#drug_id').val();
        var drugName = $('#drug_id option:selected').text();
        var drugUnit = $('#drug_unit').val();
        var quantity = parseFloat($('#quantity').val());
        var price = parseFloat($('#price').val());
        var casesCount = parseInt($('#cases_count').val());

        console.log('البيانات المدخلة:', {
            selectedCategoryId: selectedCategoryId,
            selectedCategoryName: selectedCategoryName,
            drugId: drugId,
            drugName: drugName,
            quantity: quantity,
            price: price,
            casesCount: casesCount
        });

        // التحقق من البيانات مع رسائل مفصلة
        if (!selectedCategoryId) {
            alert('يرجى اختيار التصنيف أولاً');
            return;
        }

        if (!drugId) {
            alert('يرجى اختيار الدواء');
            return;
        }

        if (!quantity || quantity <= 0) {
            alert('يرجى إدخال كمية صحيحة');
            $('#quantity').focus();
            return;
        }

        if (!price || price <= 0) {
            alert('يرجى إدخال سعر صحيح');
            $('#price').focus();
            return;
        }

        if (!casesCount || casesCount <= 0) {
            alert('يرجى إدخال عدد علب صحيح');
            $('#cases_count').focus();
            return;
        }

        var cost = quantity * price;

        // إضافة أو تحديث العنصر
        var item = {
            drug_id: drugId,
            drug_name: drugName,
            drug_unit: drugUnit,
            category_id: selectedCategoryId,
            category_name: selectedCategoryName,
            quantity: quantity,
            price: price,
            cases_count: casesCount,
            cost: cost
        };

        console.log('العنصر المراد إضافته:', item);

        // التحقق من التكرار قبل الإضافة (فقط للعناصر الجديدة)
        if (editingIndex < 0) {
            console.log('🔄 بدء عملية التحقق من التكرار...');
            checkForDuplicates(item).then(function(response) {
                console.log('📋 نتيجة التحقق:', response);
                if (response.is_duplicate) {
                    console.log('⚠️ تم اكتشاف تكرار!');
                    // عرض تحذير التكرار
                    showDuplicateWarning(response.drug_name || item.drug_name);
                    item.is_duplicate = true;
                } else {
                    console.log('✅ لا يوجد تكرار');
                    hideDuplicateWarning();
                    item.is_duplicate = false;
                }

                // إضافة العنصر
                console.log('إضافة عنصر جديد مع حالة التكرار:', item.is_duplicate);
                itemsList.push(item);

                console.log('قائمة العناصر الحالية:', itemsList);
                updateItemsTable();
                updateSaveButton(); // تحديث زر الحفظ بعد الإضافة
                clearDrugForm();
            }).catch(function(error) {
                console.error('❌ خطأ في التحقق من التكرار:', error);
                // إضافة العنصر حتى لو فشل التحقق
                item.is_duplicate = false;
                itemsList.push(item);
                updateItemsTable();
                updateSaveButton(); // تحديث زر الحفظ بعد الإضافة
                clearDrugForm();
            });
        } else {
            // تحديث عنصر موجود
            console.log('تحديث عنصر موجود في الفهرس:', editingIndex);
            itemsList[editingIndex] = item;
            editingIndex = -1;
            $('#add-drug-btn').html('<i class="mdi mdi-plus me-2"></i>إضافة إلى القائمة');

            console.log('قائمة العناصر الحالية:', itemsList);
            updateItemsTable();
            updateSaveButton(); // تحديث زر الحفظ بعد التحديث
            clearDrugForm();
        }

        // إظهار رسالة نجاح
        var successMsg = editingIndex >= 0 ? 'تم تحديث العنصر بنجاح' : 'تم إضافة العنصر بنجاح';
        console.log(successMsg);
    }

    // دالة لحذف عنصر من القائمة
    function removeItemFromList(index) {
        if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
            itemsList.splice(index, 1);
            updateItemsTable();
            updateSaveButton();

            // التحقق من وجود عناصر مكررة متبقية
            checkAndHideDuplicateWarning();

            // إلغاء وضع التعديل إذا كان العنصر المحذوف قيد التعديل
            if (editingIndex === index) {
                cancelEdit();
            } else if (editingIndex > index) {
                editingIndex--;
            }
        }
    }

    // دالة لتعديل عنصر في القائمة
    function editItemInList(index) {
        console.log('تعديل العنصر:', index, itemsList[index]);
        var item = itemsList[index];

        // البحث عن التصنيف المناسب وتحديده
        var drugSelect = $('#drug_id');
        var categorySelect = $('#category_id');

        // البحث عن التصنيف من خلال الدواء
        var drugOption = drugSelect.find('option[value="' + item.drug_id + '"]');
        if (drugOption.length === 0) {
            // إذا لم يكن الدواء موجود في القائمة الحالية، نحتاج لتحميل التصنيف أولاً
            // البحث عن التصنيف من البيانات المحفوظة
            if (item.category_id) {
                categorySelect.val(item.category_id);
                selectedCategoryId = item.category_id;
                selectedCategoryName = item.category_name;

                // تحميل الأدوية للتصنيف المحدد
                $.ajax({
                    url: '/api/drugs_by_category/' + item.category_id,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        drugSelect.html('<option value="">-- اختر الدواء --</option>');
                        if (data && data.drugs && data.drugs.length > 0) {
                            $.each(data.drugs, function(index, drug) {
                                drugSelect.append('<option value="' + drug.id + '">' + drug.name + '</option>');
                            });
                        }
                        drugSelect.prop('disabled', false);

                        // تحديد الدواء بعد تحميل القائمة
                        drugSelect.val(item.drug_id);
                    },
                    error: function() {
                        console.error('خطأ في تحميل الأدوية للتعديل');
                        alert('حدث خطأ في تحميل بيانات التعديل');
                    }
                });
            }
        } else {
            // الدواء موجود في القائمة الحالية
            drugSelect.val(item.drug_id);
        }

        // ملء باقي الحقول
        $('#drug_unit').val(item.drug_unit || '');
        $('#quantity').val(item.quantity);
        $('#price').val(item.price);
        $('#cases_count').val(item.cases_count);
        $('#item_cost').val(item.cost.toFixed(2));

        // تعيين وضع التعديل
        editingIndex = index;
        $('#add-drug-btn').html('<i class="mdi mdi-check me-2"></i>تحديث العنصر');

        // إضافة زر إلغاء
        if ($('#cancel-edit-btn').length === 0) {
            $('#add-drug-btn').after('<button type="button" id="cancel-edit-btn" class="btn btn-secondary ms-2" onclick="cancelEdit()"><i class="mdi mdi-close me-2"></i>إلغاء</button>');
        }

        // تمرير إلى أعلى النموذج
        $('html, body').animate({
            scrollTop: $('#category_id').offset().top - 100
        }, 500);
    }

    // دالة لإلغاء التعديل
    function cancelEdit() {
        editingIndex = -1;
        $('#add-drug-btn').html('<i class="mdi mdi-plus me-2"></i>إضافة إلى القائمة');
        $('#cancel-edit-btn').remove();
        clearDrugForm();
    }

    // دالة لتحديث جدول العناصر
    function updateItemsTable() {
        var tbody = $('#items-table-body');
        tbody.empty();

        if (itemsList.length === 0) {
            tbody.append('<tr id="no-items-row"><td colspan="8" class="text-center text-muted py-5"><i class="mdi mdi-package-variant-closed" style="font-size: 3rem; opacity: 0.3;"></i><br>لا توجد أدوية مضافة بعد</td></tr>');
            $('#total-cost').text('0.00');
            $('#items-count').text('0 عنصر');
        } else {
            var totalCost = 0;
            $.each(itemsList, function(index, item) {
                totalCost += item.cost;
                var editingClass = (editingIndex === index) ? ' table-warning' : '';
                var duplicateClass = item.is_duplicate ? ' duplicate-item' : '';
                var combinedClass = editingClass + duplicateClass;

                // التأكد من وجود اسم التصنيف
                var categoryName = item.category_name || 'غير محدد';

                // إضافة أيقونة تحذير للعناصر المكررة
                var duplicateIcon = item.is_duplicate ? '<i class="mdi mdi-alert-circle duplicate-icon" title="عنصر مكرر - سيتم تخطيه"></i>' : '';

                var row = '<tr class="' + combinedClass + '">' +
                    '<td><span class="badge bg-primary">' + (index + 1) + '</span></td>' +
                    '<td><strong>' + item.drug_name + '</strong>' + duplicateIcon + '</td>' +
                    '<td><span class="badge bg-info">' + categoryName + '</span></td>' +
                    '<td><span class="badge bg-success">' + (item.drug_unit || 'قرص') + '</span></td>' +
                    '<td>' + item.quantity + '</td>' +
                    '<td>' + item.price + ' ج.م</td>' +
                    '<td>' + item.cases_count + '</td>' +
                    '<td><strong class="text-success">' + item.cost.toFixed(2) + ' ج.م</strong></td>' +
                    '<td>' +
                        '<button type="button" class="btn btn-sm btn-warning me-1 edit-item" data-index="' + index + '" title="تعديل"><i class="mdi mdi-pencil"></i></button>' +
                        '<button type="button" class="btn btn-sm btn-danger remove-item" data-index="' + index + '" title="حذف"><i class="mdi mdi-delete"></i></button>' +
                    '</td>' +
                    '</tr>';
                tbody.append(row);
            });
            $('#total-cost').text(totalCost.toFixed(2));
            $('#items-count').text(itemsList.length + ' عنصر');
        }
    }

    // دالة لمسح حقول الدواء فقط (بدون التصنيف)
    function clearDrugForm() {
        $('#drug_id').val('');
        $('#drug_unit').val('');
        $('#quantity').val('');
        $('#price').val('');
        $('#cases_count').val('');
        $('#item_cost').val('');
    }

    // دالة لمسح النموذج بالكامل
    function clearForm() {
        $('#category_id').val('').prop('disabled', false);
        $('#drug_id').html('<option value="">-- اختر التصنيف أولاً --</option>').prop('disabled', true);
        $('#drug_unit').val('');
        $('#quantity').val('');
        $('#price').val('');
        $('#cases_count').val('');
        $('#item_cost').val('');
        selectedCategoryId = null;
        selectedCategoryName = null;
    }

    // دالة لمسح القائمة
    function clearList() {
        if (itemsList.length > 0 && confirm('هل أنت متأكد من مسح جميع العناصر؟')) {
            itemsList = [];
            updateItemsTable();
            updateSaveButton();
            cancelEdit();
            clearForm(); // مسح النموذج بالكامل وإعادة تفعيل التصنيف
            hideDuplicateWarning(); // إخفاء تحذير التكرار
        }
    }

    // دالة لتحديث زر الحفظ
    function updateSaveButton() {
        var saveBtn = $('#save-dispensed-btn');
        console.log('🔄 تحديث زر الحفظ - عدد العناصر:', itemsList.length);

        if (itemsList.length > 0) {
            console.log('✅ تنشيط زر الحفظ');
            saveBtn.prop('disabled', false);
            saveBtn.removeClass('btn-secondary').addClass('btn-primary');
        } else {
            console.log('❌ تعطيل زر الحفظ');
            saveBtn.prop('disabled', true);
            saveBtn.removeClass('btn-primary').addClass('btn-secondary');
        }
    }

    // دالة لحفظ المنصرف
    function saveDispensed() {
        var clinicId = $('#clinic_id').val();
        var dispenseMonth = $('#dispense_month').val();

        if (!clinicId || !dispenseMonth) {
            alert('يرجى اختيار العيادة وشهر الصرف');
            return;
        }

        if (itemsList.length === 0) {
            alert('يرجى إضافة عنصر واحد على الأقل إلى القائمة');
            return;
        }

        // تحديث البيانات المخفية
        $('#items_data').val(JSON.stringify(itemsList));
        var totalCost = itemsList.reduce((sum, item) => sum + item.cost, 0);
        $('#total_cost').val(totalCost.toFixed(2));

        // عرض مؤشر التحميل
        var saveBtn = $('#save-dispensed-btn');
        var originalText = saveBtn.html();
        saveBtn.html('<i class="mdi mdi-loading mdi_spin me-1"></i>جاري الحفظ...');
        saveBtn.prop('disabled', true);

        console.log('إرسال البيانات:', {
            clinic_id: clinicId,
            dispense_month: dispenseMonth,
            items_count: itemsList.length,
            total_cost: totalCost.toFixed(2)
        });

        // إرسال البيانات
        $.ajax({
            url: '/dispense/new',
            type: 'POST',
            data: {
                clinic_id: clinicId,
                dispense_month: dispenseMonth,
                items_data: JSON.stringify(itemsList),
                total_cost: totalCost.toFixed(2)
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('تم حفظ البيانات بنجاح:', response);

                // عرض رسالة مناسبة حسب النتيجة
                if (response.message) {
                    alert(response.message);
                } else if (response.added_count > 0 && response.skipped_count > 0) {
                    alert(`تم حفظ ${response.added_count} عنصر بنجاح وتم تخطي ${response.skipped_count} عنصر مكرر`);
                } else if (response.added_count > 0) {
                    alert(`تم حفظ ${response.added_count} عنصر بنجاح`);
                } else if (response.skipped_count > 0) {
                    alert(`تم تخطي ${response.skipped_count} عنصر مكرر - لم يتم حفظ أي عنصر جديد`);
                } else {
                    alert('تم حفظ منصرف الأدوية بنجاح');
                }

                // مسح القائمة وإعادة تفعيل التصنيف
                itemsList = [];
                updateItemsTable();
                updateSaveButton();
                cancelEdit();
                clearForm();
                hideDuplicateWarning(); // إخفاء تحذير التكرار
                location.reload();
            },
            error: function(xhr, status, error) {
                console.error('خطأ في حفظ البيانات:', error);
                console.error('Response:', xhr.responseText);
                console.error('Status:', status);

                var errorMessage = 'حدث خطأ أثناء حفظ البيانات';
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.error) {
                        errorMessage += ': ' + response.error;
                    }
                } catch (e) {
                    errorMessage += ': ' + error;
                }

                alert(errorMessage);
                saveBtn.html(originalText);
                saveBtn.prop('disabled', false);
            }
        });
    }
</script>
{% endblock %}
