{% extends "base.html" %}

{% block title %}تقرير التكلفة{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- أزرار الطباعة والتصدير في الأعلى -->
    <div class="row mb-3 no-print">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left me-1"></i>العودة للتقارير
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="exportToExcel()" title="تصدير إلى Excel">
                        <i class="mdi mdi-file-excel me-1"></i>تصدير Excel
                    </button>
                    <button class="btn btn-info" onclick="showPrintOptions('pdf')" title="حفظ كـ PDF">
                        <i class="mdi mdi-file-pdf me-1"></i>حفظ PDF
                    </button>
                    <button class="btn btn-primary" onclick="showPrintOptions('print')" title="طباعة التقرير">
                        <i class="mdi mdi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <!-- الشعار -->
            <div class="text-center mb-4">
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="الهيئة العامة للتأمين الصحي" class="report-logo">
            </div>

            <!-- رأس التقرير -->
            <div class="card shadow-sm mb-4">
                <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="mdi mdi-calculator me-2"></i>
                                تقرير التكلفة المالي - {{ scope_name }}
                            </h4>
                            <small class="text-light">السنة المالية: {{ year }}</small>
                        </div>
                        <div class="col-md-4 text-end no-print">
                            <span class="badge bg-light text-dark">
                                <i class="mdi mdi-calendar-range me-1"></i>{{ year }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="mdi mdi-information me-1"></i>معلومات التقرير:</h6>
                            <ul class="list-unstyled small">
                                <li><strong>النطاق:</strong> {{ scope_name }}</li>
                                <li><strong>السنة:</strong> {{ year }}</li>
                                <li><strong>نوع التحليل:</strong>
                                    {% if analysis_type == 'summary' %}تقرير إجمالي
                                    {% elif analysis_type == 'comparison' %}مقارنة بين الفروع
                                    {% elif analysis_type == 'detailed' %}تحليل مفصل
                                    {% endif %}
                                </li>
                                <li><strong>وحدة القياس:</strong> جنيه مصري (ج.م)</li>
                            </ul>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="alert alert-warning mb-0">
                                <h5 class="mb-0">
                                    <i class="mdi mdi-cash-multiple me-2"></i>
                                    إجمالي تكلفة الأدوية المنصرفة: {{ "{:,.2f}".format(total_cost) }} ج.م
                                </h5>
                            </div>
                        </div>
                    </div>

                    <!-- شرح توضيحي -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="mdi mdi-lightbulb me-1"></i>شرح التقرير:</h6>
                                <p class="mb-1">
                                    <strong>📊 هذا التقرير يعرض:</strong> إجمالي تكلفة الأدوية المنصرفة من جميع العيادات
                                    {% if analysis_type == 'summary' %}
                                    موزعة على الشهور خلال السنة المالية {{ year }}
                                    {% elif analysis_type == 'comparison' %}
                                    مقسمة حسب الفروع والشهور لمقارنة الأداء المالي
                                    {% elif analysis_type == 'detailed' %}
                                    مفصلة حسب المناطق داخل {{ scope_name }} لكل شهر
                                    {% endif %}
                                </p>
                                <p class="mb-0 small">
                                    <strong>💡 ملاحظة:</strong> التكلفة محسوبة على أساس: (الكمية المنصرفة × سعر الوحدة) لجميع الأدوية
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- محتوى التقرير -->
            {% if analysis_type == 'summary' %}
                <!-- التقرير الإجمالي -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="mdi mdi-chart-line me-2"></i>
                            التكلفة الشهرية لجميع الفروع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead style="background: linear-gradient(135deg, #2c3e50, #34495e); color: white;">
                                    <tr>
                                        <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #3498db;">
                                            <i class="mdi mdi-calendar me-2" style="color: #f39c12;"></i>
                                            <span>الشهر</span>
                                        </th>
                                        <th class="text-end" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e74c3c;">
                                            <i class="mdi mdi-cash-multiple me-2" style="color: #f1c40f;"></i>
                                            <span>تكلفة الأدوية المنصرفة</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                        </th>
                                        <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60;">
                                            <i class="mdi mdi-percent me-2" style="color: #2ecc71;"></i>
                                            <span>النسبة المئوية</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(من الإجمالي السنوي)</small>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for month_data in months_data %}
                                    <tr>
                                        <td>
                                            <i class="mdi mdi-calendar me-1"></i>
                                            {{ month_data.month }}
                                        </td>
                                        <td class="text-end">
                                            <strong>{{ "{:,.2f}".format(month_data.cost) }}</strong>
                                        </td>
                                        <td class="text-center">
                                            {% if total_cost > 0 %}
                                                <span class="badge bg-primary">
                                                    {{ "{:.1f}%".format((month_data.cost / total_cost) * 100) }}
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary">0%</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot class="table-warning">
                                    <tr>
                                        <th><i class="mdi mdi-sigma me-1"></i>إجمالي السنة المالية {{ year }}</th>
                                        <th class="text-end"><strong>{{ "{:,.2f}".format(total_cost) }} ج.م</strong></th>
                                        <th class="text-center"><span class="badge bg-success">100%</span></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

            {% elif analysis_type == 'comparison' %}
                <!-- مقارنة بين الفروع -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="mdi mdi-compare me-2"></i>
                            مقارنة التكلفة بين الفروع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead style="background: linear-gradient(135deg, #8e44ad, #9b59b6); color: white;">
                                    <tr>
                                        <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e67e22; min-width: 150px;">
                                            <i class="mdi mdi-office-building me-2" style="color: #f39c12;"></i>
                                            <span>اسم الفرع</span>
                                        </th>
                                        {% for month_num, month_name in arabic_months.items() %}
                                        <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 13px; border-bottom: 3px solid #3498db; min-width: 100px;">
                                            <i class="mdi mdi-calendar me-1" style="color: #2ecc71;"></i>
                                            <span>{{ month_name }}</span><br>
                                            <small style="color: #ecf0f1; font-size: 10px;">(ج.م)</small>
                                        </th>
                                        {% endfor %}
                                        <th class="text-end" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #e74c3c; min-width: 120px;">
                                            <i class="mdi mdi-calculator me-2" style="color: #f1c40f;"></i>
                                            <span>إجمالي السنة</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for branch_name, branch_months in branches_data.items() %}
                                    <tr>
                                        <td>
                                            <i class="mdi mdi-office-building me-1"></i>
                                            {{ branch_name }}
                                        </td>
                                        {% set branch_total = 0 %}
                                        {% for month_num, month_name in arabic_months.items() %}
                                        <td class="text-center">
                                            {% set month_cost = branch_months.get(month_num, 0) %}
                                            {% set branch_total = branch_total + month_cost %}
                                            {{ "{:,.0f}".format(month_cost) if month_cost > 0 else '-' }}
                                        </td>
                                        {% endfor %}
                                        <td class="text-end">
                                            <strong>{{ "{:,.2f}".format(branch_total) }}</strong>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            {% elif analysis_type == 'detailed' %}
                <!-- التحليل المفصل -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="mdi mdi-magnify me-2"></i>
                            التحليل المفصل - {{ scope_name }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">
                                    <tr>
                                        <th style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #f39c12; min-width: 150px;">
                                            <i class="mdi mdi-map-marker me-2" style="color: #f39c12;"></i>
                                            <span>اسم المنطقة</span>
                                        </th>
                                        {% for month_num, month_name in arabic_months.items() %}
                                        <th class="text-center" style="padding: 15px; font-weight: bold; font-size: 13px; border-bottom: 3px solid #3498db; min-width: 100px;">
                                            <i class="mdi mdi-calendar me-1" style="color: #2ecc71;"></i>
                                            <span>{{ month_name }}</span><br>
                                            <small style="color: #ecf0f1; font-size: 10px;">(ج.م)</small>
                                        </th>
                                        {% endfor %}
                                        <th class="text-end" style="padding: 15px; font-weight: bold; font-size: 14px; border-bottom: 3px solid #27ae60; min-width: 120px;">
                                            <i class="mdi mdi-calculator me-2" style="color: #f1c40f;"></i>
                                            <span>إجمالي السنة</span><br>
                                            <small style="color: #ecf0f1; font-size: 11px;">(بالجنيه المصري)</small>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for area_name, area_months in areas_data.items() %}
                                    <tr>
                                        <td>
                                            <i class="mdi mdi-map-marker me-1"></i>
                                            {{ area_name }}
                                        </td>
                                        {% set area_total = 0 %}
                                        {% for month_num, month_name in arabic_months.items() %}
                                        <td class="text-center">
                                            {% set month_cost = area_months.get(month_num, 0) %}
                                            {% set area_total = area_total + month_cost %}
                                            {{ "{:,.0f}".format(month_cost) if month_cost > 0 else '-' }}
                                        </td>
                                        {% endfor %}
                                        <td class="text-end">
                                            <strong>{{ "{:,.2f}".format(area_total) }}</strong>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- ملاحظات مهمة -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="mdi mdi-information me-2"></i>
                        ملاحظات مهمة حول التقرير
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="mdi mdi-calculator me-1"></i>طريقة حساب التكلفة:</h6>
                            <ul class="small">
                                <li><strong>التكلفة الشهرية:</strong> مجموع (الكمية × السعر) لجميع الأدوية المنصرفة في الشهر</li>
                                <li><strong>التكلفة السنوية:</strong> مجموع التكلفة الشهرية لجميع شهور السنة</li>
                                <li><strong>النسبة المئوية:</strong> (التكلفة الشهرية ÷ التكلفة السنوية) × 100</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="mdi mdi-chart-line me-1"></i>تفسير البيانات:</h6>
                            <ul class="small">
                                <li><strong>الأرقام تشمل:</strong> جميع أنواع الأدوية المنصرفة من العيادات</li>
                                <li><strong>العملة:</strong> جنيه مصري (ج.م)</li>
                                <li><strong>الفترة:</strong> من يناير إلى ديسمبر {{ year }}</li>
                                <li><strong>المصدر:</strong> سجلات المنصرف الإلكترونية</li>
                            </ul>
                        </div>
                    </div>

                    {% if analysis_type == 'summary' %}
                    <div class="alert alert-info mt-3 mb-0">
                        <strong><i class="mdi mdi-lightbulb me-1"></i>تفسير التقرير الإجمالي:</strong>
                        يعرض هذا التقرير إجمالي تكلفة الأدوية المنصرفة من جميع الفروع والعيادات موزعة على شهور السنة،
                        مما يساعد في فهم التوزيع الزمني للإنفاق على الأدوية وتحديد الشهور ذات الاستهلاك الأعلى.
                    </div>
                    {% elif analysis_type == 'comparison' %}
                    <div class="alert alert-info mt-3 mb-0">
                        <strong><i class="mdi mdi-lightbulb me-1"></i>تفسير تقرير المقارنة:</strong>
                        يعرض هذا التقرير مقارنة بين الفروع من حيث تكلفة الأدوية المنصرفة شهرياً،
                        مما يساعد في تحديد الفروع ذات الاستهلاك الأعلى ومقارنة الأداء المالي بين الفروع المختلفة.
                    </div>
                    {% elif analysis_type == 'detailed' %}
                    <div class="alert alert-info mt-3 mb-0">
                        <strong><i class="mdi mdi-lightbulb me-1"></i>تفسير التقرير المفصل:</strong>
                        يعرض هذا التقرير تفصيل تكلفة الأدوية المنصرفة حسب المناطق داخل {{ scope_name }} موزعة على شهور السنة،
                        مما يساعد في فهم توزيع الإنفاق على مستوى المناطق وتحديد المناطق ذات الاستهلاك الأعلى.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تضمين تذييل حقوق الطبع والنشر -->
{% include 'includes/copyright_footer.html' %}

<!-- مربع حوار خيارات الطباعة -->
<div id="printOptionsModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خيارات الطباعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="form-group mb-3">
                    <label class="form-label">اتجاه الصفحة:</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationLandscape" value="landscape" checked>
                        <label class="form-check-label" for="orientationLandscape">أفقي (Landscape)</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="pageOrientation" id="orientationPortrait" value="portrait">
                        <label class="form-check-label" for="orientationPortrait">رأسي (Portrait)</label>
                    </div>
                </div>
                <div class="form-group mb-3">
                    <label class="form-label">حجم الخط:</label>
                    <select class="form-select" id="fontSize">
                        <option value="small">صغير</option>
                        <option value="medium" selected>متوسط</option>
                        <option value="large">كبير</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmPrint">طباعة</button>
            </div>
        </div>
    </div>
</div>

<style>
    .report-logo {
        max-width: 150px;
        height: auto;
        margin: 0 auto 15px;
        display: block;
    }

    @media print {
        .btn, .no-print {
            display: none !important;
        }

        .card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
            break-inside: avoid;
        }

        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }

        .table {
            font-size: 12px;
        }

        .container-fluid {
            padding: 0;
        }

        /* إضافة اللوجو في بداية صفحة الطباعة */
        body::before {
            content: '';
            display: block;
            background-image: url("{{ url_for('static', filename='images/logo.png') }}");
            background-repeat: no-repeat;
            background-position: center top;
            background-size: 150px auto;
            height: 100px;
            margin-bottom: 20px;
        }

        /* إضافة حقوق الملكية في نهاية صفحة الطباعة */
        body::after {
            content: 'جميع الحقوق محفوظة لـ ك/أحمد علي أحمد (أحمد كوكب) © {{ current_year }}';
            display: block;
            text-align: center;
            font-size: 10px;
            color: #666;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
    }
</style>

<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
    var printAction = 'print';
    var printModal;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل صفحة تقرير التكلفة بنجاح');
    });

    function showPrintOptions(action) {
        printAction = action;
        if (!printModal) {
            printModal = new bootstrap.Modal(document.getElementById('printOptionsModal'));
            document.getElementById('confirmPrint').addEventListener('click', function() {
                printModal.hide();
                performPrint();
            });
        }
        printModal.show();
    }

    function performPrint() {
        try {
            var orientation = document.querySelector('input[name="pageOrientation"]:checked').value;
            var fontSize = document.getElementById('fontSize').value;

            var styleElement = document.createElement('style');
            styleElement.id = 'print-orientation-style';
            styleElement.innerHTML = '@page { size: ' + orientation + '; }';

            var fontSizeMap = {
                'small': '9pt',
                'medium': '11pt',
                'large': '13pt'
            };
            styleElement.innerHTML += '.table { font-size: ' + fontSizeMap[fontSize] + '; }';

            document.head.appendChild(styleElement);
            window.print();

            setTimeout(function() {
                document.head.removeChild(styleElement);
            }, 1000);
        } catch (error) {
            console.error("Error printing:", error);
            alert("حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.");
        }
    }

    function exportToExcel() {
        var table = document.querySelector('.table');
        if (!table) return;

        var data = [];
        var headers = [];
        table.querySelectorAll('thead th').forEach(function(th) {
            headers.push(th.innerText.replace(/\n/g, ' ').trim());
        });
        data.push(headers);

        table.querySelectorAll('tbody tr').forEach(function(tr) {
            var row = [];
            tr.querySelectorAll('td').forEach(function(td) {
                row.push(td.innerText.replace(/\n/g, ' ').trim());
            });
            data.push(row);
        });

        var footerRow = [];
        table.querySelectorAll('tfoot tr').forEach(function(tr) {
            tr.querySelectorAll('th, td').forEach(function(cell) {
                footerRow.push(cell.innerText.replace(/\n/g, ' ').trim());
            });
        });
        if (footerRow.length > 0) {
            data.push(footerRow);
        }

        var ws = XLSX.utils.aoa_to_sheet(data);
        var wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "تقرير التكلفة");

        var fileName = "تقرير_التكلفة_" + new Date().toISOString().slice(0, 10) + ".xlsx";
        XLSX.writeFile(wb, fileName);
    }
</script>
{% endblock %}
